import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Link } from 'react-router-dom'
import MainServicesBentoGrid from '../components/ui/main-services-bento-grid'
import { Code, Smartphone, Palette, ArrowRight, CheckCircle, Users, Star, Shield, Rocket } from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const Services = () => {
  const statsRef = useRef(null)

  useEffect(() => {
    // Stats counter animation
    gsap.fromTo('.stat-counter',
      { textContent: 0 },
      {
        textContent: (i, target) => target.getAttribute('data-value'),
        duration: 2,
        ease: 'power2.out',
        snap: { textContent: 1 },
        scrollTrigger: {
          trigger: statsRef.current,
          start: 'top 80%',
        }
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const mainServices = [
    {
      icon: Code,
      title: 'Full-Stack Development',
      subtitle: 'Enterprise Web Solutions',
      description: 'Build scalable, high-performance web applications with modern architectures that drive business growth and deliver exceptional user experiences.',
      features: [
        'Custom Web Applications',
        'E-commerce Platforms',
        'Enterprise Portals',
        'Progressive Web Apps',
        'API Development & Integration',
        'Cloud-Native Architecture'
      ],
      technologies: ['React', 'Next.js', 'Node.js', 'TypeScript', 'AWS', 'PostgreSQL'],
      color: 'from-blue-500 to-cyan-500',
      price: 'Starting at $5,000',
      deliveryTime: '6-12 weeks',
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop',
      metrics: { projects: '150+', satisfaction: '98%', performance: '40% faster' },
      badge: 'Most Popular'
    },
    {
      icon: Smartphone,
      title: 'Mobile App Development',
      subtitle: 'iOS & Android Solutions',
      description: 'Native and cross-platform mobile applications that provide seamless experiences across all devices with enterprise-grade security.',
      features: [
        'Native iOS & Android Apps',
        'Cross-Platform Development',
        'App Store Optimization',
        'Real-time Synchronization',
        'Offline-First Architecture',
        'Advanced Analytics Integration'
      ],
      technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin', 'Firebase', 'GraphQL'],
      color: 'from-purple-500 to-pink-500',
      price: 'Starting at $8,000',
      deliveryTime: '8-16 weeks',
      image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop',
      metrics: { projects: '75+', satisfaction: '96%', performance: '60% faster' },
      badge: 'Enterprise Ready'
    },
    {
      icon: Palette,
      title: 'Product Design & Strategy',
      subtitle: 'UI/UX & Digital Experience',
      description: 'User-centered design solutions that combine beautiful aesthetics with data-driven insights to maximize conversions and user engagement.',
      features: [
        'User Experience Research',
        'Design System Creation',
        'Prototyping & User Testing',
        'Conversion Rate Optimization',
        'Brand Identity & Guidelines',
        'Accessibility Compliance'
      ],
      technologies: ['Figma', 'Adobe Creative Suite', 'Principle', 'Framer', 'Miro', 'Hotjar'],
      color: 'from-green-500 to-teal-500',
      price: 'Starting at $3,500',
      deliveryTime: '4-8 weeks',
      image: 'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=600&h=400&fit=crop',
      metrics: { projects: '200+', satisfaction: '99%', performance: '85% increase' },
      badge: 'Design Excellence'
    }
  ]



  const stats = [
    { number: 150, label: 'Projects Delivered', suffix: '+' },
    { number: 50, label: 'Happy Clients', suffix: '+' },
    { number: 99, label: 'Success Rate', suffix: '%' },
    { number: 24, label: 'Support Available', suffix: '/7' }
  ]

  const industries = [
    { name: 'E-commerce', projects: '45+', icon: '🛒' },
    { name: 'Healthcare', projects: '25+', icon: '🏥' },
    { name: 'Fintech', projects: '30+', icon: '💳' },
    { name: 'Education', projects: '20+', icon: '🎓' },
    { name: 'Real Estate', projects: '15+', icon: '🏠' },
    { name: 'SaaS', projects: '35+', icon: '💻' }
  ]

  const testimonials = [
    {
      quote: "Delta Xero ne hamara e-commerce platform transform kar diya, result mein 300% conversion increase hua. Unka approach bilkul professional tha.",
      author: "Rajesh Kumar",
      position: "CEO, RetailPro India",
      rating: 5
    },
    {
      quote: "Mobile app development mein unka expertise outstanding hai. iOS aur Android dono platforms par successful launch karne mein unhone help ki.",
      author: "Deepika Sharma",
      position: "CTO, TechStart Mumbai",
      rating: 5
    },
    {
      quote: "UI/UX redesign ke baad hamara user engagement aur brand perception completely transform ho gaya. Clients ka response amazing tha!",
      author: "Amit Verma",
      position: "Marketing Director, GrowthCorp Delhi",
      rating: 5
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen"
    >
      

      {/* Agency-Style Services Section */}
      <section className="py-20 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-primary-500/10 to-blue-500/10 border border-primary-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <div className="w-2 h-2 bg-primary-400 rounded-full mr-3 animate-pulse"></div>
              <span className="text-primary-400 text-sm font-semibold uppercase tracking-wider">Enterprise Solutions</span>
            </motion.div>
            <h2 className="text-5xl md:text-7xl font-black text-white mb-8 leading-none">
              DIGITAL SERVICES
              <br />
              <span className="bg-gradient-to-r from-primary-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent">
                THAT SCALE
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
              Transform your business with cutting-edge technology solutions designed for the modern enterprise
            </p>
          </motion.div>

          {/* Services Bento Grid */}
          <MainServicesBentoGrid services={mainServices} />

          {/* Original Services Grid (Hidden) */}
          <div className="space-y-24 hidden">
            {mainServices.map((service, index) => {
              const Icon = service.icon
              const isEven = index % 2 === 0

              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className={`grid grid-cols-1 lg:grid-cols-2 gap-16 items-center ${!isEven ? 'lg:grid-flow-col-dense' : ''}`}
                >
                  {/* Content */}
                  <div className={isEven ? 'lg:pr-8' : 'lg:pl-8 lg:col-start-2'}>
                    {/* Service Badge with Popular/Enterprise indicators */}
                    <div className="flex items-center gap-3 mb-6">
                      <div className="inline-flex items-center bg-primary-500/10 border border-primary-500/20 rounded-full px-4 py-2">
                        <Icon className="text-primary-400 mr-2" size={16} />
                        <span className="text-primary-400 text-sm font-semibold">{service.subtitle}</span>
                      </div>
                      {service.badge && (
                        <div className={`inline-flex items-center bg-gradient-to-r ${service.color} px-3 py-1 rounded-full`}>
                          <span className="text-white text-xs font-bold uppercase tracking-wider">{service.badge}</span>
                        </div>
                      )}
                    </div>

                    {/* Title */}
                    <h3 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
                      {service.title}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-300 text-xl mb-8 leading-relaxed">
                      {service.description}
                    </p>

                    {/* Key Metrics */}
                    <div className="grid grid-cols-3 gap-6 mb-8">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white mb-1">{service.metrics.projects}</div>
                        <div className="text-gray-400 text-sm">Projects</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white mb-1">{service.metrics.satisfaction}</div>
                        <div className="text-gray-400 text-sm">Satisfaction</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white mb-1">{service.metrics.performance}</div>
                        <div className="text-gray-400 text-sm">Performance</div>
                      </div>
                    </div>

                    {/* Features */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                      {service.features.map((feature, featureIndex) => (
                        <motion.div
                          key={featureIndex}
                          className="flex items-center space-x-3 group"
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: featureIndex * 0.1 }}
                          viewport={{ once: true }}
                        >
                          <div className={`w-2 h-2 bg-gradient-to-r ${service.color} rounded-full group-hover:scale-125 transition-transform duration-300`}></div>
                          <span className="text-gray-300 group-hover:text-white transition-colors duration-300">{feature}</span>
                        </motion.div>
                      ))}
                    </div>

                    {/* Technologies */}
                    <div className="mb-8">
                      <h4 className="text-white font-semibold mb-4">Technology Stack:</h4>
                      <div className="flex flex-wrap gap-3">
                        {service.technologies.map((tech, techIndex) => (
                          <motion.span
                            key={techIndex}
                            className="bg-gradient-to-r from-dark-800 to-dark-700 text-gray-300 px-4 py-2 rounded-xl text-sm border border-gray-600 hover:border-primary-500/50 hover:text-white transition-all duration-300 cursor-default"
                            whileHover={{ scale: 1.05, y: -2 }}
                          >
                            {tech}
                          </motion.span>
                        ))}
                      </div>
                    </div>

                    {/* Modern Pricing Card */}
                    <div className="relative overflow-hidden">
                      <div className="bg-gradient-to-br from-black/60 to-black/30 backdrop-blur-xl border border-white/20 p-8 rounded-3xl relative group hover:border-white/30 transition-all duration-500 hover:transform hover:scale-[1.02] hover:-translate-y-2"
                        style={{ backdropFilter: 'blur(20px) saturate(180%)' }}
                      >
                        {/* Background Gradient */}
                        <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-5 group-hover:opacity-15 transition-opacity duration-500`}></div>

                        {/* Pricing Header */}
                        <div className="relative z-10">
                          <div className="flex items-start justify-between mb-8">
                            <div className="flex-1">
                              <div className="text-4xl font-black text-white mb-2">{service.price}</div>
                              <div className="text-primary-400 text-base font-medium flex items-center">
                                <span className="w-2 h-2 bg-primary-400 rounded-full mr-2"></span>
                                Timeline: {service.deliveryTime}
                              </div>
                            </div>
                            <div className={`w-16 h-16 bg-gradient-to-r ${service.color} rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg`}>
                              <Icon className="text-white" size={24} />
                            </div>
                          </div>

                          {/* Value Proposition */}
                          <div className="mb-8">
                            <div className="flex items-center space-x-2 mb-4">
                              <div className={`w-3 h-3 bg-gradient-to-r ${service.color} rounded-full`}></div>
                              <span className="text-white font-semibold">What's Included:</span>
                            </div>
                            <div className="grid grid-cols-1 gap-3">
                              {[
                                'Free Discovery Session',
                                'Dedicated Project Manager',
                                '30-Day Post-Launch Support',
                                'Performance Optimization'
                              ].map((item, idx) => (
                                <div key={idx} className="flex items-center space-x-3">
                                  <CheckCircle className="text-green-400 flex-shrink-0" size={16} />
                                  <span className="text-gray-300 text-sm">{item}</span>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* CTA Button */}
                          <Link to="/get-quote">
                            <motion.button
                              whileHover={{ scale: 1.02, y: -2 }}
                              whileTap={{ scale: 0.98 }}
                              className={`w-full bg-gradient-to-r ${service.color} text-white py-4 rounded-2xl font-bold text-lg hover:shadow-2xl transition-all duration-300 flex items-center justify-center space-x-3 mb-6 relative overflow-hidden group/btn`}
                            >
                              <span className="relative z-10">Get Quote</span>
                              <ArrowRight className="relative z-10" size={18} />
                              {/* Button Shine Effect */}
                              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>
                            </motion.button>
                          </Link>

                          {/* Guarantees */}
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            {[
                              { icon: Shield, text: 'Money-back Guarantee', color: 'text-green-400' },
                              { icon: Users, text: '24/7 Support', color: 'text-blue-400' }
                            ].map((item, idx) => (
                              <div key={idx} className="flex items-center space-x-3 text-sm bg-white/5 rounded-xl p-3 border border-white/10">
                                <item.icon className={item.color} size={16} />
                                <span className="text-gray-300 font-medium">{item.text}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Hover Glow Effect */}
                        <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl`}></div>

                        {/* Animated Border */}
                        <div className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                          style={{
                            background: `linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.3), transparent)`,
                            backgroundSize: '200% 200%',
                            animation: 'gradient-shift 3s ease infinite'
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>

                  {/* Modern Visual Card */}
                  <div className={isEven ? 'lg:pl-8' : 'lg:pr-8 lg:col-start-1 lg:row-start-1'}>
                    <div className="relative group">
                      {/* Main Card Container */}
                      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-black/60 to-black/30 backdrop-blur-xl border border-white/20 group-hover:border-white/30 transition-all duration-500 hover:transform hover:scale-[1.02] hover:-translate-y-2"
                        style={{ backdropFilter: 'blur(20px) saturate(180%)' }}
                      >
                        {/* Background Image */}
                        <div className="relative h-96 overflow-hidden">
                          <img
                            src={service.image}
                            alt={service.title}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/30 to-transparent"></div>

                          {/* Floating Tech Badges */}
                          <div className="absolute top-6 left-6 flex flex-wrap gap-3">
                            {service.technologies.slice(0, 3).map((tech, techIndex) => (
                              <motion.span
                                key={techIndex}
                                className="bg-black/60 backdrop-blur-md border border-white/30 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg"
                                initial={{ opacity: 0, y: -20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: techIndex * 0.1 }}
                                style={{ backdropFilter: 'blur(12px) saturate(180%)' }}
                                whileHover={{ scale: 1.05, y: -2 }}
                              >
                                {tech}
                              </motion.span>
                            ))}
                          </div>

                          {/* Service Badge */}
                          {service.badge && (
                            <div className="absolute top-6 right-6">
                              <div className={`bg-gradient-to-r ${service.color} px-4 py-2 rounded-full shadow-lg`}>
                                <span className="text-white text-sm font-bold uppercase tracking-wider">{service.badge}</span>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Card Content */}
                        <div className="p-8">
                          <div className="flex items-start justify-between mb-6">
                            <div className="flex-1">
                              <h4 className="text-white font-bold text-2xl mb-2">{service.title}</h4>
                              <p className="text-primary-400 text-base font-medium uppercase tracking-wider">{service.subtitle}</p>
                            </div>
                            <div className={`w-16 h-16 bg-gradient-to-r ${service.color} rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg`}>
                              <Icon className="text-white" size={28} />
                            </div>
                          </div>

                          {/* Key Metrics Display */}
                          <div className="grid grid-cols-3 gap-4 mb-6">
                            <div className="text-center bg-white/5 rounded-xl p-3 border border-white/10">
                              <div className="text-lg font-bold text-white">{service.metrics.projects}</div>
                              <div className="text-gray-400 text-xs uppercase tracking-wider">Projects</div>
                            </div>
                            <div className="text-center bg-white/5 rounded-xl p-3 border border-white/10">
                              <div className="text-lg font-bold text-white">{service.metrics.satisfaction}</div>
                              <div className="text-gray-400 text-xs uppercase tracking-wider">Success</div>
                            </div>
                            <div className="text-center bg-white/5 rounded-xl p-3 border border-white/10">
                              <div className="text-lg font-bold text-white">{service.metrics.performance}</div>
                              <div className="text-gray-400 text-xs uppercase tracking-wider">Faster</div>
                            </div>
                          </div>

                          {/* Progress Bar */}
                          <div className="mt-6">
                            <div className="flex items-center justify-between text-sm mb-3">
                              <span className="text-gray-400 font-medium">Project Success Rate</span>
                              <span className="text-primary-400 font-bold">{service.metrics.satisfaction}</span>
                            </div>
                            <div className="w-full bg-gray-700/50 rounded-full h-3 overflow-hidden">
                              <motion.div
                                className={`h-3 bg-gradient-to-r ${service.color} rounded-full relative`}
                                initial={{ width: 0 }}
                                whileInView={{ width: service.metrics.satisfaction }}
                                transition={{ duration: 1.5, delay: 0.5 }}
                                viewport={{ once: true }}
                              >
                                {/* Shine effect */}
                                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full animate-pulse"></div>
                              </motion.div>
                            </div>
                          </div>
                        </div>

                        {/* Hover Glow Effect */}
                        <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl`}></div>

                        {/* Animated Border */}
                        <div className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                          style={{
                            background: `linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.3), transparent)`,
                            backgroundSize: '200% 200%',
                            animation: 'gradient-shift 3s ease infinite'
                          }}
                        ></div>
                      </div>

                      {/* Floating Stats Card */}
                      <motion.div
                        className="absolute -top-6 -right-6 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 text-white p-4 rounded-2xl shadow-2xl"
                        animate={{ y: [0, -8, 0] }}
                        transition={{ duration: 4, repeat: Infinity }}
                        style={{ backdropFilter: 'blur(20px) saturate(180%)' }}
                      >
                        <div className="text-center">
                          <div className="text-2xl font-bold text-primary-400">150+</div>
                          <div className="text-xs text-gray-300 uppercase tracking-wider">Projects</div>
                        </div>
                      </motion.div>

                      {/* Floating Success Badge */}
                      <motion.div
                        className="absolute -bottom-4 -left-4 bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-xl border border-green-400/30 text-green-400 px-4 py-2 rounded-xl shadow-lg"
                        animate={{ y: [0, 8, 0] }}
                        transition={{ duration: 3, repeat: Infinity, delay: 1 }}
                        style={{ backdropFilter: 'blur(20px) saturate(180%)' }}
                      >
                        <div className="flex items-center space-x-2">
                          <CheckCircle size={16} />
                          <span className="text-sm font-semibold">98% Success Rate</span>
                        </div>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 to-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <motion.div
              initial={{ x: -50, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="inline-flex items-center bg-primary-500/10 border border-primary-500/20 rounded-full px-4 py-2 mb-6">
                <span className="text-primary-400 text-sm font-semibold uppercase tracking-wider">Why Choose Us</span>
              </div>

              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Your Success is Our <span className="bg-gradient-to-r from-primary-400 to-blue-500 bg-clip-text text-transparent">Priority</span>
              </h2>

              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                We don't just build websites and apps – we create digital experiences that drive real business results.
                Here's what sets us apart from the competition.
              </p>

              <div className="space-y-6">
                {[
                  {
                    icon: '⚡',
                    title: 'Lightning Fast Delivery',
                    description: 'We deliver projects 40% faster than industry average without compromising quality.'
                  },
                  {
                    icon: '🎯',
                    title: 'Results-Driven Approach',
                    description: 'Every project is designed with your business goals in mind, ensuring measurable ROI.'
                  },
                  {
                    icon: '🛡️',
                    title: 'Enterprise-Grade Security',
                    description: 'Bank-level security protocols protect your data and your customers\' information.'
                  },
                  {
                    icon: '🚀',
                    title: 'Scalable Solutions',
                    description: 'Built to grow with your business, from startup to enterprise scale.'
                  }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    className="flex items-start space-x-4"
                    initial={{ y: 20, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <div className="text-3xl">{item.icon}</div>
                    <div>
                      <h4 className="text-white font-semibold mb-2">{item.title}</h4>
                      <p className="text-gray-400 leading-relaxed">{item.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Right Stats */}
            <motion.div
              className="grid grid-cols-2 gap-6"
              initial={{ x: 50, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              {[
                { number: '150+', label: 'Projects Completed', icon: '📊', color: 'from-blue-500 to-cyan-500' },
                { number: '98%', label: 'Client Satisfaction', icon: '😊', color: 'from-green-500 to-emerald-500' },
                { number: '24/7', label: 'Support Available', icon: '🕒', color: 'from-purple-500 to-pink-500' },
                { number: '5★', label: 'Average Rating', icon: '⭐', color: 'from-yellow-500 to-orange-500' }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  className="relative group"
                  initial={{ y: 30, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <div className="bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-xl border border-white/10 p-6 rounded-2xl text-center group-hover:border-white/20 transition-all duration-500 relative overflow-hidden"
                    style={{ backdropFilter: 'blur(20px) saturate(180%)' }}
                  >
                    {/* Background Gradient */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-5 group-hover:opacity-15 transition-opacity duration-500`}></div>

                    {/* Content */}
                    <div className="relative z-10">
                      {/* Icon with Gradient Background */}
                      <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 group-hover:rotate-6 transition-all duration-300`}>
                        <span className="text-2xl">{stat.icon}</span>
                      </div>

                      {/* Number */}
                      <div className="text-3xl font-bold text-white mb-2 group-hover:text-primary-300 transition-colors duration-300">
                        {stat.number}
                      </div>

                      {/* Label */}
                      <div className="text-gray-400 text-sm font-medium">{stat.label}</div>
                    </div>

                    {/* Hover Glow Effect */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-2xl`}></div>

                    {/* Animated Border */}
                    <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                      style={{
                        background: `linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.3), transparent)`,
                        backgroundSize: '200% 200%',
                        animation: 'gradient-shift 3s ease infinite'
                      }}
                    ></div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>



      {/* Industries Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="inline-flex items-center bg-primary-500/10 border border-primary-500/20 rounded-full px-4 py-2 mb-6">
                <span className="text-primary-400 text-sm font-medium">Industries We Serve</span>
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
                Trusted Across <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Industries</span>
              </h2>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto">
                We've delivered successful projects across diverse industries, understanding unique challenges and requirements
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {industries.map((industry, index) => (
              <motion.div
                key={index}
                initial={{ y: 30, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-dark-800/50 backdrop-blur-sm p-6 rounded-xl text-center border border-gray-700 hover:border-primary-500/50 transition-all duration-300 group"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-primary-500/20 to-primary-600/30 rounded-2xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg border border-primary-500/20 p-3">
                  <span className="text-2xl">{industry.icon}</span>
                </div>
                <h3 className="text-white font-semibold mb-1 group-hover:text-primary-400 transition-colors duration-300">
                  {industry.name}
                </h3>
                <p className="text-gray-400 text-sm">{industry.projects} projects</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="inline-flex items-center bg-primary-500/10 border border-primary-500/20 rounded-full px-4 py-2 mb-6">
                <span className="text-primary-400 text-sm font-medium">Our Process</span>
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
                How We <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Deliver Excellence</span>
              </h2>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto">
                A proven methodology that ensures successful project delivery and client satisfaction
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: '01', title: 'Discovery & Strategy', description: 'Deep dive into your business goals, target audience, and technical requirements' },
              { step: '02', title: 'Design & Planning', description: 'Create detailed wireframes, designs, and project roadmaps with clear milestones' },
              { step: '03', title: 'Development & Testing', description: 'Build your solution using best practices with continuous testing and quality assurance' },
              { step: '04', title: 'Launch & Support', description: 'Deploy your project and provide ongoing maintenance and optimization support' }
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center relative"
              >
                <div className="bg-gradient-to-r from-primary-500 to-primary-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 relative z-10">
                  <span className="text-white font-bold text-lg">{item.step}</span>
                </div>
                {index < 3 && (
                  <div className="hidden md:block absolute top-8 left-1/2 w-full h-0.5 bg-gradient-to-r from-primary-500/50 to-transparent z-0"></div>
                )}
                <h3 className="text-xl font-semibold text-white mb-3">{item.title}</h3>
                <p className="text-gray-400 leading-relaxed">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Client Testimonials */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="inline-flex items-center bg-primary-500/10 border border-primary-500/20 rounded-full px-4 py-2 mb-6">
                <span className="text-primary-400 text-sm font-medium">Client Success</span>
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
                What Our <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Clients Say</span>
              </h2>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto">
                Real results from real clients who've experienced the Delta Xero difference
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-dark-800/50 backdrop-blur-sm p-8 rounded-2xl border border-gray-700 relative"
              >
                <div className="flex space-x-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="text-yellow-400 fill-current" size={16} />
                  ))}
                </div>
                <p className="text-gray-300 text-lg mb-6 leading-relaxed italic">
                  "{testimonial.quote}"
                </p>
                <div>
                  <h4 className="text-white font-semibold">{testimonial.author}</h4>
                  <p className="text-gray-400 text-sm">{testimonial.position}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Modern CTA Section */}
      <section className="py-32 bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Badge */}
            <motion.div
              className="inline-flex items-center bg-black/20 backdrop-blur-xl border border-white/10 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
              style={{ backdropFilter: 'blur(20px) saturate(180%)' }}
            >
              <Rocket className="text-primary-400 mr-3" size={16} />
              <span className="text-white text-sm font-medium">Ready to Transform?</span>
            </motion.div>

            {/* Title */}
            <h2 className="text-5xl md:text-7xl font-black text-white mb-8 leading-none">
              LET'S BUILD
              <br />
              <span className="bg-gradient-to-r from-primary-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent">
                SOMETHING AMAZING
              </span>
            </h2>

            {/* Description */}
            <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
              Ready to transform your digital presence? Get a free consultation and discover how we can help your business grow.
            </p>

            {/* CTA Button */}
            <div className="flex justify-center mb-16">
              <Link to="/get-quote">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-white text-black px-12 py-4 rounded-2xl font-bold text-lg hover:shadow-2xl hover:shadow-white/20 transition-all duration-300 flex items-center justify-center space-x-3"
                >
                  <span>Get Quote</span>
                  <ArrowRight size={20} />
                </motion.button>
              </Link>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              {[
                { icon: CheckCircle, text: 'Free Consultation', color: 'text-green-400' },
                { icon: CheckCircle, text: 'Custom Solutions', color: 'text-blue-400' },
                { icon: CheckCircle, text: 'Ongoing Support', color: 'text-purple-400' }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  className="flex items-center justify-center space-x-3 text-gray-300"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <item.icon className={item.color} size={20} />
                  <span className="font-medium">{item.text}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    </motion.div>
  )
}

export default Services

// Add CSS animations for gradient effects
const style = document.createElement('style')
style.textContent = `
  @keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes pulse-glow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
  }
`
if (!document.head.querySelector('style[data-services-animations]')) {
  style.setAttribute('data-services-animations', 'true')
  document.head.appendChild(style)
}
