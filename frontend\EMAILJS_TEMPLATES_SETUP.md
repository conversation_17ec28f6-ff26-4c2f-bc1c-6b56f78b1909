# EmailJS Templates Setup for Delta Xero Creations

## Your Configuration
- **Service ID**: `service_15pixg2` ✅ (Already configured)
- **Public Key**: `iOQKBBBfqhJh_HNHJ` (Default - update if needed)

## Required Templates to Create

### 1. Application Confirmation Template
**Template ID**: `template_deltaxero_app_confirm`

**Subject**: 
```
{{subject}}
```

**Content**:
```
Dear {{to_name}},

{{message}}

Best regards,
{{from_name}}

---
This is an automated message. Please do not reply to this email.
If you have questions, contact us at {{reply_to}}
```

### 2. New User Credentials Template  
**Template ID**: `template_deltaxero_new_user`

**Subject**:
```
{{subject}}
```

**Content**:
```
Dear {{to_name}},

{{message}}

Best regards,
{{from_name}}

---
SECURITY NOTICE: This email contains sensitive login information.
Please change your password immediately after first login.

For support, contact: {{reply_to}}
```

### 3. Admin Notification Template
**Template ID**: `template_deltaxero_admin_notify`

**Subject**:
```
{{subject}}
```

**Content**:
```
{{to_name}},

{{message}}

---
{{from_name}}
Automated Notification System
```

## How to Create Templates in EmailJS

1. **Login to EmailJS Dashboard**
   - Go to [emailjs.com](https://www.emailjs.com)
   - Login to your account

2. **Navigate to Email Templates**
   - Click "Email Templates" in the sidebar
   - Click "Create New Template"

3. **Create Each Template**
   - Use the Template IDs and content above
   - Copy the exact Template ID (case-sensitive)
   - Paste the Subject and Content as shown

4. **Test Templates**
   - Use the "Test" button in EmailJS
   - Send test emails to verify formatting

## Template Variables Used

### Application Confirmation & Admin Notifications:
- `{{to_email}}` - Recipient email
- `{{to_name}}` - Recipient name  
- `{{subject}}` - Email subject
- `{{message}}` - Main email content
- `{{from_name}}` - Sender name
- `{{reply_to}}` - Reply email address

### All templates use the same variables for consistency

## Testing Your Setup

### Test Application Emails:
1. Submit a job application on your website
2. Check the applicant's email for confirmation
3. Check <EMAIL> for admin notification

### Test User Creation Emails:
1. Create a new user in admin panel
2. Check the new user's email for credentials

## Troubleshooting

### Common Issues:
- **Template not found**: Check Template ID spelling
- **Variables not working**: Ensure {{variable}} format
- **Emails not sending**: Verify Service ID

### Debug Steps:
1. Check browser console for errors
2. Verify EmailJS service is active
3. Test templates individually in EmailJS dashboard

## Email Addresses to Update

Update these in your EmailJS service settings:
- **HR Email**: `<EMAIL>`
- **Admin Email**: `<EMAIL>`  
- **IT Support**: `<EMAIL>`

## Ready to Use!

Once you create these 3 templates, your email system will automatically:

✅ Send confirmation emails to job applicants
✅ Send confirmation emails to internship applicants  
✅ Notify HR team of new applications
✅ Send login credentials to new users
✅ Handle all email formatting and delivery

The system is already integrated into your application forms and user management!
