import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import AdminNavigation from '../../components/AdminNavigation'
import { userAPI } from '../../lib/api'
import { emailService } from '../../lib/emailService'
import {
  ArrowLeft,
  Plus,
  Edit,
  Trash2,
  Shield,
  User,
  Users,
  Mail,
  Calendar,
  MoreVertical,
  Search,
  Filter
} from 'lucide-react'

const UserManagement = () => {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const [searchTerm, setSearchTerm] = useState('')
  const [filterRole, setFilterRole] = useState('all')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    role: 'user',
    username: ''
  })
  const [formErrors, setFormErrors] = useState({})

  const roles = ['all', 'supervisor', 'admin', 'manager', 'user']

  // Fetch users on component mount
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true)
        const { data, error } = await userAPI.getAll()

        if (error) {
          throw new Error(error)
        }

        // Format users for display
        const formattedUsers = (data || []).map(user => ({
          id: user.id,
          username: user.username || 'N/A',
          email: user.email,
          role: user.role,
          created: new Date(user.created_at).toLocaleDateString(),
          status: user.is_active ? 'active' : 'inactive',
          isTemporaryPassword: user.is_temporary_password
        }))

        setUsers(formattedUsers)
      } catch (err) {
        console.error('Error fetching users:', err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchUsers()
  }, [])

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = filterRole === 'all' || user.role === filterRole
    return matchesSearch && matchesRole
  })

  const getRoleColor = (role) => {
    switch (role) {
      case 'supervisor': return 'bg-purple-600 text-white'
      case 'admin': return 'bg-red-600 text-white'
      case 'manager': return 'bg-blue-600 text-white'
      case 'user': return 'bg-gray-600 text-white'
      default: return 'bg-gray-600 text-white'
    }
  }

  const handleDeleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        const { error } = await userAPI.delete(userId)

        if (error) {
          throw new Error(error)
        }

        // Remove from local state
        setUsers(users.filter(user => user.id !== userId))
        alert('User deleted successfully!')
      } catch (err) {
        console.error('Error deleting user:', err)
        alert('Failed to delete user. Please try again.')
      }
    }
  }

  // Generate random password
  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
    let password = ''
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return password
  }

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  // Generate one-time password
  const handleGeneratePassword = () => {
    const newPassword = generatePassword()
    setFormData(prev => ({
      ...prev,
      password: newPassword
    }))
  }

  // Validate form
  const validateForm = () => {
    const errors = {}

    if (!formData.email.trim()) {
      errors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid'
    } else if (users.some(user => user.email === formData.email)) {
      errors.email = 'Email already exists'
    }

    if (!formData.password.trim()) {
      errors.password = 'Password is required'
    } else if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters'
    }

    if (!formData.username.trim()) {
      errors.username = 'Username is required'
    } else if (users.some(user => user.username === formData.username)) {
      errors.username = 'Username already exists'
    }

    if (!formData.role) {
      errors.role = 'Role is required'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Handle form submission
  const handleCreateUser = async (e) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsCreating(true)

    try {
      // Create user via API
      const { data, error } = await userAPI.create({
        username: formData.username,
        email: formData.email,
        password: formData.password,
        role: formData.role
      })

      if (error) {
        throw new Error(error)
      }

      // Format new user for display
      const newUser = {
        id: data.id,
        username: data.username,
        email: data.email,
        role: data.role,
        created: new Date(data.created_at).toLocaleDateString(),
        status: data.is_active ? 'active' : 'inactive',
        isTemporaryPassword: data.is_temporary_password
      }

      setUsers(prev => [...prev, newUser])

      // Send credentials email to new user
      try {
        const emailResult = await emailService.sendNewUserCredentials(
          {
            username: formData.username,
            email: formData.email,
            role: formData.role
          },
          formData.password
        )

        if (emailResult.success) {
          console.log('Credentials email sent successfully')
        } else {
          console.error('Failed to send credentials email:', emailResult.error)
        }
      } catch (emailError) {
        console.error('Error sending credentials email:', emailError)
        // Don't fail user creation if email fails
      }

      // Reset form
      setFormData({
        email: '',
        password: '',
        role: 'user',
        username: ''
      })
      setFormErrors({})
      setShowCreateForm(false)

      alert(`User created successfully!\nCredentials have been sent to: ${newUser.email}\n\nThe user will be required to change their password on first login.`)

    } catch (error) {
      console.error('Error creating user:', error)
      alert(`Failed to create user: ${error.message}`)
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <div className="min-h-screen bg-dark-950">
      <AdminNavigation />
      <div className="pt-20 pb-20 md:pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-white transition-colors duration-300">
                <ArrowLeft size={24} />
              </button>
              <div>
                <h1 className="text-3xl font-bold text-white">User Management</h1>
                <p className="text-gray-400 text-sm">Check Permissions</p>
              </div>
            </div>
            <button
              onClick={() => setShowCreateForm(true)}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 flex items-center space-x-2"
            >
              <Plus size={16} />
              <span>Add New User</span>
            </button>
          </div>

          {/* Filters */}
          <div className="bg-dark-800/50 border border-gray-700/50 rounded-2xl p-6 mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-dark-700/50 border border-gray-600/30 rounded-xl text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/20 transition-all duration-300"
                  />
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Filter className="text-gray-400" size={20} />
                  <select
                    value={filterRole}
                    onChange={(e) => setFilterRole(e.target.value)}
                    className="bg-dark-700/50 border border-gray-600/30 rounded-xl px-4 py-3 text-white focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/20 transition-all duration-300"
                  >
                    {roles.map(role => (
                      <option key={role} value={role} className="bg-dark-900 text-white">
                        {role === 'all' ? 'All Roles' : role.charAt(0).toUpperCase() + role.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Users Section */}
          <div className="bg-dark-800/50 border border-gray-700/50 rounded-2xl overflow-hidden">
            <div className="p-6 border-b border-gray-700/50">
              <h2 className="text-xl font-bold text-white">Users</h2>
            </div>

            {/* Table Header */}
            <div className="grid grid-cols-5 gap-4 px-6 py-4 bg-dark-700/30 border-b border-gray-700/50">
              <div className="text-gray-400 text-sm font-medium uppercase tracking-wider">USERNAME</div>
              <div className="text-gray-400 text-sm font-medium uppercase tracking-wider">EMAIL</div>
              <div className="text-gray-400 text-sm font-medium uppercase tracking-wider">ROLE</div>
              <div className="text-gray-400 text-sm font-medium uppercase tracking-wider">CREATED</div>
              <div className="text-gray-400 text-sm font-medium uppercase tracking-wider">ACTIONS</div>
            </div>

            {/* Table Body */}
            <div className="divide-y divide-gray-700/50">
              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
                  <p className="text-gray-400">Loading users...</p>
                </div>
              ) : error ? (
                <div className="text-center py-12">
                  <p className="text-red-400 mb-4">Error loading users: {error}</p>
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg"
                  >
                    Retry
                  </button>
                </div>
              ) : filteredUsers.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-gray-400">No users found</p>
                </div>
              ) : (
                filteredUsers.map((user, index) => (
                <motion.div
                  key={user.id}
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="grid grid-cols-5 gap-4 px-6 py-4 hover:bg-dark-700/30 transition-all duration-300"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                      <User className="text-white" size={16} />
                    </div>
                    <span className="text-white font-medium">{user.username}</span>
                  </div>
                  
                  <div className="flex items-center">
                    <span className="text-gray-300">{user.email}</span>
                  </div>
                  
                  <div className="flex items-center">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                      {user.role}
                    </span>
                  </div>
                  
                  <div className="flex items-center">
                    <span className="text-gray-300">{user.created}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button className="text-blue-400 hover:text-blue-300 transition-colors duration-300">
                      Edit
                    </button>
                    <button 
                      onClick={() => handleDeleteUser(user.id)}
                      className="text-red-400 hover:text-red-300 transition-colors duration-300"
                    >
                      Delete
                    </button>
                  </div>
                </motion.div>
                ))
              )}
            </div>
          </div>

          {/* Bottom Actions */}
          <div className="fixed bottom-6 right-6">
            <button className="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2">
              <Users size={20} />
              <span className="hidden sm:inline">Manage Users</span>
            </button>
          </div>
        </div>
      </div>

      {/* Create User Modal */}
      {showCreateForm && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={() => setShowCreateForm(false)}
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="bg-dark-800 rounded-2xl border border-gray-700 max-w-md w-full"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-xl font-bold text-white">Create New User</h2>
              <button
                onClick={() => setShowCreateForm(false)}
                className="p-2 text-gray-400 hover:text-white transition-colors duration-300"
              >
                <Plus className="rotate-45" size={20} />
              </button>
            </div>

            {/* Modal Content */}
            <form onSubmit={handleCreateUser} className="p-6 space-y-4">
              {/* Username */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Username *
                </label>
                <input
                  type="text"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/20 transition-all duration-300"
                  placeholder="Enter username"
                />
                {formErrors.username && (
                  <p className="text-red-400 text-sm mt-1">{formErrors.username}</p>
                )}
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Email *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/20 transition-all duration-300"
                  placeholder="Enter email address"
                />
                {formErrors.email && (
                  <p className="text-red-400 text-sm mt-1">{formErrors.email}</p>
                )}
              </div>

              {/* Role */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Role *
                </label>
                <select
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/20 transition-all duration-300"
                >
                  <option value="user" className="bg-dark-900">User</option>
                  <option value="manager" className="bg-dark-900">Manager</option>
                  <option value="admin" className="bg-dark-900">Admin</option>
                  <option value="supervisor" className="bg-dark-900">Supervisor</option>
                </select>
                {formErrors.role && (
                  <p className="text-red-400 text-sm mt-1">{formErrors.role}</p>
                )}
              </div>

              {/* Password */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  One-Time Password *
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="flex-1 px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/20 transition-all duration-300"
                    placeholder="Enter or generate password"
                  />
                  <button
                    type="button"
                    onClick={handleGeneratePassword}
                    className="px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-xl font-medium transition-all duration-300"
                  >
                    Generate
                  </button>
                </div>
                {formErrors.password && (
                  <p className="text-red-400 text-sm mt-1">{formErrors.password}</p>
                )}
                <p className="text-gray-500 text-xs mt-1">
                  User will be required to change this password on first login
                </p>
              </div>

              {/* Submit Buttons */}
              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="flex-1 px-4 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-xl font-medium transition-all duration-300"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isCreating}
                  className="flex-1 px-4 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 disabled:cursor-not-allowed text-white rounded-xl font-medium transition-all duration-300 flex items-center justify-center space-x-2"
                >
                  {isCreating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Creating...</span>
                    </>
                  ) : (
                    <>
                      <Plus size={16} />
                      <span>Create User</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </div>
  )
}

export default UserManagement
