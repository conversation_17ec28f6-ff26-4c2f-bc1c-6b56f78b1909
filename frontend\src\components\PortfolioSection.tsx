import React from 'react';
import { motion } from 'framer-motion';
import { Component as InfiniteMenu } from './ui/Infinitemenu';

interface ProjectItem {
  image: string;
  link: string;
  title: string;
  description: string;
  category: string;
  date: string;
}

const portfolioProjects: ProjectItem[] = [
  {
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=900&h=900&fit=crop',
    link: '#',
    title: 'E-Commerce Platform',
    description: 'A modern e-commerce platform built with React and Node.js featuring real-time inventory management and secure payment processing.',
    category: 'Web Development',
    technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'Socket.io'],
    date: 'December 2024',
    githubLink: 'https://github.com',
    liveLink: 'https://demo.com',
    features: ['Real-time inventory', 'Secure payments', 'Admin dashboard', 'Mobile responsive']
  },
  {
    image: 'https://images.unsplash.com/photo-*************-90a1b58e7e9c?w=900&h=900&fit=crop',
    link: '#',
    title: 'Mobile Banking App',
    description: 'Cross-platform mobile banking application with biometric authentication and real-time transaction monitoring.',
    category: 'Mobile Development',
    technologies: ['React Native', 'Firebase', 'TypeScript', 'Redux'],
    date: 'November 2024',
    githubLink: 'https://github.com',
    liveLink: 'https://app.com',
    features: ['Biometric auth', 'Real-time notifications', 'Transaction history', 'Budget tracking']
  },
  {
    image: 'https://images.unsplash.com/photo-**********-9f40138edfeb?w=900&h=900&fit=crop',
    link: '#',
    title: 'Design System',
    description: 'Comprehensive design system and component library for consistent user experiences across multiple products.',
    category: 'UI/UX Design',
    technologies: ['Figma', 'Storybook', 'React', 'Tailwind CSS'],
    date: 'October 2024',
    githubLink: 'https://github.com',
    liveLink: 'https://storybook.com',
    features: ['Component library', 'Design tokens', 'Documentation', 'Accessibility focused']
  },
  {
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=900&h=900&fit=crop',
    link: '#',
    title: 'Analytics Dashboard',
    description: 'Real-time analytics dashboard with interactive charts and data visualization for business intelligence.',
    category: 'Web Development',
    technologies: ['Next.js', 'D3.js', 'PostgreSQL', 'Chart.js'],
    date: 'September 2024',
    githubLink: 'https://github.com',
    liveLink: 'https://dashboard.com',
    features: ['Real-time data', 'Interactive charts', 'Export functionality', 'Custom filters']
  },
  {
    image: 'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=900&h=900&fit=crop',
    link: '#',
    title: 'Social Media App',
    description: 'Social networking platform with real-time messaging, content sharing, and community features.',
    category: 'Mobile Development',
    technologies: ['Flutter', 'Firebase', 'WebRTC', 'Cloud Functions'],
    date: 'August 2024',
    githubLink: 'https://github.com',
    liveLink: 'https://social.com',
    features: ['Real-time chat', 'Media sharing', 'Stories feature', 'Push notifications']
  },
  {
    image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=900&h=900&fit=crop',
    link: '#',
    title: 'Portfolio Website',
    description: 'Interactive portfolio website with advanced animations and smooth user experience.',
    category: 'Web Development',
    technologies: ['React', 'GSAP', 'Three.js', 'Framer Motion'],
    date: 'July 2024',
    githubLink: 'https://github.com',
    liveLink: 'https://portfolio.com',
    features: ['3D animations', 'Smooth scrolling', 'Interactive elements', 'Performance optimized']
  }
];

const PortfolioSection: React.FC = () => {
  // Convert portfolio projects to menu items format
  const menuItems = portfolioProjects.map(project => ({
    image: project.image,
    link: project.link,
    title: project.title,
    description: project.description
  }));

  return (
    <section className="h-screen w-full bg-dark-950 relative overflow-hidden">
      <div className="h-full w-full max-w-full">
        {/* Drag Instruction Tag */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="absolute top-4 md:top-8 left-4 md:left-8 z-20 bg-primary-500/20 backdrop-blur-sm border border-primary-500/30 rounded-full px-3 md:px-4 py-2 md:py-3"
        >
          <span className="text-primary-300 text-xs md:text-sm font-medium flex items-center space-x-2">
            <span>🎯</span>
            <span className="hidden sm:inline">Drag to see projects</span>
            <span className="sm:hidden">Tap buttons to navigate</span>
          </span>
        </motion.div>

        {/* Infinite Menu Component - Full Screen */}
        <div className="h-full w-full">
          <InfiniteMenu items={menuItems} />
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;
