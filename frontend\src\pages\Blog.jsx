import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

// Blog Post Card Component
const BlogCard = ({ post }) => {
  return (
    <motion.div
      className="bg-dark-800/50 backdrop-blur-sm border border-gray-700 rounded-xl overflow-hidden h-full hover:shadow-lg hover:shadow-primary-500/10 hover:border-primary-500/30 transition-all duration-300"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="relative">
        <img
          src={post.image}
          alt={post.title}
          className="w-full h-48 object-cover"
        />
        <div className="absolute top-4 right-4 bg-dark-900/80 backdrop-blur-sm text-white text-xs font-medium px-3 py-1 rounded-full">
          {post.category}
        </div>
      </div>
      <div className="p-6">
        <div className="flex items-center text-sm text-gray-400 mb-3">
          <span>{post.date}</span>
          <span className="mx-2">•</span>
          <span>{post.readTime} min read</span>
        </div>
        <h3 className="text-xl font-bold text-white mb-3 hover:text-primary-400 transition-colors">
          {post.title}
        </h3>
        <p className="text-gray-300 mb-4 line-clamp-3">
          {post.excerpt}
        </p>
        <div className="flex items-center justify-end">
          {post.tags && (
            <div className="flex flex-wrap gap-1 mt-2">
              {post.tags.slice(0, 3).map((tag, index) => (
                <span key={index} className="text-xs text-primary-400 bg-primary-500/10 px-2 py-1 rounded-full">
                  {tag}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

// Featured Post Component
const FeaturedPost = ({ post }) => {
  return (
    <motion.div
      className="bg-dark-800/50 backdrop-blur-sm border border-gray-700 rounded-xl overflow-hidden mb-12"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="md:flex">
        <div className="md:w-1/2 relative">
          <img
            src={post.image}
            alt={post.title}
            className="w-full h-64 md:h-full object-cover"
          />
          <div className="absolute top-4 left-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white text-xs font-bold px-3 py-1 rounded-full">
            Featured
          </div>
          <div className="absolute top-4 right-4 bg-dark-900/80 backdrop-blur-sm text-white text-xs font-medium px-3 py-1 rounded-full">
            {post.category}
          </div>
        </div>
        <div className="md:w-1/2 p-6 md:p-8 flex flex-col justify-center">
          <div className="flex items-center text-sm text-gray-400 mb-3">
            <span>{post.date}</span>
            <span className="mx-2">•</span>
            <span>{post.readTime} min read</span>
          </div>
          <h3 className="text-2xl font-bold text-white mb-4 hover:text-primary-400 transition-colors">
            {post.title}
          </h3>
          <p className="text-gray-300 mb-6">
            {post.excerpt}
          </p>
          <div className="flex flex-wrap gap-2 mb-6">
            {post.tags && post.tags.slice(0, 3).map((tag, index) => (
              <span key={index} className="text-xs text-primary-400 bg-primary-500/10 px-2 py-1 rounded-full">
                {tag}
              </span>
            ))}
          </div>
          <div className="inline-flex items-center text-primary-400 hover:text-primary-300 transition-colors">
            Read Full Article
            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
            </svg>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const Blog = () => {
  const [activeCategory, setActiveCategory] = useState('All');

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Blog categories
  const categories = ['All', 'Web Development', 'Design', 'Mobile Development', 'Performance', 'Business'];

  // Sample blog posts data
  const blogPosts = [
    {
      id: 1,
      title: 'How to Build Scalable Web Applications with Modern Frameworks',
      slug: 'scalable-web-applications-modern-frameworks',
      excerpt: 'Discover effective strategies for building scalable web applications using React, Next.js, and modern development practices that grow with your business.',
      image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
      category: 'Web Development',
      date: 'April 19, 2025',
      readTime: 8,
      featured: true,
      tags: ['React', 'Next.js', 'scalability', 'web development', 'architecture']
    },
    {
      id: 2,
      title: 'Why Affordable Web Development Doesn\'t Mean Compromising Quality',
      slug: 'affordable-web-development-quality',
      excerpt: 'Explore how Delta Xero Creations delivers premium quality web solutions at affordable prices through efficient processes and modern technologies.',
      image: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
      category: 'Business',
      date: 'February 15, 2025',
      readTime: 6,
      featured: false,
      tags: ['affordable development', 'quality', 'business strategy', 'value']
    },
    {
      id: 3,
      title: 'Essential Web Design Trends Shaping 2025',
      slug: 'web-design-trends-2025',
      excerpt: 'Stay ahead of the curve with these cutting-edge web design trends that are transforming user experiences and driving engagement in 2025.',
      image: 'https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
      category: 'Design',
      date: 'March 8, 2025',
      readTime: 7,
      featured: false,
      tags: ['web design', 'trends', 'UI/UX', 'user experience', '2025']
    },
    {
      id: 4,
      title: 'The Future of Mobile App Development: React Native vs Flutter',
      slug: 'mobile-app-development-react-native-flutter',
      excerpt: 'Compare React Native and Flutter frameworks to make informed decisions for your next mobile app development project.',
      image: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1172&q=80',
      category: 'Mobile Development',
      date: 'February 22, 2025',
      readTime: 9,
      featured: false,
      tags: ['mobile development', 'React Native', 'Flutter', 'cross-platform']
    },
    {
      id: 5,
      title: 'Building Your First Professional Portfolio Website',
      slug: 'professional-portfolio-website-guide',
      excerpt: 'A comprehensive guide to creating an impressive portfolio website that showcases your skills and attracts potential clients or employers.',
      image: 'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
      category: 'Web Development',
      date: 'March 17, 2025',
      readTime: 10,
      featured: false,
      tags: ['portfolio', 'web development', 'career', 'showcase']
    },
    {
      id: 6,
      title: 'Website Performance Optimization: Speed Up Your Site',
      slug: 'website-performance-optimization-guide',
      excerpt: 'Learn practical strategies to improve your website\'s loading speed and performance for better user experience and SEO rankings.',
      image: 'https://images.unsplash.com/photo-1562577309-4932fdd64cd1?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1174&q=80',
      category: 'Performance',
      date: 'March 25, 2025',
      readTime: 8,
      featured: false,
      tags: ['performance', 'optimization', 'SEO', 'user experience']
    },
    {
      id: 7,
      title: 'Creating Stunning Animations with GSAP and Framer Motion',
      slug: 'animations-gsap-framer-motion',
      excerpt: 'Master the art of web animations using GSAP and Framer Motion to create engaging and interactive user experiences.',
      image: 'https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=800&h=400&fit=crop',
      category: 'Design',
      date: 'April 5, 2025',
      readTime: 12,
      featured: false,
      tags: ['animations', 'GSAP', 'Framer Motion', 'interactive design']
    },
    {
      id: 8,
      title: 'The Business Case for Investing in Quality Web Development',
      slug: 'business-case-quality-web-development',
      excerpt: 'Understand how investing in quality web development can drive business growth, improve customer satisfaction, and increase ROI.',
      image: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
      category: 'Business',
      date: 'February 10, 2025',
      readTime: 7,
      featured: false,
      tags: ['business strategy', 'ROI', 'web development', 'investment']
    }
  ];

  // Get featured post
  const featuredPost = blogPosts.find(post => post.featured);

  // Get regular posts filtered by category
  const filteredPosts = blogPosts.filter(post =>
    !post.featured && (activeCategory === 'All' || post.category === activeCategory)
  );

  return (
    <section className="py-32 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Delta Xero <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary-400 to-primary-600">Blog</span>
            </h1>
            <p className="text-xl text-gray-300">
              Insights, tips, and stories about web development, design, and digital innovation.
            </p>
          </motion.div>

          {/* Categories */}
          <motion.div
            className="flex flex-wrap gap-2 justify-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {categories.map((category) => (
              <button
                key={category}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  activeCategory === category
                    ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white'
                    : 'bg-dark-800/50 text-gray-300 hover:bg-dark-700/50'
                }`}
                onClick={() => setActiveCategory(category)}
              >
                {category}
              </button>
            ))}
          </motion.div>

          {/* Featured Post */}
          {featuredPost && activeCategory === 'All' && (
            <Link to={`/blog/${featuredPost.slug}`}>
              <FeaturedPost post={featuredPost} />
            </Link>
          )}

          {/* Blog Posts Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPosts.map(post => (
              <Link key={post.id} to={`/blog/${post.slug}`}>
                <BlogCard post={post} />
              </Link>
            ))}
          </div>

          {/* Newsletter Signup */}
          <motion.div
            className="mt-20 text-center bg-dark-800/30 backdrop-blur-sm border border-gray-700 rounded-2xl p-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Stay <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Updated</span>
            </h2>
            <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
              Subscribe to our newsletter and get the latest insights on web development, design trends, and business growth delivered to your inbox.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-4 py-3 bg-dark-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-primary-500 focus:outline-none"
              />
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg hover:shadow-primary-500/25 transition-all duration-300"
              >
                Subscribe
              </motion.button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Blog
