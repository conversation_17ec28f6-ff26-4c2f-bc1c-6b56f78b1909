import React, { useEffect, useRef, useState } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import {
  MessageSquare,
  ArrowRight,
  CheckCircle,
  Code,
  Smartphone,
  Palette,
  Zap,
  Globe,
  Users,
  DollarSign,
  Send,
  Star,
  Shield,
  Clock,
  Rocket
} from 'lucide-react'
import { quoteRequestAPI } from '../lib/api'

gsap.registerPlugin(ScrollTrigger)

const GetQuote = () => {
  const heroRef = useRef(null)

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    message: '',
    services: []
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [errors, setErrors] = useState({})

  useEffect(() => {
    // Hero animation
    gsap.fromTo(heroRef.current?.children,
      { y: 100, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1.2,
        stagger: 0.2,
        ease: 'power3.out'
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const services = [
    // Core Services (from home page)
    { id: 'digital-platforms', name: 'Digital Experience Platforms', icon: Code, color: 'from-blue-500 to-cyan-500' },
    { id: 'mobile-engineering', name: 'Mobile Engineering', icon: Smartphone, color: 'from-purple-500 to-pink-500' },
    { id: 'experience-design', name: 'Experience Design', icon: Palette, color: 'from-green-500 to-teal-500' },
    { id: 'performance-optimization', name: 'Performance Optimization', icon: Zap, color: 'from-yellow-500 to-orange-500' },

    // Additional Services (from services page)
    { id: 'web-development', name: 'Web Development', icon: Globe, color: 'from-emerald-500 to-blue-500' },
    { id: 'mobile-development', name: 'Mobile Development', icon: Smartphone, color: 'from-purple-500 to-indigo-500' },
    { id: 'ui-ux-design', name: 'UI/UX Design', icon: Palette, color: 'from-pink-500 to-rose-500' },
    { id: 'api-development', name: 'API Development', icon: Code, color: 'from-cyan-500 to-blue-500' },
    { id: 'brand-strategy', name: 'Brand Strategy', icon: Star, color: 'from-orange-500 to-red-500' },
    { id: 'digital-transformation', name: 'Digital Transformation', icon: ArrowRight, color: 'from-teal-500 to-green-500' },

    // Industry Solutions
    { id: 'ecommerce', name: 'E-commerce Solutions', icon: Globe, color: 'from-blue-500 to-purple-500' },
    { id: 'saas', name: 'SaaS Platforms', icon: Code, color: 'from-green-500 to-blue-500' },
    { id: 'healthcare', name: 'Healthcare Solutions', icon: Shield, color: 'from-red-500 to-pink-500' },
    { id: 'fintech', name: 'Fintech Applications', icon: DollarSign, color: 'from-yellow-500 to-green-500' },
    { id: 'education', name: 'Education Platforms', icon: Users, color: 'from-indigo-500 to-purple-500' },
    { id: 'banking', name: 'Banking Solutions', icon: Shield, color: 'from-gray-500 to-blue-500' }
  ]






  const handleFormChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleServiceToggle = (serviceId) => {
    setFormData(prev => ({
      ...prev,
      services: prev.services.includes(serviceId)
        ? prev.services.filter(id => id !== serviceId)
        : [...prev.services, serviceId]
    }))
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.name?.trim()) {
      newErrors.name = 'Name is required'
    }

    if (!formData.email?.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!formData.message?.trim()) {
      newErrors.message = 'Project details are required'
    }

    if (!formData.services || formData.services.length === 0) {
      newErrors.services = 'Please select at least one service'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    setErrors({})

    try {
      const { data, error } = await quoteRequestAPI.submit(formData)

      if (error) {
        throw new Error(error)
      }

      setSubmitSuccess(true)
      setFormData({
        name: '',
        email: '',
        company: '',
        phone: '',
        message: '',
        services: []
      })

      // Show success message for 5 seconds
      setTimeout(() => {
        setSubmitSuccess(false)
      }, 5000)

    } catch (error) {
      console.error('Error submitting quote request:', error)
      setErrors({ submit: `Error submitting request: ${error.message}. Please try again.` })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen"
    >
      {/* Modern Hero Section */}
      <section className="relative -mt-16 lg:-mt-20 pt-32 pb-16 bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-primary-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-blue-500/8 rounded-full blur-3xl"></div>
        </div>

        {/* Grid Pattern */}
        <div className="absolute inset-0 opacity-[0.02]">
          <div className="absolute inset-0" style={{
            backgroundImage: `linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px)`,
            backgroundSize: '50px 50px'
          }}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div ref={heroRef}>
              <motion.div
                className="inline-flex items-center bg-primary-500/10 border border-primary-500/20 rounded-full px-4 py-2 mb-6"
                whileHover={{ scale: 1.05 }}
              >
                <MessageSquare className="text-primary-400 mr-2" size={14} />
                <span className="text-primary-400 text-xs font-medium uppercase tracking-wider">Get Quote</span>
              </motion.div>

              <h1 className="text-4xl md:text-6xl font-black text-white mb-6 leading-tight">
                Let's Build Your
                <br />
                <span className="bg-gradient-to-r from-primary-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent">
                  Next Big Thing
                </span>
              </h1>

              <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                Ready to transform your ideas into reality? Get a personalized quote for your project.
                Our team of experts will analyze your requirements and provide you with a detailed proposal.
              </p>

              {/* Quick Stats */}
              <div className="grid grid-cols-3 gap-6 mb-8">
                {[
                  { number: '24h', label: 'Response Time' },
                  { number: '200+', label: 'Projects Done' },
                  { number: '98%', label: 'Success Rate' }
                ].map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-2xl font-bold text-white mb-1">{stat.number}</div>
                    <div className="text-sm text-gray-400">{stat.label}</div>
                  </div>
                ))}
              </div>

              {/* Trust Badges */}
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <Shield className="text-green-400" size={16} />
                  <span className="text-sm text-gray-300">Free Consultation</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Star className="text-yellow-400" size={16} />
                  <span className="text-sm text-gray-300">5-Star Rated</span>
                </div>
              </div>
            </div>

            {/* Right Content - Benefits */}
            <div className="lg:pl-8">
              <motion.div
                initial={{ x: 50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.8 }}
                className="space-y-8"
              >
                {/* Process Steps */}
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-white mb-6">Our Simple Process</h3>
                  {[
                    { step: '01', title: 'Share Your Vision', desc: 'Tell us about your project goals and requirements' },
                    { step: '02', title: 'Get Your Quote', desc: 'Receive a detailed proposal within 24 hours' },
                    { step: '03', title: 'Start Building', desc: 'Begin your project with our expert team' }
                  ].map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ x: 30, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ duration: 0.6, delay: index * 0.2 }}
                      className="flex items-start space-x-4"
                    >
                      <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-blue-500 rounded-xl flex items-center justify-center font-bold text-white">
                        {item.step}
                      </div>
                      <div>
                        <h4 className="text-white font-semibold mb-1">{item.title}</h4>
                        <p className="text-gray-400 text-sm">{item.desc}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Why Choose Us */}
                <div className="bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                  <h4 className="text-white font-bold mb-4">Why Choose Delta Xero?</h4>
                  <div className="space-y-3">
                    {[
                      '✨ Award-winning design team',
                      '🚀 Latest technology stack',
                      '⚡ 24-hour response guarantee',
                      '🔒 100% confidential process',
                      '💬 Free consultation included'
                    ].map((benefit, index) => (
                      <div key={index} className="text-gray-300 text-sm">{benefit}</div>
                    ))}
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>



      {/* Contact Form Section */}
      <section id="contact-form" className="py-20 bg-dark-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-primary-500/10 border border-primary-500/20 rounded-full px-4 py-2 mb-6">
              <Send className="text-primary-400 mr-2" size={14} />
              <span className="text-primary-400 text-xs font-medium uppercase tracking-wider">Project Details</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-black text-white mb-6 leading-tight">
              Tell Us About Your
              <br />
              <span className="bg-gradient-to-r from-primary-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent">
                Dream Project
              </span>
            </h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
              Share your vision with us. The more details you provide, the more accurate and comprehensive your quote will be.
            </p>
          </div>

          <motion.div
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 rounded-3xl p-8 md:p-12"
          >
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleFormChange}
                    required
                    className={`w-full px-4 py-4 bg-white/5 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 ${
                      errors.name
                        ? 'border-red-500 focus:border-red-400 focus:ring-red-400/20'
                        : 'border-white/20 focus:border-primary-400 focus:ring-primary-400/20'
                    }`}
                    placeholder="Your full name"
                  />
                  {errors.name && <p className="text-red-400 text-sm mt-2">{errors.name}</p>}
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleFormChange}
                    required
                    className={`w-full px-4 py-4 bg-white/5 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 ${
                      errors.email
                        ? 'border-red-500 focus:border-red-400 focus:ring-red-400/20'
                        : 'border-white/20 focus:border-primary-400 focus:ring-primary-400/20'
                    }`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && <p className="text-red-400 text-sm mt-2">{errors.email}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-gray-300 mb-2">
                    Company Name
                  </label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    value={formData.company}
                    onChange={handleFormChange}
                    className="w-full px-4 py-4 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                    placeholder="Your company"
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleFormChange}
                    className="w-full px-4 py-4 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                    placeholder="+****************"
                  />
                </div>
              </div>

              {/* Services Selection - Bento Grid */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-6">
                  Services Needed *
                </label>

                {/* Service Selection Bento Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 auto-rows-[140px] mb-4">
                  {services.map((service, index) => {
                    const Icon = service.icon
                    const isSelected = formData.services?.includes(service.id)

                    // Define layout for services (9 services total)
                    const getServiceLayout = (idx) => {
                      const layouts = [
                        'col-span-1 md:col-span-2 lg:col-span-2 row-span-2', // Hero - Full-Stack Development
                        'col-span-1 row-span-1', // Mobile App
                        'col-span-1 row-span-1', // UI/UX Design
                        'col-span-1 md:col-span-2 lg:col-span-2 row-span-1', // Wide - Performance Optimization
                        'col-span-1 row-span-1', // E-commerce
                        'col-span-1 row-span-1', // SaaS
                        'col-span-1 row-span-1', // Healthcare
                        'col-span-1 row-span-1', // Fintech
                        'col-span-1 md:col-span-2 lg:col-span-2 row-span-1', // Wide - Education & Banking
                      ]
                      return layouts[idx] || 'col-span-1 row-span-1'
                    }

                    const layoutClass = getServiceLayout(index)
                    const isHero = index === 0
                    const isWide = index === 3 || index === 8

                    return (
                      <motion.div
                        key={service.id}
                        initial={{ opacity: 0, y: 20, scale: 0.9 }}
                        whileInView={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{ duration: 0.6, delay: index * 0.1 }}
                        viewport={{ once: true }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => handleServiceToggle(service.id)}
                        className={`${layoutClass} cursor-pointer group relative overflow-hidden rounded-2xl border-2 transition-all duration-300 ${
                          isSelected
                            ? 'border-primary-400 bg-primary-500/20 shadow-lg shadow-primary-500/20'
                            : 'border-white/10 bg-gradient-to-br from-dark-800/50 to-dark-900/50 backdrop-blur-xl hover:border-primary-400/50'
                        }`}
                      >
                        {/* Background Gradient */}
                        <div className={`absolute inset-0 bg-gradient-to-br opacity-10 group-hover:opacity-20 transition-opacity duration-700 ${service.color}`} />

                        {/* Content */}
                        <div className={`relative z-10 h-full flex flex-col justify-between ${isHero ? 'p-6' : 'p-4'}`}>
                          <div className="flex-1">
                            <div className={`rounded-xl bg-gradient-to-br flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-lg mb-3 ${service.color} ${
                              isHero ? 'w-12 h-12' : 'w-10 h-10'
                            }`}>
                              <Icon className="text-white" size={isHero ? 20 : 16} />
                            </div>

                            <h3 className={`font-bold text-white group-hover:text-primary-300 transition-colors duration-300 leading-tight ${
                              isHero ? 'text-lg mb-2' : isWide ? 'text-md mb-1' : 'text-sm mb-1'
                            }`}>
                              {service.name}
                            </h3>
                          </div>

                          {/* Selection Indicator */}
                          {isSelected && (
                            <div className="absolute top-3 right-3">
                              <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                                <CheckCircle className="text-white" size={14} />
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Hover Effect */}
                        <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700" />
                      </motion.div>
                    )
                  })}
                </div>
                {errors.services && <p className="text-red-400 text-sm mt-2">{errors.services}</p>}
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                  Project Details *
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleFormChange}
                  required
                  rows={6}
                  className={`w-full px-4 py-4 bg-white/5 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 resize-none ${
                    errors.message
                      ? 'border-red-500 focus:border-red-400 focus:ring-red-400/20'
                      : 'border-white/20 focus:border-primary-400 focus:ring-primary-400/20'
                  }`}
                  placeholder="Tell us more about your project requirements, goals, and any specific features you need..."
                />
                {errors.message && <p className="text-red-400 text-sm mt-2">{errors.message}</p>}
              </div>



              {/* Success Message */}
              {submitSuccess && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-green-500/20 border border-green-500/30 rounded-xl p-4 mb-6"
                >
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="text-green-400" size={20} />
                    <div>
                      <p className="text-green-400 font-medium">Quote request submitted successfully!</p>
                      <p className="text-green-300 text-sm">We'll get back to you within 24 hours with a detailed quote.</p>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Error Message */}
              {errors.submit && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-red-500/20 border border-red-500/30 rounded-xl p-4 mb-6"
                >
                  <p className="text-red-400">{errors.submit}</p>
                </motion.div>
              )}

              <motion.button
                type="submit"
                disabled={isSubmitting}
                whileHover={!isSubmitting ? { scale: 1.02, y: -2 } : {}}
                whileTap={!isSubmitting ? { scale: 0.98 } : {}}
                className={`w-full py-5 rounded-2xl font-bold text-lg flex items-center justify-center space-x-3 transition-all duration-300 relative overflow-hidden group ${
                  isSubmitting
                    ? 'bg-gray-600 cursor-not-allowed'
                    : 'bg-gradient-to-r from-primary-500 to-blue-500 text-white hover:shadow-2xl hover:shadow-primary-500/25'
                }`}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    <span className="relative z-10">Submitting...</span>
                  </>
                ) : (
                  <>
                    <span className="relative z-10">Get My Quote</span>
                    <ArrowRight className="relative z-10" size={20} />
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                  </>
                )}
              </motion.button>

              {/* Form Footer */}
              <p className="text-center text-gray-400 text-sm mt-6">
                By submitting this form, you agree to our terms of service and privacy policy.
                We'll respond within 24 hours with a detailed quote.
              </p>
            </form>
          </motion.div>
        </div>
      </section>



      {/* Trust Indicators */}
      <section className="py-16 bg-gradient-to-b from-dark-900 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            {[
              { icon: Users, number: '150+', label: 'Happy Clients' },
              { icon: Rocket, number: '300+', label: 'Projects Delivered' },
              { icon: Star, number: '4.9/5', label: 'Client Rating' },
              { icon: Clock, number: '24h', label: 'Response Time' }
            ].map((stat, index) => {
              const Icon = stat.icon
              return (
                <motion.div
                  key={index}
                  initial={{ y: 30, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-dark-800/30 p-6 rounded-xl border border-gray-700"
                >
                  <Icon className="text-primary-400 mx-auto mb-4" size={32} />
                  <div className="text-3xl font-bold text-white mb-2">{stat.number}</div>
                  <div className="text-gray-400">{stat.label}</div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>
    </motion.div>
  )
}

export default GetQuote
