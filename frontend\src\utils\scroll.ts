import { getSmoothScrollInstance } from '../hooks/useSmoothScroll';

/**
 * Scroll to a specific element or position using Lenis smooth scroll
 * @param target - Element selector, element, or scroll position (number)
 * @param options - Scroll options
 */
export const scrollTo = (
  target: string | Element | number,
  options: {
    duration?: number;
    easing?: (t: number) => number;
    offset?: number;
    immediate?: boolean;
  } = {}
) => {
  const lenis = getSmoothScrollInstance();
  
  if (lenis) {
    lenis.scrollTo(target, {
      duration: options.duration || 1.2,
      easing: options.easing,
      offset: options.offset || 0,
      immediate: options.immediate || false,
    });
  } else {
    // Fallback for when Lenis is not available
    if (typeof target === 'number') {
      window.scrollTo({
        top: target,
        behavior: options.immediate ? 'auto' : 'smooth'
      });
    } else if (typeof target === 'string') {
      const element = document.querySelector(target);
      if (element) {
        element.scrollIntoView({
          behavior: options.immediate ? 'auto' : 'smooth',
          block: 'start'
        });
      }
    } else if (target instanceof Element) {
      target.scrollIntoView({
        behavior: options.immediate ? 'auto' : 'smooth',
        block: 'start'
      });
    }
  }
};

/**
 * Scroll to top of the page
 * @param immediate - Whether to scroll immediately without animation
 */
export const scrollToTop = (immediate = false) => {
  scrollTo(0, { immediate, duration: 1.5 });
};

/**
 * Scroll to a specific section by ID
 * @param sectionId - The ID of the section to scroll to
 * @param offset - Offset from the top (useful for fixed headers)
 */
export const scrollToSection = (sectionId: string, offset = -80) => {
  scrollTo(`#${sectionId}`, { offset, duration: 1.2 });
};

/**
 * Stop Lenis scrolling
 */
export const stopScroll = () => {
  const lenis = getSmoothScrollInstance();
  if (lenis) {
    lenis.stop();
  }
};

/**
 * Start Lenis scrolling
 */
export const startScroll = () => {
  const lenis = getSmoothScrollInstance();
  if (lenis) {
    lenis.start();
  }
};

/**
 * Get current scroll position
 */
export const getScrollPosition = (): number => {
  const lenis = getSmoothScrollInstance();
  if (lenis) {
    return lenis.scroll;
  }
  return window.pageYOffset || document.documentElement.scrollTop;
};
