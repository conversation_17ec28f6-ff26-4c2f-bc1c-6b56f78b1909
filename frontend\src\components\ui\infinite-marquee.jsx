import React from 'react';
import { motion } from 'framer-motion';

const InfiniteMarquee = ({ children, speed = 50, direction = 'left', className = '' }) => {
  return (
    <div className={`overflow-hidden whitespace-nowrap ${className}`}>
      <motion.div
        className="inline-block"
        animate={{
          x: direction === 'left' ? ['0%', '-50%'] : ['-50%', '0%'],
        }}
        transition={{
          x: {
            repeat: Infinity,
            repeatType: 'loop',
            duration: speed,
            ease: 'linear',
          },
        }}
      >
        <div className="flex items-center">
          {children}
          {children}
        </div>
      </motion.div>
    </div>
  );
};

export default InfiniteMarquee;
