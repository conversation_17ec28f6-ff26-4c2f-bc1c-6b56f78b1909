import { FC, useRef, useState, useEffect, MutableRefObject } from "react";
import { mat4, quat, vec2, vec3 } from "gl-matrix";

// Import the existing classes and functions from the original component
// We'll reuse the WebGL implementation but modify the React wrapper

interface MenuItem {
  image: string;
  link: string;
  title: string;
  description: string;
}

interface PortfolioInfiniteMenuProps {
  items?: MenuItem[];
  onActiveItemChange?: (index: number) => void;
}

// We'll import the InfiniteGridMenu class from the original file
// For now, let's create a simplified version that works with our portfolio

const PortfolioInfiniteMenu: FC<PortfolioInfiniteMenuProps> = ({ 
  items = [], 
  onActiveItemChange 
}) => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null) as MutableRefObject<HTMLCanvasElement | null>;
  const [activeItem, setActiveItem] = useState<MenuItem | null>(null);
  const [isMoving, setIsMoving] = useState<boolean>(false);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !items.length) return;

    // Import the InfiniteGridMenu class dynamically
    import('./ui/Infinitemenu').then(({ Component }) => {
      // This is a workaround since we can't easily modify the original component
      // We'll use the existing component but try to capture its state changes
    });

    const handleActiveItem = (index: number) => {
      if (!items.length) return;
      const itemIndex = index % items.length;
      setActiveItem(items[itemIndex]);
      if (onActiveItemChange) {
        onActiveItemChange(itemIndex);
      }
    };

    // Set initial active item
    if (items.length > 0) {
      setActiveItem(items[0]);
      if (onActiveItemChange) {
        onActiveItemChange(0);
      }
    }

    // Cleanup function
    return () => {
      // Cleanup if needed
    };
  }, [items, onActiveItemChange]);

  return (
    <div className="relative w-full h-full">
      <canvas
        ref={canvasRef}
        className="cursor-grab w-full h-full overflow-hidden relative outline-none active:cursor-grabbing"
        style={{ background: 'transparent' }}
      />

      {/* Drag instruction overlay */}
      <div className="absolute inset-0 pointer-events-none">
        <div className={`
          absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
          transition-all duration-300 ease-in-out
          ${isMoving ? 'opacity-0 scale-95' : 'opacity-100 scale-100'}
        `}>
          <div className="bg-dark-800/80 backdrop-blur-sm border border-primary-500/30 rounded-xl p-4 text-center">
            <div className="text-primary-400 text-sm font-medium mb-2">
              🎯 Interactive Portfolio
            </div>
            <div className="text-gray-300 text-xs">
              Drag to explore projects
            </div>
          </div>
        </div>
      </div>

      {/* Project preview overlay */}
      {activeItem && (
        <div className="absolute bottom-4 left-4 right-4 pointer-events-none">
          <div className={`
            bg-dark-800/90 backdrop-blur-sm border border-gray-700 rounded-lg p-4
            transition-all duration-300 ease-in-out
            ${isMoving ? 'opacity-0 translate-y-4' : 'opacity-100 translate-y-0'}
          `}>
            <h4 className="text-white font-semibold text-sm mb-1">{activeItem.title}</h4>
            <p className="text-gray-400 text-xs line-clamp-2">{activeItem.description}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default PortfolioInfiniteMenu;
