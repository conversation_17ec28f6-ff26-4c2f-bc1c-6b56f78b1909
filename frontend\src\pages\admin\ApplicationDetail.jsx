import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useParams } from 'react-router-dom'
import AdminNavigation from '../../components/AdminNavigation'
import { 
  ArrowLeft,
  Mail,
  Phone,
  MapPin,
  Calendar,
  GraduationCap,
  Briefcase,
  ExternalLink,
  Download,
  Star,
  Check,
  X,
  MessageSquare,
  User,
  Award,
  BookOpen,
  Globe
} from 'lucide-react'

const ApplicationDetail = () => {
  const { id } = useParams()
  
  // Application data - in production, this would be fetched from API based on ID
  const application = {
    id: 1,
    name: 'khushboo joshi',
    position: 'frontend-intern',
    email: '<EMAIL>',
    phone: '9929855657',
    location: 'Jaipur',
    university: 'JECRC university',
    year: '4',
    major: 'Computer Science',
    portfolio: 'https://github.com/joshikhush',
    linkedin: 'https://www.linkedin.com/in/khushboo-joshi-03953a224/',
    status: 'New',
    appliedDate: '2024-01-20',
    coverLetter: `I'm genuinely excited about the opportunity to intern at Intelermate because your mission—bridging the gap between students and startups—really resonates with me. As someone who is stepping into the tech world myself, being part of a team that's helping others grow their careers feels meaningful.

What makes me a good fit is my hands-on experience with the MERN stack and my strong interest in building clean, scalable web applications. I've worked on projects that involved both frontend and backend development, and I enjoy turning ideas into intuitive user experiences.

Beyond the technical skills, I bring curiosity, commitment, and a mindset to learn. Working at a growth-driven startup like Intelermate would not only allow me to contribute meaningfully but also help me grow in ways that align with my career goals.`,
    skills: ['React', 'JavaScript', 'Node.js', 'MongoDB', 'HTML/CSS'],
    experience: 'Student',
    gpa: '3.8'
  }

  const [status, setStatus] = useState(application.status)
  const [notes, setNotes] = useState('')

  const handleStatusChange = (newStatus) => {
    setStatus(newStatus)
    // In real app, make API call to update status
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'New': return 'bg-purple-600 text-white'
      case 'Pending': return 'bg-blue-600 text-white'
      case 'Accepted': return 'bg-green-600 text-white'
      case 'Rejected': return 'bg-red-600 text-white'
      default: return 'bg-gray-600 text-white'
    }
  }

  return (
    <div className="min-h-screen bg-dark-950">
      <AdminNavigation />
      <div className="pt-20 pb-20 md:pb-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-white transition-colors duration-300">
                <ArrowLeft size={24} />
              </button>
              <div>
                <h1 className="text-3xl font-bold text-white">{application.name}</h1>
                <p className="text-gray-400">{application.position}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className={`px-4 py-2 rounded-full text-sm font-medium ${getStatusColor(status)}`}>
                {status}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Contact Information */}
              <div className="bg-dark-800/50 border border-gray-700/50 rounded-2xl p-6">
                <h2 className="text-xl font-bold text-white mb-6">Contact Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Mail className="text-gray-400" size={20} />
                      <div>
                        <p className="text-gray-400 text-sm">Email</p>
                        <p className="text-white">{application.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Phone className="text-gray-400" size={20} />
                      <div>
                        <p className="text-gray-400 text-sm">Phone</p>
                        <p className="text-white">{application.phone}</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <MapPin className="text-gray-400" size={20} />
                      <div>
                        <p className="text-gray-400 text-sm">Location</p>
                        <p className="text-white">{application.location}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Calendar className="text-gray-400" size={20} />
                      <div>
                        <p className="text-gray-400 text-sm">Applied Date</p>
                        <p className="text-white">{new Date(application.appliedDate).toLocaleDateString()}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Academic Information */}
              <div className="bg-dark-800/50 border border-gray-700/50 rounded-2xl p-6">
                <h2 className="text-xl font-bold text-white mb-6">Academic Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <GraduationCap className="text-gray-400" size={20} />
                      <div>
                        <p className="text-gray-400 text-sm">University</p>
                        <p className="text-white">{application.university}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <BookOpen className="text-gray-400" size={20} />
                      <div>
                        <p className="text-gray-400 text-sm">Major</p>
                        <p className="text-white">{application.major}</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Award className="text-gray-400" size={20} />
                      <div>
                        <p className="text-gray-400 text-sm">Year of Study</p>
                        <p className="text-white">{application.year}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Star className="text-gray-400" size={20} />
                      <div>
                        <p className="text-gray-400 text-sm">GPA</p>
                        <p className="text-white">{application.gpa}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Links */}
              <div className="bg-dark-800/50 border border-gray-700/50 rounded-2xl p-6">
                <h2 className="text-xl font-bold text-white mb-6">Links</h2>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Globe className="text-gray-400" size={20} />
                    <div>
                      <p className="text-gray-400 text-sm">Portfolio</p>
                      <a 
                        href={application.portfolio} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-300 transition-colors duration-300 flex items-center space-x-1"
                      >
                        <span>{application.portfolio}</span>
                        <ExternalLink size={16} />
                      </a>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <User className="text-gray-400" size={20} />
                    <div>
                      <p className="text-gray-400 text-sm">LinkedIn</p>
                      <a 
                        href={application.linkedin} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-300 transition-colors duration-300 flex items-center space-x-1"
                      >
                        <span>{application.linkedin}</span>
                        <ExternalLink size={16} />
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Cover Letter */}
              <div className="bg-dark-800/50 border border-gray-700/50 rounded-2xl p-6">
                <h2 className="text-xl font-bold text-white mb-6">Cover Letter</h2>
                <div className="prose prose-invert max-w-none">
                  <p className="text-gray-300 leading-relaxed whitespace-pre-line">
                    {application.coverLetter}
                  </p>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Skills */}
              <div className="bg-dark-800/50 border border-gray-700/50 rounded-2xl p-6">
                <h3 className="text-lg font-bold text-white mb-4">Skills</h3>
                <div className="flex flex-wrap gap-2">
                  {application.skills.map((skill, index) => (
                    <span
                      key={index}
                      className="bg-purple-500/20 text-purple-400 px-3 py-1 rounded-lg text-sm"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="bg-dark-800/50 border border-gray-700/50 rounded-2xl p-6">
                <h3 className="text-lg font-bold text-white mb-4">Actions</h3>
                <div className="space-y-3">
                  <button
                    onClick={() => handleStatusChange('Accepted')}
                    className="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-xl font-medium transition-all duration-300 flex items-center justify-center space-x-2"
                  >
                    <Check size={16} />
                    <span>Accept Application</span>
                  </button>
                  <button
                    onClick={() => handleStatusChange('Rejected')}
                    className="w-full bg-red-600 hover:bg-red-700 text-white py-3 rounded-xl font-medium transition-all duration-300 flex items-center justify-center space-x-2"
                  >
                    <X size={16} />
                    <span>Reject Application</span>
                  </button>
                </div>
              </div>

              {/* Notes */}
              <div className="bg-dark-800/50 border border-gray-700/50 rounded-2xl p-6">
                <h3 className="text-lg font-bold text-white mb-4">Notes</h3>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Add your notes about this candidate..."
                  rows={4}
                  className="w-full bg-dark-700/50 border border-gray-600/30 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/20 transition-all duration-300 resize-none"
                />
                <button className="w-full mt-3 bg-purple-600 hover:bg-purple-700 text-white py-2 rounded-lg font-medium transition-all duration-300">
                  Save Notes
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ApplicationDetail
