import React from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useAuth } from '../contexts/AuthContext'
import {
  LayoutDashboard,
  FileText,
  Settings,
  LogOut,
  Bell,
  Search,
  MessageSquare,
  User
} from 'lucide-react'

const AdminNavigation = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const { user, logout } = useAuth()

  const handleLogout = () => {
    if (window.confirm('Are you sure you want to logout?')) {
      logout()
      navigate('/admin/login', { replace: true })
    }
  }

  const navItems = [
    {
      name: 'Dashboard',
      href: '/admin/dashboard',
      icon: LayoutDashboard,
      description: 'Overview and analytics'
    },
    {
      name: 'Applications',
      href: '/admin/applications',
      icon: FileText,
      description: 'Review applications'
    },
    {
      name: 'Quotes',
      href: '/admin/quotes',
      icon: MessageSquare,
      description: 'Manage quote requests'
    },
    {
      name: 'Users',
      href: '/admin/users',
      icon: Settings,
      description: 'Manage system users'
    }
  ]

  const isActive = (href) => {
    return location.pathname === href || (href === '/admin/dashboard' && location.pathname === '/admin')
  }

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-dark-950/95 backdrop-blur-xl border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/admin/dashboard" className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-blue-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">DX</span>
            </div>
            <div>
              <span className="text-white font-bold text-lg">Admin Panel</span>
              <div className="text-xs text-gray-400">Delta Xero Creations</div>
            </div>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => {
              const Icon = item.icon
              const active = isActive(item.href)
              
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className="relative group"
                >
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-300 ${
                      active
                        ? 'bg-primary-500 text-white shadow-lg shadow-primary-500/25'
                        : 'text-gray-300 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <Icon size={18} />
                    <span className="font-medium">{item.name}</span>
                  </motion.div>
                  
                  {/* Tooltip */}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-dark-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
                    {item.description}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-dark-800"></div>
                  </div>
                </Link>
              )
            })}
          </nav>

          {/* Right Side */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <button className="p-2 text-gray-400 hover:text-white transition-colors duration-300">
              <Search size={20} />
            </button>

            {/* Notifications */}
            <button className="relative p-2 text-gray-400 hover:text-white transition-colors duration-300">
              <Bell size={20} />
              <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>

            {/* Settings */}
            <button className="p-2 text-gray-400 hover:text-white transition-colors duration-300">
              <Settings size={20} />
            </button>

            {/* User Info */}
            <div className="flex items-center space-x-3 px-3 py-2 bg-white/5 rounded-xl">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                <User size={16} className="text-white" />
              </div>
              <div className="hidden sm:block">
                <p className="text-white text-sm font-medium">{user?.username || 'Admin'}</p>
                <p className="text-gray-400 text-xs capitalize">{user?.role || 'admin'}</p>
              </div>
            </div>

            {/* Logout */}
            <button
              onClick={handleLogout}
              className="flex items-center space-x-2 px-4 py-2 bg-red-500/20 text-red-400 rounded-xl hover:bg-red-500/30 transition-all duration-300"
            >
              <LogOut size={16} />
              <span className="hidden sm:inline">Logout</span>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navigation - Fixed at bottom */}
      <div
        className="block md:hidden fixed bottom-0 left-0 right-0 w-full z-[9999]"
        style={{
          position: 'fixed',
          bottom: '0px',
          left: '0px',
          right: '0px',
          zIndex: 9999
        }}
      >
        <div className="bg-black border-t-2 border-purple-500 px-6 py-4 w-full shadow-2xl">
          <div className="flex items-center justify-between max-w-sm mx-auto">
            {/* Help/Info */}
            <button className="w-11 h-11 rounded-full bg-gray-700/60 flex items-center justify-center text-gray-400 hover:text-white transition-all duration-300">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
            </button>

            {/* Settings */}
            <Link
              to="/admin/users"
              className="w-11 h-11 rounded-full bg-gray-700/60 flex items-center justify-center text-gray-400 hover:text-white transition-all duration-300"
            >
              <Settings size={20} />
            </Link>

            {/* Applications */}
            <Link
              to="/admin/applications"
              className="w-11 h-11 rounded-full bg-purple-600 flex items-center justify-center text-white shadow-lg transition-all duration-300"
            >
              <FileText size={20} />
            </Link>

            {/* Messages */}
            <button className="w-11 h-11 rounded-full bg-gray-700/60 flex items-center justify-center text-gray-400 hover:text-white transition-all duration-300">
              <MessageSquare size={20} />
            </button>

            {/* Users Profile */}
            <Link
              to="/admin/users"
              className="w-12 h-12 rounded-full bg-purple-600 flex items-center justify-center text-white shadow-lg transition-all duration-300"
            >
              <User size={24} />
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminNavigation
