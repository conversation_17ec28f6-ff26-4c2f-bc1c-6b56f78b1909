import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import SolutionsBentoGrid from '../components/ui/solutions-bento-grid'
import { Palette, Users, Eye, Zap, Smartphone, Monitor, ArrowRight, CheckCircle, Star, Layers, Rocket, TrendingUp, BarChart3, Gauge, Target, Figma, Brush, MousePointer } from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const UIUXDesign = () => {
  const statsRef = useRef(null)

  useEffect(() => {
    // Stats counter animation
    gsap.fromTo('.stat-counter',
      { textContent: 0 },
      {
        textContent: (i, target) => target.getAttribute('data-value'),
        duration: 2,
        ease: 'power2.out',
        snap: { textContent: 1 },
        scrollTrigger: {
          trigger: statsRef.current,
          start: 'top 80%',
        }
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const designServices = [
    {
      icon: Eye,
      title: 'User Experience Strategy',
      description: 'Research-driven UX design that creates intuitive, conversion-focused experiences backed by data and user insights.',
      features: ['User Research & Analytics', 'Information Architecture', 'User Journey Optimization', 'Conversion Rate Design'],
      color: 'from-blue-500 to-cyan-500',
      price: 'From $5,000',
      timeline: '4-8 weeks',
      metrics: { conversion: '+85% increase', satisfaction: '4.9/5 rating', retention: '+60% improvement' }
    },
    {
      icon: Palette,
      title: 'Visual Interface Design',
      description: 'Beautiful, modern interfaces that align with your brand and provide exceptional visual experiences across all devices.',
      features: ['Design System Creation', 'Brand Integration', 'Responsive Design', 'Interactive Prototyping'],
      color: 'from-purple-500 to-pink-500',
      price: 'From $4,000',
      timeline: '3-6 weeks',
      metrics: { engagement: '+70% increase', bounce: '-45% reduction', time: '+120% on page' }
    },
    {
      icon: Smartphone,
      title: 'Mobile App Design',
      description: 'Native mobile app designs optimized for iOS and Android with platform-specific guidelines and user behaviors.',
      features: ['Platform-Specific Design', 'Gesture-Based Interactions', 'Accessibility Compliance', 'App Store Optimization'],
      color: 'from-green-500 to-emerald-500',
      price: 'From $6,000',
      timeline: '5-10 weeks',
      metrics: { downloads: '2M+ installs', rating: '4.8★ average', retention: '75% day-7' }
    },
    {
      icon: Monitor,
      title: 'Web Application Design',
      description: 'Complex web application interfaces designed for productivity, usability, and scalability across enterprise environments.',
      features: ['Dashboard Design', 'Data Visualization', 'Workflow Optimization', 'Enterprise Integration'],
      color: 'from-orange-500 to-red-500',
      price: 'From $8,000',
      timeline: '6-12 weeks',
      metrics: { productivity: '+90% efficiency', errors: '-65% reduction', adoption: '95% user rate' }
    }
  ]

  const stats = [
    { number: 300, label: 'Design Projects', suffix: '+', icon: Palette, color: 'from-blue-500 to-cyan-500' },
    { number: 4.9, label: 'Client Rating', suffix: '/5', icon: Star, color: 'from-yellow-500 to-orange-500' },
    { number: 85, label: 'Avg Conversion Increase', suffix: '%', icon: TrendingUp, color: 'from-green-500 to-emerald-500' },
    { number: 99, label: 'Client Satisfaction', suffix: '%', icon: Target, color: 'from-purple-500 to-pink-500' }
  ]

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen"
    >
      {/* Hero Section */}
      <section className="relative py-32 bg-gradient-to-b from-dark-950 via-dark-900 to-dark-950 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              className="inline-flex items-center bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-full px-6 py-3 mb-8"
            >
              <div className="w-2 h-2 bg-purple-400 rounded-full mr-3 animate-pulse"></div>
              <Palette className="w-4 h-4 text-purple-400 mr-2" />
              <span className="text-purple-400 text-sm font-semibold uppercase tracking-wider">UI/UX Design Excellence</span>
            </motion.div>
            
            <motion.h1 
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-5xl sm:text-6xl md:text-7xl font-black text-white mb-8 leading-none"
            >
              DESIGN THAT
              <br />
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
                CONVERTS
              </span>
            </motion.h1>
            
            <motion.p 
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-xl sm:text-2xl text-gray-300 max-w-4xl mx-auto mb-12 leading-relaxed"
            >
              Create exceptional user experiences that drive engagement, increase conversions, 
              and build lasting relationships with your customers.
            </motion.p>
            
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex justify-center mb-16"
            >
              <Link to="/get-quote">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-12 py-4 rounded-2xl font-bold text-lg hover:shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 flex items-center justify-center space-x-3"
                >
                  <span>Schedule Consultation</span>
                  <ArrowRight className="w-5 h-5" />
                </motion.button>
              </Link>
            </motion.div>

            {/* Key Features */}
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
            >
              {[
                { icon: Eye, text: '85% Conversion Increase', color: 'text-purple-400' },
                { icon: Users, text: '4.9/5 Client Rating', color: 'text-pink-400' },
                { icon: Gauge, text: '300+ Projects Delivered', color: 'text-cyan-400' }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-center space-x-3 text-gray-300">
                  <item.icon className={item.color} size={20} />
                  <span className="font-medium">{item.text}</span>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-32 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div 
              className="inline-flex items-center bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Rocket className="text-purple-400 mr-3" size={16} />
              <span className="text-purple-400 text-sm font-semibold uppercase tracking-wider">Design Services</span>
            </motion.div>
            
            <h2 className="text-5xl md:text-6xl font-black text-white mb-8 leading-tight">
              UI/UX DESIGN
              <br />
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
                SERVICES
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
              Transform your digital presence with design that not only looks beautiful but drives real business results
            </p>
          </motion.div>

          <SolutionsBentoGrid services={designServices} />
        </div>
      </section>

      {/* Stats Section */}
      <section ref={statsRef} className="py-32 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-black text-white mb-4">
              PROVEN <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">RESULTS</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Numbers that speak to our commitment to design excellence and client success
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group"
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <div className="bg-gradient-to-br from-black/60 to-black/30 backdrop-blur-xl border border-white/20 p-8 rounded-3xl text-center group-hover:border-white/30 transition-all duration-500 relative overflow-hidden"
                    style={{ backdropFilter: 'blur(20px) saturate(180%)' }}
                  >
                    {/* Background Gradient */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-5 group-hover:opacity-15 transition-opacity duration-500`}></div>

                    {/* Content */}
                    <div className="relative z-10">
                      {/* Icon */}
                      <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg`}>
                        <Icon className="text-white" size={24} />
                      </div>

                      {/* Number */}
                      <div className="text-4xl md:text-5xl font-black text-white mb-3 group-hover:text-purple-300 transition-colors duration-300">
                        <span className="stat-counter" data-value={stat.number}>0</span>
                        {stat.suffix}
                      </div>

                      {/* Label */}
                      <div className="text-gray-400 font-medium text-lg">{stat.label}</div>
                    </div>

                    {/* Hover Glow Effect */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl`}></div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>
    </motion.div>
  )
}

export default UIUXDesign
