import React from 'react';
import { GooeyTextDemo } from '@/components/ui/gooey-text-demo';
import { GooeyText } from '@/components/ui/gooey-text-morphing';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const GooeyTextDemoPage = () => {
  return (
    <div className="min-h-screen bg-dark-950">
      {/* Navigation */}
      <div className="fixed top-4 left-4 z-50">
        <Link 
          to="/" 
          className="flex items-center gap-2 px-4 py-2 bg-dark-800/80 backdrop-blur-sm border border-gray-700 rounded-lg text-white hover:text-primary-400 transition-colors duration-300"
        >
          <ArrowLeft size={16} />
          Back to Home
        </Link>
      </div>

      {/* Hero Section */}
      <section className="h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-8">Gooey Text Morphing Demo</h1>
          <GooeyTextDemo />
        </div>
      </section>

      {/* Custom Examples */}
      <section className="py-20 px-10 bg-dark-900">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Custom Examples</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Different configurations of the gooey text morphing effect with various timings and content.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Fast Morphing */}
            <div className="bg-dark-800 rounded-lg p-8">
              <h3 className="text-xl font-bold text-white mb-6 text-center">Fast Morphing</h3>
              <div className="h-32 flex items-center justify-center">
                <GooeyText
                  texts={["Fast", "Quick", "Rapid", "Swift"]}
                  morphTime={0.5}
                  cooldownTime={0.1}
                  className="font-bold"
                  textClassName="text-2xl md:text-4xl text-primary-400"
                />
              </div>
              <div className="mt-4 text-center">
                <code className="text-sm text-gray-400">morphTime: 0.5s, cooldownTime: 0.1s</code>
              </div>
            </div>

            {/* Slow Morphing */}
            <div className="bg-dark-800 rounded-lg p-8">
              <h3 className="text-xl font-bold text-white mb-6 text-center">Slow Morphing</h3>
              <div className="h-32 flex items-center justify-center">
                <GooeyText
                  texts={["Slow", "Smooth", "Elegant", "Graceful"]}
                  morphTime={2}
                  cooldownTime={0.5}
                  className="font-bold"
                  textClassName="text-2xl md:text-4xl text-green-400"
                />
              </div>
              <div className="mt-4 text-center">
                <code className="text-sm text-gray-400">morphTime: 2s, cooldownTime: 0.5s</code>
              </div>
            </div>

            {/* Brand Example */}
            <div className="bg-dark-800 rounded-lg p-8">
              <h3 className="text-xl font-bold text-white mb-6 text-center">Brand Example</h3>
              <div className="h-32 flex items-center justify-center">
                <GooeyText
                  texts={["Delta", "Xero", "Creations", "Innovation"]}
                  morphTime={1.2}
                  cooldownTime={0.3}
                  className="font-bold"
                  textClassName="text-2xl md:text-4xl bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent"
                />
              </div>
              <div className="mt-4 text-center">
                <code className="text-sm text-gray-400">Brand colors with gradient</code>
              </div>
            </div>

            {/* Services Example */}
            <div className="bg-dark-800 rounded-lg p-8">
              <h3 className="text-xl font-bold text-white mb-6 text-center">Services Example</h3>
              <div className="h-32 flex items-center justify-center">
                <GooeyText
                  texts={["Web", "Mobile", "Design", "API"]}
                  morphTime={1}
                  cooldownTime={0.25}
                  className="font-bold"
                  textClassName="text-2xl md:text-4xl text-yellow-400"
                />
              </div>
              <div className="mt-4 text-center">
                <code className="text-sm text-gray-400">Service categories</code>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Implementation Guide */}
      <section className="py-20 px-10 bg-dark-950">
        <div className="container mx-auto max-w-4xl">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-4">Implementation Guide</h2>
            <p className="text-gray-400">
              The gooey text morphing component has been successfully integrated as your loading screen.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-dark-800 rounded-lg p-6">
              <h3 className="text-xl font-bold text-white mb-4">✅ What's Included</h3>
              <ul className="space-y-2 text-gray-300">
                <li>• Gooey text component in <code className="text-primary-400">/components/ui/gooey-text-morphing.tsx</code></li>
                <li>• Loading screen component in <code className="text-primary-400">/components/LoadingScreen.tsx</code></li>
                <li>• App loading hook in <code className="text-primary-400">/hooks/useAppLoading.ts</code></li>
                <li>• Integrated into your main App.jsx</li>
                <li>• Ensures minimum one complete cycle</li>
                <li>• Smooth transitions and animations</li>
              </ul>
            </div>

            <div className="bg-dark-800 rounded-lg p-6">
              <h3 className="text-xl font-bold text-white mb-4">🎨 Features</h3>
              <ul className="space-y-2 text-gray-300">
                <li>• SVG filter-based morphing effect</li>
                <li>• Customizable timing and text arrays</li>
                <li>• Responsive design</li>
                <li>• Loading progress indicators</li>
                <li>• Minimum display time guarantee</li>
                <li>• Smooth exit animations</li>
              </ul>
            </div>
          </div>

          <div className="mt-12 bg-dark-800 rounded-lg p-6">
            <h3 className="text-xl font-bold text-white mb-4">🚀 Usage</h3>
            <div className="space-y-4">
              <div>
                <h4 className="text-lg font-semibold text-primary-400 mb-2">Basic Usage:</h4>
                <pre className="bg-dark-900 p-4 rounded text-sm text-gray-300 overflow-x-auto">
{`import { GooeyText } from "@/components/ui/gooey-text-morphing";

<GooeyText
  texts={["Hello", "World", "Gooey", "Text"]}
  morphTime={1}
  cooldownTime={0.25}
  className="font-bold"
  textClassName="text-4xl text-white"
/>`}
                </pre>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold text-primary-400 mb-2">Loading Screen Configuration:</h4>
                <pre className="bg-dark-900 p-4 rounded text-sm text-gray-300 overflow-x-auto">
{`// In App.jsx - already configured
<LoadingScreen
  isLoading={isLoading}
  onLoadingComplete={handleLoadingComplete}
  texts={["Delta", "Xero", "Creations", "Welcome"]}
  morphTime={1.2}
  cooldownTime={0.3}
  minDisplayTime={5000} // 5 seconds minimum
/>`}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default GooeyTextDemoPage;
