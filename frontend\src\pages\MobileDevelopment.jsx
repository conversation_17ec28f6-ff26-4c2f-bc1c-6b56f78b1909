import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import SolutionsBentoGrid from '../components/ui/solutions-bento-grid'
import { Smartphone, Zap, Shield, Globe, Database, Users, ArrowRight, CheckCircle, Star, Download, Rocket, TrendingUp, BarChart3, Gauge, Lock, Cloud, Cpu, Play } from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const MobileDevelopment = () => {
  const statsRef = useRef(null)

  useEffect(() => {
    // Stats counter animation
    gsap.fromTo('.stat-counter',
      { textContent: 0 },
      {
        textContent: (i, target) => target.getAttribute('data-value'),
        duration: 2,
        ease: 'power2.out',
        snap: { textContent: 1 },
        scrollTrigger: {
          trigger: statsRef.current,
          start: 'top 80%',
        }
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const mobileServices = [
    {
      icon: Smartphone,
      title: 'Native Mobile Applications',
      description: 'High-performance native mobile applications built with Swift and Kotlin, optimized for each platform\'s unique capabilities.',
      features: ['Native Performance Optimization', 'Platform-Specific UI/UX', 'Advanced Device Integration', 'App Store Optimization'],
      color: 'from-blue-500 to-cyan-500',
      price: 'From $15,000',
      timeline: '12-20 weeks',
      metrics: { performance: '60fps smooth', downloads: '1M+ installs', rating: '4.8★ average' }
    },
    {
      icon: Zap,
      title: 'Cross-Platform Solutions',
      description: 'Cost-effective solutions using React Native and Flutter to target both iOS and Android with a single codebase.',
      features: ['Single Codebase Architecture', 'Rapid Development Cycle', 'Consistent User Experience', 'Simplified Maintenance'],
      color: 'from-green-500 to-emerald-500',
      price: 'From $10,000',
      timeline: '8-16 weeks',
      metrics: { efficiency: '40% faster', coverage: 'iOS + Android', maintenance: '60% less cost' }
    },
    {
      icon: Database,
      title: 'Backend & Cloud Integration',
      description: 'Robust backend services with cloud infrastructure, real-time synchronization, and scalable API architecture.',
      features: ['Real-time Data Sync', 'Cloud Storage Solutions', 'Push Notification System', 'Offline-First Architecture'],
      color: 'from-purple-500 to-pink-500',
      price: 'From $8,000',
      timeline: '6-12 weeks',
      metrics: { uptime: '99.9% SLA', sync: 'Real-time', scale: '10M+ users' }
    },
    {
      icon: Shield,
      title: 'Security & Quality Assurance',
      description: 'Enterprise-grade security implementation with comprehensive testing to ensure app reliability and data protection.',
      features: ['Security Penetration Testing', 'Automated QA Testing', 'Performance Optimization', 'Compliance Certification'],
      color: 'from-orange-500 to-red-500',
      price: 'From $5,000',
      timeline: '4-8 weeks',
      metrics: { security: 'Bank-level', testing: '99% coverage', compliance: 'GDPR ready' }
    }
  ]

  const platforms = [
    { name: 'iOS', logo: '🍎', description: 'Native iOS development with Swift', category: 'Native', features: ['SwiftUI', 'Core Data', 'CloudKit', 'ARKit'] },
    { name: 'Android', logo: '🤖', description: 'Native Android development with Kotlin', category: 'Native', features: ['Jetpack Compose', 'Room Database', 'Firebase', 'ML Kit'] },
    { name: 'React Native', logo: '⚛️', description: 'Cross-platform with React Native', category: 'Cross-Platform', features: ['JavaScript', 'Hot Reload', 'Native Modules', 'Expo'] },
    { name: 'Flutter', logo: '🦋', description: 'Cross-platform with Flutter', category: 'Cross-Platform', features: ['Dart', 'Hot Reload', 'Material Design', 'Cupertino'] }
  ]

  const stats = [
    { number: 120, label: 'Mobile Apps Delivered', suffix: '+', icon: Smartphone, color: 'from-blue-500 to-cyan-500' },
    { number: 4.9, label: 'Average App Rating', suffix: '/5', icon: Star, color: 'from-yellow-500 to-orange-500' },
    { number: 2.5, label: 'Million Downloads', suffix: 'M+', icon: Download, color: 'from-green-500 to-emerald-500' },
    { number: 99.2, label: 'Client Satisfaction', suffix: '%', icon: TrendingUp, color: 'from-purple-500 to-pink-500' }
  ]

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen"
    >
      {/* Hero Section */}
      <section className="relative py-32 bg-gradient-to-b from-dark-950 via-dark-900 to-dark-950 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              className="inline-flex items-center bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-full px-6 py-3 mb-8"
            >
              <div className="w-2 h-2 bg-purple-400 rounded-full mr-3 animate-pulse"></div>
              <Smartphone className="w-4 h-4 text-purple-400 mr-2" />
              <span className="text-purple-400 text-sm font-semibold uppercase tracking-wider">Mobile App Development</span>
            </motion.div>

            <motion.h1
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-5xl sm:text-6xl md:text-7xl font-black text-white mb-8 leading-none"
            >
              MOBILE APPS
              <br />
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
                THAT SCALE
              </span>
            </motion.h1>

            <motion.p
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-xl sm:text-2xl text-gray-300 max-w-4xl mx-auto mb-12 leading-relaxed"
            >
              Create powerful, user-friendly mobile applications for iOS and Android that engage millions of users
              and drive exponential business growth.
            </motion.p>

            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-6 justify-center mb-16"
            >
              <Link to="/get-quote">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-12 py-4 rounded-2xl font-bold text-lg hover:shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 flex items-center justify-center space-x-3"
                >
                  <span>Start Your App</span>
                  <ArrowRight className="w-5 h-5" />
                </motion.button>
              </Link>

              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="bg-black/20 backdrop-blur-xl border border-white/20 text-white px-12 py-4 rounded-2xl font-bold text-lg hover:bg-white/10 transition-all duration-300 flex items-center justify-center space-x-3"
                style={{ backdropFilter: 'blur(20px) saturate(180%)' }}
              >
                <Play className="w-5 h-5" />
                <span>View Portfolio</span>
              </motion.button>
            </motion.div>

            {/* Key Features */}
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
            >
              {[
                { icon: Smartphone, text: 'iOS + Android', color: 'text-purple-400' },
                { icon: Zap, text: 'Lightning Fast', color: 'text-pink-400' },
                { icon: Users, text: '2.5M+ Downloads', color: 'text-cyan-400' }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-center space-x-3 text-gray-300">
                  <item.icon className={item.color} size={20} />
                  <span className="font-medium">{item.text}</span>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Rocket className="text-purple-400 mr-3" size={16} />
              <span className="text-purple-400 text-sm font-semibold uppercase tracking-wider">Mobile Solutions</span>
            </motion.div>

            <h2 className="text-5xl md:text-6xl font-black text-white mb-8 leading-tight">
              MOBILE DEVELOPMENT
              <br />
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
                SERVICES
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
              From concept to app store, we deliver mobile solutions that engage millions of users and drive business growth
            </p>
          </motion.div>

          <SolutionsBentoGrid services={mobileServices} />
        </div>
      </section>

      {/* Platforms Section */}
      <section className="py-20 bg-dark-900/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Platforms We Support
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              We develop for all major mobile platforms using the best technologies for each
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {platforms.map((platform, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-dark-800/50 border border-gray-700 rounded-xl p-6 text-center hover:border-primary-500/50 transition-all duration-300 group"
              >
                <div className="text-5xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {platform.logo}
                </div>
                <h3 className="text-white font-semibold mb-2">{platform.name}</h3>
                <p className="text-gray-400 text-sm">{platform.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section ref={statsRef} className="py-20 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-4xl md:text-5xl font-bold text-primary-400 mb-2">
                  <span className="stat-counter" data-value={stat.number}>0</span>
                  {stat.suffix}
                </div>
                <div className="text-gray-400 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* App Development Process */}
      <section className="py-20 bg-dark-900/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Mobile App Development Process
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              From concept to app store, we follow a comprehensive process to ensure your mobile app succeeds
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                step: "01",
                title: "Strategy & Planning",
                description: "Define app objectives, target audience, and create detailed project roadmap with timeline and milestones.",
                features: ["Market Research", "Competitor Analysis", "User Personas", "Feature Planning"]
              },
              {
                step: "02",
                title: "UI/UX Design",
                description: "Create intuitive, engaging designs that provide exceptional user experience across all devices.",
                features: ["Wireframing", "Visual Design", "Prototyping", "User Testing"]
              },
              {
                step: "03",
                title: "Development",
                description: "Build your app using latest technologies with clean, maintainable code and best practices.",
                features: ["Native/Cross-platform", "API Integration", "Database Design", "Security Implementation"]
              },
              {
                step: "04",
                title: "Testing & QA",
                description: "Rigorous testing across devices, platforms, and scenarios to ensure flawless performance.",
                features: ["Functional Testing", "Performance Testing", "Security Testing", "User Acceptance Testing"]
              },
              {
                step: "05",
                title: "App Store Launch",
                description: "Handle app store submission, optimization, and launch strategy for maximum visibility.",
                features: ["App Store Optimization", "Launch Strategy", "Marketing Materials", "Store Submission"]
              },
              {
                step: "06",
                title: "Support & Updates",
                description: "Ongoing maintenance, updates, and feature enhancements to keep your app competitive.",
                features: ["Bug Fixes", "Feature Updates", "Performance Monitoring", "User Support"]
              }
            ].map((process, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-dark-800/50 border border-gray-700 rounded-2xl p-6 hover:border-primary-500/50 transition-all duration-300"
              >
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                    {process.step}
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-3">{process.title}</h3>
                <p className="text-gray-400 mb-4 leading-relaxed">{process.description}</p>
                <ul className="space-y-2">
                  {process.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-sm text-gray-300">
                      <CheckCircle className="w-4 h-4 text-primary-400 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* App Types Section */}
      <section className="py-20 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Types of Mobile Apps We Build
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              We specialize in various types of mobile applications across different industries and use cases
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "E-commerce Apps",
                description: "Feature-rich shopping apps with payment integration, inventory management, and user analytics.",
                icon: "🛒",
                features: ["Product Catalog", "Payment Gateway", "Order Tracking", "Push Notifications"]
              },
              {
                title: "Social Media Apps",
                description: "Engaging social platforms with real-time messaging, content sharing, and community features.",
                icon: "📱",
                features: ["Real-time Chat", "Media Sharing", "User Profiles", "Social Features"]
              },
              {
                title: "Business Apps",
                description: "Productivity and business management apps to streamline operations and improve efficiency.",
                icon: "💼",
                features: ["Task Management", "Team Collaboration", "Analytics Dashboard", "Cloud Sync"]
              },
              {
                title: "Healthcare Apps",
                description: "HIPAA-compliant health and wellness apps with secure data handling and telemedicine features.",
                icon: "🏥",
                features: ["Patient Records", "Appointment Booking", "Telemedicine", "Health Tracking"]
              },
              {
                title: "Education Apps",
                description: "Interactive learning platforms with multimedia content, progress tracking, and gamification.",
                icon: "🎓",
                features: ["Course Management", "Progress Tracking", "Interactive Content", "Assessments"]
              },
              {
                title: "Entertainment Apps",
                description: "Engaging entertainment apps with streaming, gaming, and interactive content features.",
                icon: "🎮",
                features: ["Media Streaming", "Gaming Features", "User Engagement", "Content Management"]
              }
            ].map((appType, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-dark-900/50 backdrop-blur-sm border border-gray-800 rounded-2xl p-6 hover:border-primary-500/50 transition-all duration-300 group"
              >
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {appType.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-3">{appType.title}</h3>
                <p className="text-gray-400 mb-4 leading-relaxed">{appType.description}</p>
                <ul className="space-y-2">
                  {appType.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-sm text-gray-300">
                      <CheckCircle className="w-4 h-4 text-primary-400 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary-900/20 to-primary-800/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Launch Your Mobile App?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Let's bring your mobile app idea to life and reach millions of users on iOS and Android
            </p>
            <div className="flex justify-center">
              <button className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-12 py-4 rounded-lg font-semibold hover:from-primary-600 hover:to-primary-700 transition-all duration-300 inline-flex items-center justify-center">
                Schedule Consultation
                <ArrowRight className="ml-2 w-5 h-5" />
              </button>
            </div>
          </motion.div>
        </div>
      </section>
    </motion.div>
  )
}

export default MobileDevelopment
