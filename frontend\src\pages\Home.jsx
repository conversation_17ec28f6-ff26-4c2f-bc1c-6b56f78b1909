import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Link } from 'react-router-dom'
import { ArrowRight, Rocket, CheckCircle } from 'lucide-react'
import { HeroParallaxDemo } from '../components/ui/hero-parallax-demo'
import TestimonialsSection from '../components/TestimonialsSection'
import { DisplayCardsDemo } from '../components/DisplayCardsDemo'
import PortfolioSection from '../components/PortfolioSection'
import InfiniteMarquee from '../components/ui/infinite-marquee'
import ServicesSection from '../components/ServicesSection'
import { getSmoothScrollInstance, integrateLenisWithGSAP } from '../hooks/useSmoothScroll'

gsap.registerPlugin(ScrollTrigger)

const Home = () => {
  const statsRef = useRef(null)
  const clientsRef = useRef(null)

  useEffect(() => {
    // Wait for Lenis to be ready before initializing GSAP animations
    const initAnimations = () => {
      const lenis = getSmoothScrollInstance()
      if (!lenis) {
        // Retry if Lenis isn't ready yet
        setTimeout(initAnimations, 100)
        return
      }

      // Integrate GSAP with Lenis
      integrateLenisWithGSAP()

      // Small delay to ensure everything is properly initialized
      setTimeout(() => {
        // Optimized GSAP animations with better performance
        gsap.set('.stat-number, .client-logo', {
          willChange: 'transform, opacity'
        })

        // Stats counter animation - optimized
        const statNumbers = gsap.utils.toArray('.stat-number')
        if (statNumbers.length > 0) {
          gsap.fromTo(statNumbers,
            { textContent: 0 },
            {
              textContent: (_, target) => target.getAttribute('data-value'),
              duration: 1.5,
              ease: 'power2.out',
              snap: { textContent: 1 },
              scrollTrigger: {
                trigger: statsRef.current,
                start: 'top 85%',
                toggleActions: 'play none none none',
                scroller: document.body // Explicitly set scroller
              }
            }
          )
        }

        // Client logos animation - optimized
        const clientLogos = gsap.utils.toArray('.client-logo')
        if (clientLogos.length > 0) {
          gsap.fromTo(clientLogos,
            { opacity: 0, y: 20 },
            {
              opacity: 1,
              y: 0,
              duration: 0.5,
              stagger: 0.08,
              ease: 'power2.out',
              scrollTrigger: {
                trigger: clientsRef.current,
                start: 'top 85%',
                toggleActions: 'play none none reverse',
                scroller: document.body // Explicitly set scroller
              }
            }
          )
        }
      }, 200)
    }

    initAnimations()

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
      // Only set willChange if elements exist
      const elements = document.querySelectorAll('.stat-number, .client-logo')
      if (elements.length > 0) {
        gsap.set(elements, {
          willChange: 'auto'
        })
      }
    }
  }, [])



  const stats = [
    { number: 150, label: 'Projects Delivered', suffix: '+' },
    { number: 50, label: 'Happy Clients', suffix: '+' },
    { number: 99, label: 'Success Rate', suffix: '%' },
    { number: 24, label: 'Support Available', suffix: '/7' }
  ]

  const trustedClients = [
    { name: 'Google', logo: 'https://upload.wikimedia.org/wikipedia/commons/2/2f/Google_2015_logo.svg' },
    { name: 'Apple', logo: 'https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_white.svg' },
    { name: 'Microsoft', logo: 'https://upload.wikimedia.org/wikipedia/commons/4/44/Microsoft_logo.svg' },
    { name: 'Meta', logo: 'https://upload.wikimedia.org/wikipedia/commons/7/7b/Meta_Platforms_Inc._logo.svg' },
    { name: 'Adobe', logo: 'https://upload.wikimedia.org/wikipedia/commons/8/8d/Adobe_Corporate_Logo.svg' },
    { name: 'Slack', logo: 'https://upload.wikimedia.org/wikipedia/commons/d/d5/Slack_icon_2019.svg' },
    { name: 'GitHub', logo: 'https://upload.wikimedia.org/wikipedia/commons/9/91/Octicons-mark-github.svg', masked: true },
    { name: 'LinkedIn', logo: 'https://upload.wikimedia.org/wikipedia/commons/c/ca/LinkedIn_logo_initials.png' },
    { name: 'X (Twitter)', logo: 'https://upload.wikimedia.org/wikipedia/commons/5/57/X_logo_2023_original.svg' },
    { name: 'Netflix', logo: 'https://upload.wikimedia.org/wikipedia/commons/0/08/Netflix_2015_logo.svg' },
    { name: 'Spotify', logo: 'https://upload.wikimedia.org/wikipedia/commons/1/19/Spotify_logo_without_text.svg' },
    { name: 'Tesla', logo: 'https://upload.wikimedia.org/wikipedia/commons/b/bb/Tesla_T_symbol.svg' }
  ]



  const successStories = [
    {
      title: 'E-commerce Platform Transformation',
      client: 'RetailPro',
      description: 'Increased conversion rates by 300% with a modern, responsive e-commerce platform',
      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop',
      results: ['300% increase in conversions', '50% faster page load times', '99.9% uptime achieved']
    },
    {
      title: 'Mobile App Development',
      client: 'TechStart Inc.',
      description: 'Launched a cross-platform mobile app that gained 100K+ users in the first month',
      image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop',
      results: ['100K+ downloads in month 1', '4.8/5 app store rating', 'Featured by Apple']
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen"
    >
      {/* Hero Parallax Section - Takes 300vh for animation */}
      <HeroParallaxDemo />

      {/* Trusted Clients Section - Infinite Marquee */}
      <section ref={clientsRef} className="py-8 md:py-12 bg-dark-950 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-6 md:mb-8"
          >
            <p className="text-gray-400 text-base md:text-lg">Trusted by People working at industry worldwide</p>
          </motion.div>

          <InfiniteMarquee speed={30} className="py-4 md:py-6">
            <div className="flex items-center justify-center space-x-8 md:space-x-16">
              {trustedClients.map((client, index) => (
                <div
                  key={index}
                  className="client-logo flex items-center justify-center flex-shrink-0 w-24 h-12 md:w-32 md:h-16"
                >
                  <img
                    src={client.logo}
                    alt={client.name}
                    className={`h-8 md:h-12 w-auto max-w-[80px] md:max-w-[120px] opacity-70 hover:opacity-100 transition-opacity duration-300 object-contain mx-auto ${
                      client.masked ? 'filter brightness-0 invert' : ''
                    }`}
                    loading="lazy"
                    onError={(e) => {
                      // Hide the entire parent container when image fails to load
                      const parentDiv = e.target.closest('.client-logo');
                      if (parentDiv) {
                        parentDiv.style.display = 'none';
                      }
                    }}
                  />
                </div>
              ))}
            </div>
          </InfiniteMarquee>
        </div>
      </section>

      {/* Services Section */}
      <ServicesSection />

      {/* Interactive Service Cards */}
      <section className="py-16 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Explore Our <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Services</span>
              </h2>
              <p className="text-xl text-gray-400 mb-8 leading-relaxed">
                Hover over the cards to discover our core services and expertise areas. Each card represents a key area where we deliver exceptional results.
              </p>

              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="text-primary-400" size={20} />
                  <span className="text-gray-300">Modern web applications with React & Next.js</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="text-primary-400" size={20} />
                  <span className="text-gray-300">Cross-platform mobile apps for iOS & Android</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="text-primary-400" size={20} />
                  <span className="text-gray-300">User-centered design that converts</span>
                </div>
              </div>
            </motion.div>

            {/* Right Content - Interactive Cards */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="flex justify-center lg:justify-end"
            >
              <DisplayCardsDemo />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Success Stories Section */}
  

     

      {/* Testimonials Section */}
      <TestimonialsSection />

      {/* Portfolio Section */}
      {/* <PortfolioSection /> */}

      {/* CTA Section */}
      <section className="py-16 bg-dark-950">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            

            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
              Let's create your <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">digital success story</span> together
            </h2>
            <p className="text-xl text-gray-400 mb-8 max-w-2xl mx-auto leading-relaxed">
              Join 50+ companies who've transformed their digital presence with Delta Xero Creations.
              Affordable excellence, delivered with cutting-edge innovation.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
             

              <Link to="/get-quote">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="border-2 border-primary-500 text-primary-400 px-12 py-4 rounded-lg font-semibold text-lg hover:bg-primary-500/10 transition-all duration-300"
                >
                  Get Quote
                </motion.button>
              </Link>
            </div>

            <div className="flex items-center justify-center space-x-6 text-sm text-gray-400">
              <div className="flex items-center space-x-2">
                <CheckCircle className="text-green-400" size={16} />
                <span>Free Consultation</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="text-green-400" size={16} />
                <span>No Hidden Costs</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="text-green-400" size={16} />
                <span>24/7 Support</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </motion.div>
  )
}

export default Home
