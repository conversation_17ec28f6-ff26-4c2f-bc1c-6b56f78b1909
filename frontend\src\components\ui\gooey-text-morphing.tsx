"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface GooeyTextProps {
  texts: string[];
  morphTime?: number;
  cooldownTime?: number;
  className?: string;
  textClassName?: string;
}

export function GooeyText({
  texts,
  morphTime = 1,
  cooldownTime = 0.25,
  className,
  textClassName
}: GooeyTextProps) {
  const text1Ref = React.useRef<HTMLSpanElement>(null);
  const text2Ref = React.useRef<HTMLSpanElement>(null);
  const animationRef = React.useRef<number>();
  const isInitialized = React.useRef(false);
  const animationState = React.useRef({
    textIndex: 0,
    startTime: 0,
    isDisplaying: true,
    isRunning: false
  });

  // Memoize texts to prevent unnecessary re-renders
  const memoizedTexts = React.useMemo(() => texts, [texts.join(',')]);
  const memoizedMorphTime = React.useMemo(() => morphTime, [morphTime]);
  const memoizedCooldownTime = React.useMemo(() => cooldownTime, [cooldownTime]);

  React.useEffect(() => {
    if (memoizedTexts.length === 0 || animationState.current.isRunning) return;

    // Mark as running to prevent multiple instances
    animationState.current.isRunning = true;
    animationState.current.startTime = performance.now();

    // Initialize only once
    if (!isInitialized.current && text1Ref.current && text2Ref.current) {
      text1Ref.current.textContent = memoizedTexts[0];
      text2Ref.current.textContent = memoizedTexts[1 % memoizedTexts.length];

      // Initial state: show first text
      text1Ref.current.style.opacity = "1";
      text1Ref.current.style.filter = "";
      text2Ref.current.style.opacity = "0";
      text2Ref.current.style.filter = "";

      isInitialized.current = true;
    }

    const animate = (currentTime: number) => {
      if (!animationState.current.isRunning) return;

      const elapsed = (currentTime - animationState.current.startTime) / 1000;
      const state = animationState.current;

      if (state.isDisplaying) {
        // Display phase - show current text
        if (elapsed >= memoizedCooldownTime) {
          // Switch to morphing phase
          state.isDisplaying = false;
          state.startTime = currentTime;
        }
      } else {
        // Morphing phase - transition to next text
        const progress = Math.min(elapsed / memoizedMorphTime, 1);

        // Smooth easing
        const eased = progress < 0.5
          ? 4 * progress * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 3) / 2;

        if (text1Ref.current && text2Ref.current) {
          // Fade out current text with blur
          text1Ref.current.style.opacity = String(1 - eased);
          text1Ref.current.style.filter = eased > 0.1 ? `blur(${eased * 8}px)` : "";

          // Fade in next text with blur reduction
          text2Ref.current.style.opacity = String(eased);
          text2Ref.current.style.filter = (1 - eased) > 0.1 ? `blur(${(1 - eased) * 8}px)` : "";
        }

        if (progress >= 1) {
          // Morphing complete - move to next text
          state.textIndex = (state.textIndex + 1) % memoizedTexts.length;
          const nextIndex = (state.textIndex + 1) % memoizedTexts.length;

          if (text1Ref.current && text2Ref.current) {
            // Swap texts: next becomes current
            text1Ref.current.textContent = memoizedTexts[state.textIndex];
            text2Ref.current.textContent = memoizedTexts[nextIndex];

            // Reset to display state
            text1Ref.current.style.opacity = "1";
            text1Ref.current.style.filter = "";
            text2Ref.current.style.opacity = "0";
            text2Ref.current.style.filter = "";
          }

          state.isDisplaying = true;
          state.startTime = currentTime;
        }
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    // Start animation
    animationRef.current = requestAnimationFrame(animate);

    return () => {
      animationState.current.isRunning = false;
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [memoizedTexts, memoizedMorphTime, memoizedCooldownTime]);

  // Generate unique filter ID to avoid conflicts
  const filterId = React.useMemo(() => `gooey-threshold-${Math.random().toString(36).substr(2, 9)}`, []);

  return (
    <div className={cn("relative", className)}>
      <svg className="absolute h-0 w-0" aria-hidden="true" focusable="false">
        <defs>
          <filter id={filterId}>
            <feColorMatrix
              in="SourceGraphic"
              type="matrix"
              values="1 0 0 0 0
                      0 1 0 0 0
                      0 0 1 0 0
                      0 0 0 255 -140"
            />
          </filter>
        </defs>
      </svg>

      <div
        className="flex items-center justify-center"
        style={{ filter: `url(#${filterId})` }}
      >
        <span
          ref={text1Ref}
          className={cn(
            "absolute inline-block select-none text-center text-6xl md:text-[60pt]",
            "text-white font-bold",
            textClassName
          )}
        />
        <span
          ref={text2Ref}
          className={cn(
            "absolute inline-block select-none text-center text-6xl md:text-[60pt]",
            "text-white font-bold",
            textClassName
          )}
        />
      </div>
    </div>
  );
}
