import React from 'react'
import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import { ArrowRight } from 'lucide-react'
import { cn } from '@/lib/utils'

interface Service {
  icon: React.ComponentType<any>
  title: string
  description: string
  features: string[]
  color: string
  price: string
  timeline: string
  metrics: Record<string, string>
}

interface SolutionsBentoGridProps {
  services: Service[]
  className?: string
}

const SolutionsBentoGrid: React.FC<SolutionsBentoGridProps> = ({ services, className }) => {
  // Define different layouts based on number of services
  const getGridLayout = (count: number) => {
    if (count === 4) {
      return [
        { size: 'large', position: 'col-span-1 md:col-span-2 row-span-3' },
        { size: 'medium', position: 'col-span-1 row-span-2' },
        { size: 'medium', position: 'col-span-1 row-span-2' },
        { size: 'wide', position: 'col-span-1 md:col-span-2 row-span-2' }
      ]
    } else {
      // Default layout for other counts
      return services.map((_, index) => ({
        size: index === 0 ? 'large' : 'medium',
        position: index === 0 ? 'col-span-1 md:col-span-2 row-span-3' : 'col-span-1 row-span-2'
      }))
    }
  }

  const layout = getGridLayout(services.length)

  return (
    <div className={cn(
      "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 auto-rows-[150px] max-w-7xl mx-auto",
      className
    )}>
      {services.map((service, index) => {
        const Icon = service.icon
        const layoutConfig = layout[index] || { size: 'medium', position: 'col-span-1 row-span-2' }
        
        return (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 30, scale: 0.9 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ 
              duration: 0.6, 
              delay: index * 0.1,
              type: "spring",
              stiffness: 100
            }}
            viewport={{ once: true }}
            className={cn(
              layoutConfig.position,
              "group relative overflow-hidden rounded-3xl border border-white/10 bg-gradient-to-br from-dark-800/50 to-dark-900/50 backdrop-blur-xl hover:border-white/20 transition-all duration-500 cursor-pointer"
            )}
          >
            {/* Background Gradient */}
            <div className={cn(
              "absolute inset-0 bg-gradient-to-br opacity-5 group-hover:opacity-15 transition-opacity duration-500",
              service.color
            )} />
            
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5 group-hover:opacity-15 transition-opacity duration-500">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.3) 1px, transparent 0)`,
                backgroundSize: '24px 24px'
              }}></div>
            </div>

            {/* Content */}
            <div className="relative z-10 h-full flex flex-col p-6 md:p-8">
              {/* Header with Icon and Price */}
              <div className="flex items-start justify-between mb-4">
                <div className={cn(
                  "w-12 h-12 rounded-2xl bg-gradient-to-br flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg",
                  service.color
                )}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                {layoutConfig.size === 'large' && (
                  <div className="text-right">
                    <div className="text-lg font-bold text-white">{service.price}</div>
                    <div className="text-primary-400 text-xs font-medium">{service.timeline}</div>
                  </div>
                )}
              </div>
              
              {/* Title */}
              <h3 className={cn(
                "font-bold mb-3 group-hover:text-white transition-colors duration-300",
                layoutConfig.size === 'large' ? 'text-xl md:text-2xl text-white' : 'text-lg text-white'
              )}>
                {service.title}
              </h3>
              
              {/* Description */}
              <p className={cn(
                "text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-300 mb-4",
                layoutConfig.size === 'large' ? 'text-sm md:text-base flex-1' : 'text-xs flex-1'
              )}>
                {service.description}
              </p>

              {/* Features for large cards */}
              {layoutConfig.size === 'large' && (
                <div className="space-y-2 mb-4">
                  {service.features.slice(0, 3).map((feature, idx) => (
                    <div key={idx} className="flex items-center space-x-2">
                      <div className={cn(
                        "w-1.5 h-1.5 rounded-full bg-gradient-to-r",
                        service.color
                      )} />
                      <span className="text-xs text-gray-400">{feature}</span>
                    </div>
                  ))}
                </div>
              )}

              {/* Metrics for large cards */}
              {layoutConfig.size === 'large' && (
                <div className="grid grid-cols-3 gap-2 mb-4">
                  {Object.entries(service.metrics).map(([key, value], idx) => (
                    <div key={idx} className="text-center bg-white/5 rounded-lg p-2 border border-white/10">
                      <div className="text-xs font-bold text-white">{value}</div>
                      <div className="text-gray-400 text-[10px] uppercase tracking-wider">{key}</div>
                    </div>
                  ))}
                </div>
              )}

              {/* CTA Button */}
              <div className="mt-auto">
                <Link to="/get-quote">
                  <motion.button
                    whileHover={{ scale: 1.02, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                    className={cn(
                      "w-full bg-gradient-to-r text-white font-bold rounded-xl transition-all duration-300 relative overflow-hidden group/btn shadow-lg flex items-center justify-center space-x-2",
                      service.color,
                      layoutConfig.size === 'large' ? 'py-3 text-sm' : 'py-2 text-xs'
                    )}
                  >
                    <span className="relative z-10">Get Started</span>
                    <ArrowRight className="relative z-10" size={layoutConfig.size === 'large' ? 16 : 14} />
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>
                  </motion.button>
                </Link>
              </div>
            </div>

            {/* Hover Effect Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            
            {/* Border Glow Effect */}
            <div className={cn(
              "absolute inset-0 rounded-3xl bg-gradient-to-br opacity-0 group-hover:opacity-30 transition-opacity duration-500 blur-xl -z-10",
              service.color
            )} />

            {/* Corner Accent */}
            <div className={cn(
              "absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl opacity-10 group-hover:opacity-20 transition-opacity duration-500",
              service.color
            )} style={{ clipPath: 'polygon(100% 0, 0 0, 100% 100%)' }} />
          </motion.div>
        )
      })}
    </div>
  )
}

export default SolutionsBentoGrid
