import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Users, Target, Award, Heart, Globe, Code, Zap, Star, Quote, CheckCircle, Shield, Rocket, Clock, Lightbulb } from 'lucide-react'
import TestimonialsSection from '../components/TestimonialsSection'
import FeaturesBentoGrid from '../components/ui/features-bento-grid'

gsap.registerPlugin(ScrollTrigger)

const About = () => {
  const timelineRef = useRef(null)

  useEffect(() => {
    // Timeline animation
    gsap.fromTo('.timeline-item',
      { x: -100, opacity: 0 },
      {
        x: 0,
        opacity: 1,
        duration: 0.8,
        stagger: 0.3,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: timelineRef.current,
          start: 'top 80%',
        }
      }
    )



    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])



  const values = [
    {
      icon: Target,
      title: 'Innovation',
      description: 'We stay ahead of technology trends to deliver cutting-edge solutions that push boundaries',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Users,
      title: 'Collaboration',
      description: 'We work closely with our clients as partners, not just service providers',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Award,
      title: 'Excellence',
      description: 'We never compromise on quality, delivering excellence in every pixel and line of code',
      color: 'from-green-500 to-teal-500'
    },
    {
      icon: Heart,
      title: 'Passion',
      description: 'We love what we do and it shows in every project we create',
      color: 'from-orange-500 to-red-500'
    }
  ]

  const aboutFeatures = [
    {
      icon: Shield,
      title: 'Trusted by 150+ Clients',
      description: 'From startups to Fortune 500 companies, we deliver reliable solutions that scale with your business growth',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Rocket,
      title: 'Fast Delivery',
      description: 'Agile development process ensures rapid deployment without compromising on quality or security',
      color: 'from-blue-500 to-indigo-500'
    },
    {
      icon: Clock,
      title: '24/7 Support',
      description: 'Round-the-clock technical support and maintenance to keep your applications running smoothly',
      color: 'from-purple-500 to-violet-500'
    },
    {
      icon: Lightbulb,
      title: 'Creative Solutions',
      description: 'Innovative problem-solving approach that transforms complex challenges into elegant digital experiences',
      color: 'from-yellow-500 to-orange-500'
    },
    {
      icon: Globe,
      title: 'Global Reach',
      description: 'Serving clients across 5 countries with localized solutions and international best practices',
      color: 'from-cyan-500 to-teal-500'
    },
    {
      icon: Star,
      title: '99% Satisfaction',
      description: 'Consistently high client satisfaction rates through transparent communication and exceptional results',
      color: 'from-pink-500 to-rose-500'
    }
  ]

  const timeline = [
    {
      year: '2020',
      title: 'The Beginning',
      description: 'Founded with a vision to democratize access to high-quality web development services',
      highlight: 'Started in a small garage with big dreams'
    },
    {
      year: '2021',
      title: 'First Breakthrough',
      description: 'Delivered our first enterprise-level project, establishing credibility in the market',
      highlight: 'Gained recognition for innovative solutions'
    },
    {
      year: '2022',
      title: 'Team Growth',
      description: 'Expanded our team to include specialists in mobile development, UI/UX design, and DevOps',
      highlight: 'Became a multi-disciplinary powerhouse'
    },
    {
      year: '2023',
      title: 'Technology Leadership',
      description: 'Invested heavily in emerging technologies, AI integration, and advanced animation frameworks',
      highlight: 'Pioneered new approaches to web development'
    },
    {
      year: '2024',
      title: 'Global Expansion',
      description: 'Expanded services globally, serving clients across multiple continents and time zones',
      highlight: 'Achieved 24/7 global support capability'
    },
    {
      year: '2025',
      title: 'The Future',
      description: 'Continuing to innovate and lead the industry with AI-powered development and immersive experiences',
      highlight: 'Shaping the future of digital experiences'
    }
  ]



  const capabilities = [
    {
      icon: Code,
      title: 'Full-Stack Development',
      description: 'End-to-end development with modern frameworks and technologies'
    },
    {
      icon: Zap,
      title: 'Performance Optimization',
      description: 'Lightning-fast websites that provide exceptional user experiences'
    },
    {
      icon: Globe,
      title: 'Global Delivery',
      description: 'Round-the-clock support with teams across multiple time zones'
    },
    {
      icon: Star,
      title: 'Premium Quality',
      description: 'Enterprise-grade solutions at affordable pricing'
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen"
    >
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <motion.div
              initial={{ x: -50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
                Transforming Ideas into <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Digital Reality</span>
              </h1>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                At Delta Xero Creations, we're not just developers—we're digital architects who transform
                ambitious ideas into powerful, scalable solutions. Founded with the vision to democratize
                access to premium technology, we bridge the gap between innovation and affordability.
              </p>
              <div className="bg-gradient-to-r from-primary-500/10 to-primary-600/10 rounded-xl p-6 mb-8 border border-primary-500/20">
                <h3 className="text-lg font-semibold text-white mb-2">Our Mission</h3>
                <p className="text-gray-300">
                  To empower businesses of all sizes with cutting-edge digital solutions that drive growth,
                  enhance user experiences, and create lasting impact in the digital landscape.
                </p>
              </div>
              <div className="flex flex-wrap gap-6 text-sm text-gray-400">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-primary-500 rounded-full mr-2"></div>
                  <span>Affordable Excellence</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-primary-500 rounded-full mr-2"></div>
                  <span>Global Team</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-primary-500 rounded-full mr-2"></div>
                  <span>24/7 Support</span>
                </div>
              </div>
            </motion.div>

            {/* Right Content - Company Image */}
            <motion.div
              initial={{ x: 50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="relative bg-gradient-to-br from-primary-500/20 to-primary-600/20 rounded-2xl p-8 backdrop-blur-sm border border-primary-500/20">
                <img
                  src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=600&h=400&fit=crop"
                  alt="Delta Xero Team"
                  className="w-full h-80 object-cover rounded-xl"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-dark-900/60 via-transparent to-transparent rounded-xl"></div>
                <div className="absolute bottom-8 left-8 right-8">
                  <h3 className="text-white text-xl font-bold mb-2">Our Global Team</h3>
                  <p className="text-gray-300 text-sm">Passionate developers and designers working together to create amazing digital experiences</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Why Choose Us - Bento Grid Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 to-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="inline-flex items-center bg-primary-500/10 border border-primary-500/20 rounded-full px-4 py-2 mb-6">
                <span className="text-primary-400 text-sm font-medium">Why Choose Delta Xero</span>
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
                What Makes Us <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Different</span>
              </h2>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto">
                We combine technical expertise with creative vision to deliver solutions that not only work flawlessly but also inspire and engage your users.
              </p>
            </motion.div>
          </div>

          <FeaturesBentoGrid features={aboutFeatures} />
        </div>
      </section>



      {/* Leadership Team Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-4xl md:text-5xl font-bold text-white mb-4"
            >
              Meet Our <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Leadership</span>
            </motion.h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Visionary leaders with decades of combined experience in technology, design, and business strategy.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "Alex Chen",
                role: "Founder & CEO",
                bio: "Former tech lead at Google with 10+ years in full-stack development and team leadership.",
                image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
                expertise: ["Strategic Vision", "Product Development", "Team Leadership"]
              },
              {
                name: "Sarah Rodriguez",
                role: "CTO & Co-Founder",
                bio: "Ex-Microsoft architect specializing in scalable systems and emerging technologies.",
                image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face",
                expertise: ["System Architecture", "AI/ML Integration", "Cloud Infrastructure"]
              },
              {
                name: "Marcus Thompson",
                role: "Head of Design",
                bio: "Award-winning designer with experience at Apple and Airbnb, passionate about user-centered design.",
                image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face",
                expertise: ["UX Strategy", "Design Systems", "Brand Identity"]
              }
            ].map((leader, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="bg-dark-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-primary-500/50 transition-all duration-300 group"
              >
                <div className="relative mb-6">
                  <img
                    src={leader.image}
                    alt={leader.name}
                    className="w-24 h-24 rounded-full mx-auto object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 rounded-full bg-gradient-to-br from-primary-500/20 to-primary-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                <div className="text-center">
                  <h3 className="text-xl font-semibold text-white mb-1">{leader.name}</h3>
                  <p className="text-primary-400 font-medium mb-4">{leader.role}</p>
                  <p className="text-gray-400 text-sm mb-6 leading-relaxed">{leader.bio}</p>

                  <div className="space-y-2">
                    {leader.expertise.map((skill, skillIndex) => (
                      <div key={skillIndex} className="inline-block bg-primary-500/10 text-primary-400 px-3 py-1 rounded-full text-xs mr-2 mb-2">
                        {skill}
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* What We Do Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ x: -50, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                What do we <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">do?</span>
              </h2>
              <p className="text-gray-300 text-lg mb-8 leading-relaxed">
                We help digital changemakers make change. Our clients include startups, enterprises,
                and organizations of different shapes and sizes. Our integrated services include modern
                web development, mobile applications, UI/UX design, and digital consulting.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {capabilities.map((capability, index) => {
                  const Icon = capability.icon
                  return (
                    <motion.div
                      key={index}
                      initial={{ y: 30, opacity: 0 }}
                      whileInView={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-start space-x-3"
                    >
                      <div className="bg-gradient-to-br from-primary-500/20 to-primary-600/20 p-2 rounded-lg flex-shrink-0">
                        <Icon className="text-primary-400" size={20} />
                      </div>
                      <div>
                        <h3 className="text-white font-semibold mb-1">{capability.title}</h3>
                        <p className="text-gray-400 text-sm">{capability.description}</p>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            </motion.div>

            <motion.div
              initial={{ x: 50, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-primary-500/10 to-primary-600/10 rounded-2xl p-8 border border-primary-500/20">
                <div className="grid grid-cols-2 gap-6 text-center">
                  <div>
                    <div className="text-3xl font-bold text-primary-400 mb-2">500+</div>
                    <p className="text-gray-400">Open Source Contributions</p>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-primary-400 mb-2">150+</div>
                    <p className="text-gray-400">Successful Projects</p>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-primary-400 mb-2">25+</div>
                    <p className="text-gray-400">Team Members Globally</p>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-primary-400 mb-2">5+</div>
                    <p className="text-gray-400">Countries Served</p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Technology Stack Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-4xl md:text-5xl font-bold text-white mb-4"
            >
              Our <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Technology Stack</span>
            </motion.h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              We leverage cutting-edge technologies and frameworks to build scalable, performant, and future-ready solutions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                category: "Frontend",
                technologies: ["React", "Next.js", "Vue.js", "TypeScript", "Tailwind CSS", "Framer Motion"],
                icon: "🎨",
                color: "from-blue-500 to-cyan-500"
              },
              {
                category: "Backend",
                technologies: ["Node.js", "Python", "Express", "FastAPI", "PostgreSQL", "MongoDB"],
                icon: "⚙️",
                color: "from-green-500 to-teal-500"
              },
              {
                category: "Mobile",
                technologies: ["React Native", "Flutter", "Swift", "Kotlin", "Expo", "Firebase"],
                icon: "📱",
                color: "from-purple-500 to-pink-500"
              },
              {
                category: "Cloud & DevOps",
                technologies: ["AWS", "Docker", "Kubernetes", "CI/CD", "Terraform", "Monitoring"],
                icon: "☁️",
                color: "from-orange-500 to-red-500"
              }
            ].map((stack, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-dark-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700 hover:border-primary-500/50 transition-all duration-300 group"
              >
                <div className={`w-16 h-16 bg-gradient-to-r ${stack.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <span className="text-2xl">{stack.icon}</span>
                </div>

                <h3 className="text-xl font-semibold text-white mb-4">{stack.category}</h3>

                <div className="space-y-2">
                  {stack.technologies.map((tech, techIndex) => (
                    <div key={techIndex} className="flex items-center space-x-2">
                      <div className="w-1 h-1 bg-primary-400 rounded-full"></div>
                      <span className="text-gray-300 text-sm">{tech}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Delta Xero is <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">values-driven</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Our core values aren't just words on a wall—they're the foundation of every decision we make
              and every relationship we build.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => {
              const Icon = value.icon
              return (
                <motion.div
                  key={index}
                  initial={{ y: 50, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-dark-800/50 backdrop-blur-sm p-8 rounded-2xl border border-gray-700 hover:border-primary-500/50 transition-all duration-300 group"
                >
                  <div className={`w-16 h-16 bg-gradient-to-r ${value.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="text-white" size={28} />
                  </div>
                  <h3 className="text-2xl font-semibold text-white mb-4">{value.title}</h3>
                  <p className="text-gray-400 text-lg leading-relaxed">{value.description}</p>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <TestimonialsSection />

      {/* Company Culture Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ x: -50, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Our <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Culture</span>
              </h2>
              <p className="text-gray-300 text-lg mb-8 leading-relaxed">
                We believe that great products come from great teams. Our culture is built on collaboration,
                continuous learning, and the shared belief that technology should make the world a better place.
              </p>

              <div className="space-y-6">
                {[
                  {
                    title: "Remote-First Approach",
                    description: "Flexible work environment that attracts global talent and promotes work-life balance."
                  },
                  {
                    title: "Continuous Learning",
                    description: "Regular tech talks, conference attendance, and learning budgets for every team member."
                  },
                  {
                    title: "Open Source Contribution",
                    description: "We actively contribute to open source projects and encourage innovation."
                  },
                  {
                    title: "Diversity & Inclusion",
                    description: "Building diverse teams that bring different perspectives and experiences."
                  }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ y: 30, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-start space-x-4"
                  >
                    <div className="bg-gradient-to-br from-primary-500/20 to-primary-600/20 p-2 rounded-lg flex-shrink-0 mt-1">
                      <div className="w-2 h-2 bg-primary-400 rounded-full"></div>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold mb-2">{item.title}</h3>
                      <p className="text-gray-400 text-sm leading-relaxed">{item.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ x: 50, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-primary-500/10 to-primary-600/10 rounded-2xl p-8 border border-primary-500/20">
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-400 mb-2">95%</div>
                    <p className="text-gray-400 text-sm">Employee Satisfaction</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-400 mb-2">4.9/5</div>
                    <p className="text-gray-400 text-sm">Glassdoor Rating</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-400 mb-2">12</div>
                    <p className="text-gray-400 text-sm">Avg. Tenure (Months)</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-400 mb-2">100%</div>
                    <p className="text-gray-400 text-sm">Remote Flexibility</p>
                  </div>
                </div>

                <div className="mt-8 pt-6 border-t border-gray-700">
                  <h4 className="text-white font-semibold mb-4 text-center">Recent Achievements</h4>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <Award className="text-primary-400" size={16} />
                      <span className="text-gray-300 text-sm">Best Places to Work 2024</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Star className="text-primary-400" size={16} />
                      <span className="text-gray-300 text-sm">Top 10 Emerging Tech Companies</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Heart className="text-primary-400" size={16} />
                      <span className="text-gray-300 text-sm">Community Impact Award</span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section ref={timelineRef} className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              The Delta Xero <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Story</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Our journey to becoming the trusted partner for digital transformation—from humble beginnings to global impact
            </p>
          </div>

          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-8 md:left-1/2 md:transform md:-translate-x-0.5 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary-500 to-primary-600"></div>

            {timeline.map((item, index) => (
              <div key={index} className={`timeline-item relative flex items-start mb-16 last:mb-0 ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'}`}>
                {/* Timeline dot */}
                <div className="bg-primary-500 w-6 h-6 rounded-full absolute left-5 md:left-1/2 md:transform md:-translate-x-3 top-4 z-10 border-4 border-dark-900"></div>

                {/* Content */}
                <div className={`ml-16 md:ml-0 md:w-1/2 ${index % 2 === 0 ? 'md:pr-12' : 'md:pl-12'}`}>
                  <motion.div
                    initial={{ y: 50, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-dark-800/50 backdrop-blur-sm p-8 rounded-2xl border border-gray-700 hover:border-primary-500/50 transition-all duration-300 group"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="text-primary-400 font-bold text-2xl">{item.year}</div>
                      <div className="text-xs text-gray-500 bg-primary-500/10 px-3 py-1 rounded-full">
                        {item.highlight}
                      </div>
                    </div>
                    <h3 className="text-white text-2xl font-semibold mb-4 group-hover:text-primary-400 transition-colors duration-300">
                      {item.title}
                    </h3>
                    <p className="text-gray-400 leading-relaxed">{item.description}</p>
                  </motion.div>
                </div>

                {/* Spacer for alternating layout */}
                <div className="hidden md:block md:w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Social Impact Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-4xl md:text-5xl font-bold text-white mb-4"
            >
              Making a <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Difference</span>
            </motion.h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Beyond building great software, we're committed to using technology as a force for positive change in our communities.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Education Initiative",
                description: "Free coding workshops for underrepresented communities and students.",
                impact: "500+ Students Trained",
                icon: "🎓",
                color: "from-blue-500 to-cyan-500"
              },
              {
                title: "Open Source",
                description: "Contributing to open source projects that benefit the global developer community.",
                impact: "50+ Projects Contributed",
                icon: "💻",
                color: "from-green-500 to-teal-500"
              },
              {
                title: "Environmental",
                description: "Carbon-neutral hosting and sustainable development practices.",
                impact: "100% Green Energy",
                icon: "🌱",
                color: "from-purple-500 to-pink-500"
              }
            ].map((initiative, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="bg-dark-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-primary-500/50 transition-all duration-300 group text-center"
              >
                <div className={`w-16 h-16 bg-gradient-to-r ${initiative.color} rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300`}>
                  <span className="text-2xl">{initiative.icon}</span>
                </div>

                <h3 className="text-xl font-semibold text-white mb-4">{initiative.title}</h3>
                <p className="text-gray-400 text-sm mb-6 leading-relaxed">{initiative.description}</p>

                <div className="bg-primary-500/10 rounded-lg p-3">
                  <div className="text-primary-400 font-semibold text-lg">{initiative.impact}</div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Global Presence Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              We are <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Global</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              We're distributed across multiple countries and time zones, enabling us to provide
              round-the-clock support and diverse perspectives to every project.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-5 gap-8 text-center">
            {['United States', 'India', 'United Kingdom', 'Canada', 'Australia'].map((country, index) => (
              <motion.div
                key={index}
                initial={{ y: 30, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-dark-800/50 backdrop-blur-sm p-6 rounded-xl border border-gray-700 hover:border-primary-500/50 transition-colors duration-300"
              >
                <Globe className="text-primary-400 mx-auto mb-3" size={32} />
                <h3 className="text-white font-semibold">{country}</h3>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.h2
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl font-bold text-white mb-6"
          >
            Ready to Work <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Together</span>?
          </motion.h2>
          <motion.p
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl text-gray-400 mb-8 max-w-2xl mx-auto"
          >
            Ready to transform your digital presence? Join 150+ companies who've chosen Delta Xero Creations
            as their trusted technology partner. Let's build something extraordinary together.
          </motion.p>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
            className="flex items-center justify-center space-x-8 mb-8 text-sm text-gray-400"
          >
            <div className="flex items-center space-x-2">
              <CheckCircle className="text-green-400" size={16} />
              <span>Free Consultation</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="text-green-400" size={16} />
              <span>No Hidden Costs</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="text-green-400" size={16} />
              <span>24/7 Support</span>
            </div>
          </motion.div>
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-12 py-4 rounded-lg font-semibold text-lg hover:shadow-lg hover:shadow-primary-500/25 transition-all duration-300"
            >
              Start Your Project
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="border-2 border-primary-500 text-primary-400 px-12 py-4 rounded-lg font-semibold text-lg hover:bg-primary-500/10 transition-all duration-300"
            >
              Schedule a Call
            </motion.button>
          </motion.div>
        </div>
      </section>
    </motion.div>
  )
}

export default About
