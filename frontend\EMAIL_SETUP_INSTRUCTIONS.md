# Email Service Setup Instructions

## Overview
The automated email system is now integrated into your Delta Xero Creations application. This system sends:

1. **Application Confirmations** - To applicants when they submit job/internship applications
2. **Admin Notifications** - To HR team when new applications are received
3. **User Credentials** - To new users when admin creates their accounts

## EmailJS Setup Required

### Step 1: Create EmailJS Account
1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Sign up for a free account
3. Verify your email address

### Step 2: Create Email Service
1. In EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider (Gmail, Outlook, etc.)
4. Configure with your email credentials
5. Note the **Service ID** (e.g., `service_deltaxero`)

### Step 3: Create Email Templates
Create these 4 templates in EmailJS:

#### Template 1: Application Confirmation (`template_app_confirm`)
```
Subject: {{subject}}

{{message}}

---
This is an automated message from Delta Xero Creations.
```

#### Template 2: New User Credentials (`template_new_user`)
```
Subject: {{subject}}

{{message}}

---
Delta Xero Creations IT Team
```

### Step 4: Get Public Key
1. Go to "Account" in EmailJS dashboard
2. Copy your **Public Key**

### Step 5: Update Configuration
Update the configuration in `frontend/src/lib/emailService.js`:

```javascript
const EMAILJS_CONFIG = {
  serviceId: 'your_service_id_here',        // From Step 2
  templateIds: {
    applicationConfirmation: 'template_app_confirm',  // From Step 3
    newUserCredentials: 'template_new_user'           // From Step 3
  },
  publicKey: 'your_public_key_here'         // From Step 4
}
```

## Email Templates Content

### Job Application Confirmation
- **To**: Applicant's email
- **Subject**: Application Received - [Position]
- **Content**: Confirmation with application details and next steps

### Internship Application Confirmation
- **To**: Applicant's email
- **Subject**: Internship Application Received - [Position]
- **Content**: Confirmation with internship details and timeline

### Admin Notifications
- **To**: <EMAIL>
- **Subject**: New [Job/Internship] Application - [Position]
- **Content**: Applicant details and application summary

### New User Credentials
- **To**: New user's email
- **Subject**: Your Delta Xero Creations Admin Account
- **Content**: Login credentials and security instructions

## Testing the Email System

### Test Application Emails
1. Submit a job application through the form
2. Check applicant email for confirmation
3. Check <EMAIL> for admin notification

### Test User Creation Emails
1. Create a new user in admin panel
2. Check new user's email for credentials

## Email Service Features

### Batch Email Sending
The system can send multiple emails simultaneously:
- Applicant confirmation + Admin notification
- Error handling for individual email failures

### Error Handling
- Emails failures don't break application submission
- Console logging for debugging
- Graceful fallbacks

### Security Features
- Temporary password generation
- Secure credential transmission
- Professional email templates

## Customization Options

### Email Templates
You can customize the email content in `emailService.js`:
- Update company branding
- Modify message content
- Add additional fields

### Email Recipients
Update admin email addresses:
```javascript
// In EMAIL_TEMPLATES
to_email: '<EMAIL>'
```

### Additional Email Types
Add new email types by:
1. Creating new template in EmailJS
2. Adding template function in `EMAIL_TEMPLATES`
3. Creating service function in `emailService`

## Troubleshooting

### Common Issues
1. **Emails not sending**: Check EmailJS configuration
2. **Template errors**: Verify template IDs match
3. **Service errors**: Check service ID and public key

### Debug Mode
Enable console logging to debug email issues:
```javascript
console.log('Email results:', emailResults)
```

### Rate Limits
EmailJS free tier has limits:
- 200 emails/month
- Upgrade for higher limits

## Production Considerations

### Email Delivery
- Set up proper SPF/DKIM records
- Use professional email service
- Monitor delivery rates

### Error Monitoring
- Implement proper error logging
- Set up email delivery monitoring
- Create fallback notification system

### Compliance
- Add unsubscribe options
- Follow email marketing laws
- Implement data privacy measures

## Support
For issues with email setup:
1. Check EmailJS documentation
2. Verify configuration values
3. Test with simple templates first
4. Contact EmailJS support if needed
