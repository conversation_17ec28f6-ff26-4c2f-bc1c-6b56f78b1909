import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import { MapPin, Clock, DollarSign, Users, Code, Briefcase, GraduationCap, Heart, Globe, Zap, Award, Coffee, Laptop, Plane, BookOpen, Target, Shield, Star, TrendingUp, Calendar, Building, ArrowRight, CheckCircle } from 'lucide-react'

const Jobs = () => {
  const [activeTab, setActiveTab] = useState('jobs')
  const [selectedJob, setSelectedJob] = useState(null)

  const jobs = [
    {
      id: 1,
      title: 'Senior React Developer',
      type: 'Full-time',
      location: 'Remote',
      salary: '$85,000 - $120,000',
      experience: '4+ years',
      department: 'Engineering',
      posted: '2 days ago',
      applicants: 12,
      urgent: false,
      featured: true,
      tags: ['React', 'TypeScript', 'Next.js', 'GraphQL'],
      description: 'Join our engineering team to build cutting-edge web applications using React and modern JavaScript. You\'ll work on high-impact projects for clients ranging from startups to Fortune 500 companies.',
      requirements: [
        'Strong proficiency in React.js, Next.js, and TypeScript',
        'Experience with modern JavaScript (ES6+) and CSS-in-JS solutions',
        'Knowledge of state management (Redux Toolkit, Zustand, Context API)',
        'Familiarity with RESTful APIs, GraphQL, and real-time technologies',
        'Experience with testing frameworks (Jest, React Testing Library, Cypress)',
        'Understanding of performance optimization and accessibility standards',
        'Experience with Git workflows and CI/CD pipelines'
      ],
      benefits: [
        'Competitive salary with annual reviews',
        'Equity participation program',
        'Flexible working hours (async-first)',
        'Premium health, dental, and vision insurance',
        '$3000 annual learning & development budget',
        'Latest MacBook Pro and 4K monitor setup',
        'Unlimited PTO and sabbatical options'
      ]
    },
    {
      id: 2,
      title: 'Senior UI/UX Designer',
      type: 'Full-time',
      location: 'Remote',
      salary: '$75,000 - $95,000',
      experience: '3+ years',
      department: 'Design',
      posted: '1 week ago',
      applicants: 8,
      urgent: false,
      featured: false,
      tags: ['Figma', 'Design Systems', 'User Research', 'Prototyping'],
      description: 'Lead design initiatives for complex web and mobile applications. You\'ll work closely with product managers and developers to create user-centered designs that solve real business problems.',
      requirements: [
        'Expert proficiency in Figma, Adobe Creative Suite, and prototyping tools',
        'Strong understanding of design systems and component libraries',
        'Experience with user research, usability testing, and data-driven design',
        'Knowledge of responsive design and mobile-first principles',
        'Portfolio demonstrating end-to-end design process and impact',
        'Understanding of front-end development constraints and possibilities',
        'Experience with design handoff tools and developer collaboration'
      ],
      benefits: [
        'Creative freedom with strategic impact',
        'Latest design tools and software licenses',
        'Collaborative, design-forward team environment',
        'Flexible schedule with core collaboration hours',
        'Annual design conference attendance ($2500 budget)',
        'Mentorship opportunities with junior designers',
        'Access to user research tools and platforms'
      ]
    },
    {
      id: 3,
      title: 'Mobile App Developer',
      type: 'Full-time',
      location: 'Remote',
      salary: '$70,000 - $95,000',
      experience: '3+ years',
      department: 'Engineering',
      posted: '3 days ago',
      applicants: 15,
      urgent: true,
      featured: false,
      tags: ['React Native', 'Flutter', 'iOS', 'Android'],
      description: 'Build innovative mobile applications using React Native and Flutter. You\'ll work on diverse projects from fintech apps to social platforms, ensuring optimal performance across iOS and Android.',
      requirements: [
        'Strong experience with React Native and/or Flutter',
        'Knowledge of native iOS (Swift) and Android (Kotlin) development',
        'Understanding of mobile UI/UX principles and platform guidelines',
        'Experience with mobile app deployment and CI/CD pipelines',
        'Familiarity with mobile testing frameworks and debugging tools',
        'App store submission and review process experience',
        'Knowledge of mobile security best practices'
      ],
      benefits: [
        'Work on cutting-edge mobile technologies',
        'Flexible remote work environment',
        'Device testing lab access',
        'Mobile development conference attendance',
        'Opportunity to mentor junior developers',
        'Cross-platform expertise development',
        'Direct client interaction opportunities'
      ]
    },
    {
      id: 4,
      title: 'DevOps Engineer',
      type: 'Full-time',
      location: 'Remote',
      salary: '$90,000 - $125,000',
      experience: '4+ years',
      department: 'Infrastructure',
      posted: '5 days ago',
      applicants: 6,
      urgent: false,
      featured: true,
      tags: ['AWS', 'Kubernetes', 'Docker', 'Terraform'],
      description: 'Lead our infrastructure and deployment strategies. You\'ll design scalable cloud architectures and implement robust CI/CD pipelines that enable our development teams to ship with confidence.',
      requirements: [
        'Strong experience with AWS, Azure, or Google Cloud Platform',
        'Expertise in containerization (Docker, Kubernetes)',
        'Proficiency in Infrastructure as Code (Terraform, CloudFormation)',
        'Experience with CI/CD tools (GitHub Actions, Jenkins, GitLab CI)',
        'Knowledge of monitoring and logging solutions (DataDog, ELK stack)',
        'Understanding of security best practices and compliance',
        'Scripting skills in Python, Bash, or similar languages'
      ],
      benefits: [
        'Shape the technical infrastructure of growing company',
        'Work with latest cloud technologies',
        'Flexible schedule with on-call rotation',
        'Cloud certification reimbursement',
        'Home lab equipment stipend',
        'Direct impact on product reliability',
        'Mentorship and knowledge sharing opportunities'
      ]
    },
    {
      id: 5,
      title: 'Product Manager',
      type: 'Full-time',
      location: 'Remote',
      salary: '$95,000 - $130,000',
      experience: '5+ years',
      department: 'Product',
      posted: '1 day ago',
      applicants: 4,
      urgent: true,
      featured: true,
      tags: ['Strategy', 'Analytics', 'Agile', 'Stakeholder Management'],
      description: 'Drive product strategy and execution for our client projects and internal tools. You\'ll work at the intersection of business, technology, and user experience to deliver products that create real value.',
      requirements: [
        'Proven track record in product management for digital products',
        'Strong analytical skills and data-driven decision making',
        'Experience with agile development methodologies',
        'Excellent communication and stakeholder management skills',
        'Understanding of technical constraints and possibilities',
        'Experience with product analytics tools (Mixpanel, Amplitude)',
        'Background in user research and customer development'
      ],
      benefits: [
        'Strategic influence on company direction',
        'Work with diverse client portfolio',
        'Flexible schedule with core collaboration hours',
        'Product management conference attendance',
        'Access to premium analytics and research tools',
        'Direct client interaction and relationship building',
        'Opportunity to build and lead product team'
      ]
    }
  ]

  const internships = [
    {
      id: 1,
      title: 'Frontend Development Intern',
      duration: '3-6 months',
      location: 'Remote',
      stipend: '$800/month',
      department: 'Engineering',
      posted: '1 week ago',
      applicants: 25,
      spots: 2,
      commitment: '20-25 hours/week',
      startDate: 'June 2025',
      tags: ['React', 'JavaScript', 'HTML/CSS', 'Git'],
      description: 'Learn modern web development while working on real client projects. You\'ll be mentored by senior developers and contribute to production applications.',
      requirements: [
        'Basic knowledge of HTML, CSS, JavaScript',
        'Familiarity with React (preferred)',
        'Eagerness to learn and grow',
        'Good communication skills',
        'Available for 20+ hours per week'
      ]
    },
    {
      id: 2,
      title: 'UI/UX Design Intern',
      duration: '3-4 months',
      location: 'Remote',
      stipend: '$700/month',
      department: 'Design',
      posted: '3 days ago',
      applicants: 18,
      spots: 1,
      commitment: '15-20 hours/week',
      startDate: 'July 2025',
      tags: ['Figma', 'UI Design', 'User Research', 'Prototyping'],
      description: 'Work alongside our design team to create stunning visual experiences. You\'ll participate in the full design process from research to final implementation.',
      requirements: [
        'Basic design skills and portfolio',
        'Knowledge of design tools (Figma, Photoshop)',
        'Understanding of design principles',
        'Creative thinking and problem-solving',
        'Available for 15+ hours per week'
      ]
    },
    {
      id: 3,
      title: 'Marketing Intern',
      duration: '4-6 months',
      location: 'Remote',
      stipend: '$600/month',
      department: 'Marketing',
      posted: '2 days ago',
      applicants: 12,
      spots: 1,
      commitment: '15-20 hours/week',
      startDate: 'June 2025',
      tags: ['Content Marketing', 'Social Media', 'Analytics', 'SEO'],
      description: 'Help grow our brand presence and support marketing initiatives. You\'ll work on content creation, social media management, and marketing analytics.',
      requirements: [
        'Strong written communication skills',
        'Basic understanding of digital marketing',
        'Familiarity with social media platforms',
        'Creative mindset and attention to detail',
        'Available for 15+ hours per week'
      ]
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen -mt-16 lg:-mt-20"
    >
      {/* Hero Section */}
      <section className="relative py-32 bg-gradient-to-br from-dark-950 via-dark-900 to-primary-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <motion.h1
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              className="text-5xl md:text-6xl font-bold text-white mb-6"
            >
              Build the Future <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">With Us</span>
            </motion.h1>
            <motion.p
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-xl text-gray-300 max-w-3xl mx-auto mb-8"
            >
              Join a team of passionate innovators who are transforming ideas into digital reality.
              At Delta Xero Creations, we don't just build software—we craft experiences that matter.
            </motion.p>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex flex-wrap justify-center gap-8 text-sm text-gray-400"
            >
              <div className="flex items-center space-x-2">
                <Globe className="text-primary-400" size={16} />
                <span>Remote-First Culture</span>
              </div>
              <div className="flex items-center space-x-2">
                <Heart className="text-primary-400" size={16} />
                <span>Work-Life Balance</span>
              </div>
              <div className="flex items-center space-x-2">
                <Zap className="text-primary-400" size={16} />
                <span>Cutting-Edge Tech</span>
              </div>
              <div className="flex items-center space-x-2">
                <Award className="text-primary-400" size={16} />
                <span>Growth Opportunities</span>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Tabs */}
      <section className="py-8 bg-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-center">
            <div className="bg-dark-800 p-2 rounded-lg">
              <button
                onClick={() => setActiveTab('jobs')}
                className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                  activeTab === 'jobs'
                    ? 'bg-primary-500 text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <Briefcase className="inline-block mr-2" size={20} />
                Job Openings
              </button>
              <button
                onClick={() => setActiveTab('internships')}
                className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                  activeTab === 'internships'
                    ? 'bg-primary-500 text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <GraduationCap className="inline-block mr-2" size={20} />
                Internships
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Jobs/Internships Content */}
      <section className="py-12 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {activeTab === 'jobs' ? (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Job Listings */}
              <div className="lg:col-span-2 space-y-6">
                {jobs.map((job) => (
                  <motion.div
                    key={job.id}
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    className={`relative bg-dark-800/50 backdrop-blur-sm p-6 rounded-2xl border cursor-pointer transition-all duration-300 group overflow-hidden ${
                      selectedJob?.id === job.id
                        ? 'border-primary-500 bg-primary-500/5 shadow-lg shadow-primary-500/10'
                        : 'border-gray-700 hover:border-primary-500/50 hover:bg-dark-800/70'
                    }`}
                    onClick={() => setSelectedJob(job)}
                    whileHover={{ y: -2 }}
                  >
                    {/* Featured/Urgent Badges */}
                    <div className="absolute top-4 right-4 flex gap-2">
                      {job.featured && (
                        <div className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
                          <Star size={12} className="mr-1" />
                          Featured
                        </div>
                      )}
                      {job.urgent && (
                        <div className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
                          <TrendingUp size={12} className="mr-1" />
                          Urgent
                        </div>
                      )}
                    </div>

                    {/* Header */}
                    <div className="mb-4 pr-20">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-xl font-semibold text-white group-hover:text-primary-400 transition-colors duration-300">
                          {job.title}
                        </h3>
                        <span className="bg-primary-500/20 text-primary-400 px-3 py-1 rounded-full text-sm font-medium">
                          {job.type}
                        </span>
                      </div>

                      <div className="flex items-center gap-4 text-sm text-gray-400 mb-3">
                        <div className="flex items-center">
                          <Building size={14} className="mr-1" />
                          {job.department}
                        </div>
                        <div className="flex items-center">
                          <Calendar size={14} className="mr-1" />
                          {job.posted}
                        </div>
                        <div className="flex items-center">
                          <Users size={14} className="mr-1" />
                          {job.applicants} applicants
                        </div>
                      </div>

                      <p className="text-gray-300 leading-relaxed line-clamp-2">{job.description}</p>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {job.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="bg-gray-700/50 text-gray-300 px-2 py-1 rounded-md text-xs font-medium hover:bg-primary-500/20 hover:text-primary-400 transition-colors duration-200"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>

                    {/* Footer */}
                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-4 text-sm text-gray-400">
                        <div className="flex items-center">
                          <MapPin size={16} className="mr-1 text-primary-400" />
                          {job.location}
                        </div>
                        <div className="flex items-center">
                          <DollarSign size={16} className="mr-1 text-green-400" />
                          {job.salary}
                        </div>
                        <div className="flex items-center">
                          <Clock size={16} className="mr-1 text-blue-400" />
                          {job.experience}
                        </div>
                      </div>

                      <div className="flex items-center text-primary-400 text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <span className="mr-1">View Details</span>
                        <ArrowRight size={16} className="group-hover:translate-x-1 transition-transform duration-300" />
                      </div>
                    </div>

                    {/* Apply Button */}
                    <div className="mt-4 pt-4 border-t border-gray-700">
                      <Link
                        to={`/apply/job?position=${encodeURIComponent(job.title)}&department=${encodeURIComponent(job.department)}&type=${encodeURIComponent(job.type)}&location=${encodeURIComponent(job.location)}`}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <motion.button
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          className="w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-2 px-4 rounded-lg font-medium hover:shadow-lg hover:shadow-primary-500/25 transition-all duration-300 flex items-center justify-center"
                        >
                          <span>Apply Now</span>
                          <ArrowRight size={14} className="ml-2" />
                        </motion.button>
                      </Link>
                    </div>

                    {/* Hover Effect Gradient */}
                    <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                  </motion.div>
                ))}
              </div>

              {/* Job Details */}
              <div className="lg:col-span-1">
                {selectedJob ? (
                  <motion.div
                    initial={{ x: 20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    className="bg-dark-800 p-6 rounded-xl border border-gray-700 sticky top-8"
                  >
                    <h3 className="text-2xl font-semibold text-white mb-4">{selectedJob.title}</h3>
                    
                    <div className="space-y-6">
                      <div>
                        <h4 className="text-lg font-semibold text-white mb-2">Requirements</h4>
                        <ul className="space-y-2">
                          {selectedJob.requirements.map((req, index) => (
                            <li key={index} className="text-gray-400 flex items-start">
                              <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                              {req}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="text-lg font-semibold text-white mb-2">Benefits</h4>
                        <ul className="space-y-2">
                          {selectedJob.benefits.map((benefit, index) => (
                            <li key={index} className="text-gray-400 flex items-start">
                              <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                              {benefit}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                    
                    <Link to={selectedJob ? `/apply/job?position=${encodeURIComponent(selectedJob.title)}&department=${encodeURIComponent(selectedJob.department)}&type=${encodeURIComponent(selectedJob.type)}&location=${encodeURIComponent(selectedJob.location)}` : '/apply/job'}>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-3 rounded-lg font-semibold mt-6 hover:shadow-lg hover:shadow-primary-500/25 transition-all duration-300"
                      >
                        Apply Now
                      </motion.button>
                    </Link>
                  </motion.div>
                ) : (
                  <div className="bg-dark-800 p-6 rounded-xl border border-gray-700 text-center">
                    <Code className="mx-auto mb-4 text-gray-400" size={48} />
                    <p className="text-gray-400">Select a job to view details</p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Internships */
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {internships.map((internship) => (
                <motion.div
                  key={internship.id}
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  className="relative bg-dark-800/50 backdrop-blur-sm p-6 rounded-2xl border border-gray-700 hover:border-primary-500/50 transition-all duration-300 group overflow-hidden"
                  whileHover={{ y: -5 }}
                >
                  {/* Header */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="bg-gradient-to-r from-green-500 to-teal-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center">
                        <GraduationCap size={12} className="mr-1" />
                        Internship
                      </div>
                      <div className="text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-md">
                        {internship.spots} spot{internship.spots > 1 ? 's' : ''} left
                      </div>
                    </div>

                    <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-primary-400 transition-colors duration-300">
                      {internship.title}
                    </h3>

                    <div className="flex items-center gap-4 text-sm text-gray-400 mb-3">
                      <div className="flex items-center">
                        <Building size={14} className="mr-1" />
                        {internship.department}
                      </div>
                      <div className="flex items-center">
                        <Calendar size={14} className="mr-1" />
                        {internship.posted}
                      </div>
                    </div>

                    <p className="text-gray-300 leading-relaxed text-sm">{internship.description}</p>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {internship.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-gray-700/50 text-gray-300 px-2 py-1 rounded-md text-xs font-medium"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Details Grid */}
                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div className="bg-dark-700/50 p-3 rounded-lg">
                      <div className="flex items-center text-blue-400 mb-1">
                        <Clock size={14} className="mr-1" />
                        <span className="font-medium">Duration</span>
                      </div>
                      <div className="text-gray-300">{internship.duration}</div>
                    </div>

                    <div className="bg-dark-700/50 p-3 rounded-lg">
                      <div className="flex items-center text-green-400 mb-1">
                        <DollarSign size={14} className="mr-1" />
                        <span className="font-medium">Stipend</span>
                      </div>
                      <div className="text-gray-300">{internship.stipend}</div>
                    </div>

                    <div className="bg-dark-700/50 p-3 rounded-lg">
                      <div className="flex items-center text-purple-400 mb-1">
                        <MapPin size={14} className="mr-1" />
                        <span className="font-medium">Location</span>
                      </div>
                      <div className="text-gray-300">{internship.location}</div>
                    </div>

                    <div className="bg-dark-700/50 p-3 rounded-lg">
                      <div className="flex items-center text-orange-400 mb-1">
                        <Target size={14} className="mr-1" />
                        <span className="font-medium">Commitment</span>
                      </div>
                      <div className="text-gray-300">{internship.commitment}</div>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between mb-4 text-xs text-gray-400">
                    <div className="flex items-center">
                      <Users size={12} className="mr-1" />
                      {internship.applicants} applicants
                    </div>
                    <div className="flex items-center">
                      <Calendar size={12} className="mr-1" />
                      Starts {internship.startDate}
                    </div>
                  </div>

                  <Link to={`/apply/internship?position=${encodeURIComponent(internship.title)}&department=${encodeURIComponent(internship.department)}&duration=${encodeURIComponent(internship.duration)}&location=${encodeURIComponent(internship.location)}&commitment=${encodeURIComponent(internship.commitment)}`}>
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-3 rounded-lg font-semibold hover:shadow-lg hover:shadow-primary-500/25 transition-all duration-300 flex items-center justify-center"
                    >
                      <span>Apply for Internship</span>
                      <ArrowRight size={16} className="ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </motion.button>
                  </Link>

                  {/* Hover Effect Gradient */}
                  <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Why Work With Us */}
      <section className="py-20 bg-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Why Work <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">With Us</span>?
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: Users,
                title: 'Great Team',
                description: 'Work with passionate and talented individuals who love what they do'
              },
              {
                icon: Code,
                title: 'Latest Tech',
                description: 'Use cutting-edge technologies and tools in your daily work'
              },
              {
                icon: GraduationCap,
                title: 'Growth',
                description: 'Continuous learning opportunities and career development'
              }
            ].map((item, index) => {
              const Icon = item.icon
              return (
                <motion.div
                  key={index}
                  initial={{ y: 50, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="bg-gradient-to-br from-primary-500/20 to-primary-600/20 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Icon className="text-primary-400" size={32} />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">{item.title}</h3>
                  <p className="text-gray-400">{item.description}</p>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Benefits & Perks Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-4xl md:text-5xl font-bold text-white mb-4"
            >
              Benefits & <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Perks</span>
            </motion.h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              We believe in taking care of our team with comprehensive benefits that support your health, growth, and happiness.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: Heart,
                title: "Health & Wellness",
                benefits: [
                  "Premium health insurance",
                  "Mental health support",
                  "Fitness membership",
                  "Annual health checkups"
                ],
                color: "from-red-500 to-pink-500"
              },
              {
                icon: Laptop,
                title: "Work Setup",
                benefits: [
                  "Latest MacBook Pro/PC",
                  "4K monitor setup",
                  "Ergonomic workspace",
                  "Home office stipend"
                ],
                color: "from-blue-500 to-cyan-500"
              },
              {
                icon: BookOpen,
                title: "Learning & Growth",
                benefits: [
                  "$2000 learning budget",
                  "Conference attendance",
                  "Online course access",
                  "Mentorship programs"
                ],
                color: "from-green-500 to-teal-500"
              },
              {
                icon: Plane,
                title: "Time Off & Travel",
                benefits: [
                  "Unlimited PTO",
                  "Sabbatical options",
                  "Travel allowance",
                  "Team retreats"
                ],
                color: "from-purple-500 to-indigo-500"
              }
            ].map((category, index) => {
              const Icon = category.icon
              return (
                <motion.div
                  key={index}
                  initial={{ y: 50, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-dark-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700 hover:border-primary-500/50 transition-all duration-300 group"
                >
                  <div className={`w-12 h-12 bg-gradient-to-r ${category.color} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="text-white" size={24} />
                  </div>

                  <h3 className="text-lg font-semibold text-white mb-4">{category.title}</h3>

                  <ul className="space-y-2">
                    {category.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="text-gray-400 text-sm flex items-start">
                        <div className="w-1 h-1 bg-primary-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Employee Testimonials Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-4xl md:text-5xl font-bold text-white mb-4"
            >
              What Our <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Team Says</span>
            </motion.h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Hear from our team members about their experience working at Delta Xero Creations.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "Arjun Malhotra",
                role: "Senior Frontend Developer",
                image: "https://placehold.co/400x400/4F46E5/FFFFFF?text=AM",
                testimonial: "Delta Xero mein learning opportunities incredible hain. 2 saal mein jo growth mila hai, wo previous 5 years mein nahi mila tha. Team genuinely care karti hai development ke liye.",
                rating: 5
              },
              {
                name: "Kavya Nair",
                role: "UX Designer",
                image: "https://placehold.co/400x400/EC4899/FFFFFF?text=KN",
                testimonial: "Remote work karte hue strong team connections maintain karna mushkil lagta tha, but Delta Xero mein culture genuinely collaborative aur supportive hai. Best decision ever!",
                rating: 5
              },
              {
                name: "Rohit Agarwal",
                role: "Full Stack Developer",
                image: "https://placehold.co/400x400/10B981/FFFFFF?text=RA",
                testimonial: "Work-life balance yahan real hai. Apne passions pursue kar sakta hun aur saath mein meaningful projects par contribute kar sakta hun. Perfect environment!",
                rating: 5
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="bg-dark-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 hover:border-primary-500/50 transition-all duration-300"
              >
                <div className="flex items-center mb-6">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full object-cover mr-4"
                  />
                  <div>
                    <h3 className="text-white font-semibold">{testimonial.name}</h3>
                    <p className="text-primary-400 text-sm">{testimonial.role}</p>
                  </div>
                </div>

                <p className="text-gray-300 leading-relaxed mb-6 italic">
                  "{testimonial.testimonial}"
                </p>

                <div className="flex space-x-1">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <div key={i} className="w-4 h-4 bg-primary-400 rounded-full"></div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Company Culture Section */}
      <section className="py-20 bg-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ x: -50, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Our <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Culture</span>
              </h2>
              <p className="text-gray-300 text-lg mb-8 leading-relaxed">
                We've built a culture where innovation thrives, collaboration is natural, and every voice matters.
                Our remote-first approach brings together diverse talents from around the world.
              </p>

              <div className="space-y-6">
                {[
                  {
                    title: "Innovation First",
                    description: "We encourage experimentation and embrace new technologies to stay ahead of the curve."
                  },
                  {
                    title: "Collaborative Spirit",
                    description: "Cross-functional teams work together seamlessly to deliver exceptional results."
                  },
                  {
                    title: "Continuous Learning",
                    description: "We invest in our team's growth with learning opportunities and skill development."
                  },
                  {
                    title: "Work-Life Balance",
                    description: "Flexible schedules and remote work options help you maintain a healthy balance."
                  }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ y: 30, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-start space-x-4"
                  >
                    <div className="bg-gradient-to-br from-primary-500/20 to-primary-600/20 p-2 rounded-lg flex-shrink-0 mt-1">
                      <div className="w-2 h-2 bg-primary-400 rounded-full"></div>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold mb-2">{item.title}</h3>
                      <p className="text-gray-400 text-sm leading-relaxed">{item.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ x: 50, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-primary-500/10 to-primary-600/10 rounded-2xl p-8 border border-primary-500/20">
                <h3 className="text-xl font-semibold text-white mb-6 text-center">Life at Delta Xero</h3>

                <div className="grid grid-cols-2 gap-6 mb-8">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-400 mb-2">95%</div>
                    <p className="text-gray-400 text-sm">Employee Satisfaction</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-400 mb-2">4.8/5</div>
                    <p className="text-gray-400 text-sm">Glassdoor Rating</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-400 mb-2">25+</div>
                    <p className="text-gray-400 text-sm">Countries Represented</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-400 mb-2">100%</div>
                    <p className="text-gray-400 text-sm">Remote Flexibility</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Coffee className="text-primary-400" size={16} />
                    <span className="text-gray-300 text-sm">Virtual coffee chats & team bonding</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Target className="text-primary-400" size={16} />
                    <span className="text-gray-300 text-sm">Clear career progression paths</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Shield className="text-primary-400" size={16} />
                    <span className="text-gray-300 text-sm">Inclusive & diverse environment</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Application Process Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-4xl md:text-5xl font-bold text-white mb-4"
            >
              Our Hiring <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Process</span>
            </motion.h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              We've designed a transparent and efficient hiring process that respects your time while helping us find the perfect fit.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "Application",
                description: "Submit your application with resume and portfolio. We review every application carefully.",
                duration: "1-2 days",
                icon: Briefcase
              },
              {
                step: "02",
                title: "Initial Call",
                description: "30-minute conversation with our HR team to discuss your background and interests.",
                duration: "30 minutes",
                icon: Users
              },
              {
                step: "03",
                title: "Technical Interview",
                description: "Technical discussion and coding challenge relevant to the role you're applying for.",
                duration: "1-2 hours",
                icon: Code
              },
              {
                step: "04",
                title: "Final Interview",
                description: "Meet the team and discuss culture fit, career goals, and answer any questions you have.",
                duration: "45 minutes",
                icon: Heart
              }
            ].map((step, index) => {
              const Icon = step.icon
              return (
                <motion.div
                  key={index}
                  initial={{ y: 50, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="relative"
                >
                  {/* Connection Line */}
                  {index < 3 && (
                    <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-primary-500 to-primary-600 z-0"></div>
                  )}

                  <div className="bg-dark-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700 hover:border-primary-500/50 transition-all duration-300 relative z-10">
                    <div className="flex items-center justify-between mb-4">
                      <div className="bg-gradient-to-r from-primary-500 to-primary-600 text-white text-sm font-bold px-3 py-1 rounded-full">
                        {step.step}
                      </div>
                      <Icon className="text-primary-400" size={24} />
                    </div>

                    <h3 className="text-xl font-semibold text-white mb-3">{step.title}</h3>
                    <p className="text-gray-400 text-sm mb-4 leading-relaxed">{step.description}</p>

                    <div className="flex items-center space-x-2">
                      <Clock className="text-primary-400" size={14} />
                      <span className="text-primary-400 text-xs font-medium">{step.duration}</span>
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>

          <motion.div
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center mt-16"
          >
            <div className="bg-gradient-to-r from-primary-500/10 to-primary-600/10 rounded-2xl p-8 border border-primary-500/20 max-w-4xl mx-auto">
              <h3 className="text-2xl font-semibold text-white mb-4">Ready to Join Our Team?</h3>
              <p className="text-gray-300 mb-6 leading-relaxed">
                We're always looking for talented individuals who share our passion for creating exceptional digital experiences.
                Even if you don't see a perfect match above, we'd love to hear from you.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/apply/job">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg hover:shadow-primary-500/25 transition-all duration-300"
                  >
                    Apply for Job
                  </motion.button>
                </Link>
                <Link to="/apply/internship">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="border-2 border-primary-500 text-primary-400 px-8 py-3 rounded-lg font-semibold hover:bg-primary-500/10 transition-all duration-300"
                  >
                    Apply for Internship
                  </motion.button>
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </motion.div>
  )
}

export default Jobs
