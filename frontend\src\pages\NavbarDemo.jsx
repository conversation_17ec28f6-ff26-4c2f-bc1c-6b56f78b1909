import React from 'react';
import { motion } from 'framer-motion';
import ModernLayout from '../components/ModernLayout';

const NavbarDemo = () => {
  return (
    <div className="min-h-screen bg-dark-950">
      {/* Hero Section */}
      <section className="min-h-screen flex items-center justify-center relative overflow-hidden">
        {/* Background Animation */}
        <div className="absolute inset-0 bg-gradient-to-br from-dark-950 via-dark-900 to-primary-950">
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 left-20 w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <div className="absolute top-40 right-32 w-1 h-1 bg-primary-400 rounded-full animate-pulse delay-1000"></div>
            <div className="absolute bottom-32 left-1/4 w-1.5 h-1.5 bg-white rounded-full animate-pulse delay-2000"></div>
            <div className="absolute bottom-20 right-20 w-1 h-1 bg-primary-400 rounded-full animate-pulse delay-3000"></div>
          </div>
        </div>

        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
              Modern <span className="bg-gradient-to-r from-primary-400 via-primary-500 to-primary-600 bg-clip-text text-transparent">Navbar</span> Component
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
              Experience our beautiful, animated navigation menu with hover effects, smooth transitions, and responsive design.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-8 py-4 rounded-lg font-semibold hover:shadow-lg hover:shadow-primary-500/25 transition-all duration-300"
              >
                Try Hovering the Menu
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-primary-500 text-primary-400 px-8 py-4 rounded-lg font-semibold hover:bg-primary-500/10 transition-all duration-300"
              >
                View Documentation
              </motion.button>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-dark-800/30 rounded-xl p-6 border border-gray-700/50"
              >
                <h3 className="text-xl font-semibold text-white mb-3">Smooth Animations</h3>
                <p className="text-gray-400">Framer Motion powered animations with spring physics for natural feel</p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-dark-800/30 rounded-xl p-6 border border-gray-700/50"
              >
                <h3 className="text-xl font-semibold text-white mb-3">Responsive Design</h3>
                <p className="text-gray-400">Adapts beautifully to all screen sizes with mobile-first approach</p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="bg-dark-800/30 rounded-xl p-6 border border-gray-700/50"
              >
                <h3 className="text-xl font-semibold text-white mb-3">Easy Integration</h3>
                <p className="text-gray-400">Drop-in replacement for existing navigation with minimal setup</p>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Instructions Section */}
      <section className="py-20 bg-dark-900/50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-white mb-4">
              How to Use
            </h2>
            <p className="text-xl text-gray-400">
              Simple steps to integrate the modern navbar into your application
            </p>
          </motion.div>

          <div className="space-y-8">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-dark-800/50 rounded-xl p-6 border border-gray-700"
            >
              <h3 className="text-xl font-semibold text-white mb-3">1. Replace Layout Component</h3>
              <p className="text-gray-300 mb-4">
                Import and use ModernLayout instead of the default Layout component:
              </p>
              <div className="bg-dark-900 rounded-lg p-4 font-mono text-sm text-gray-300">
                <code>
                  {`import ModernLayout from './components/ModernLayout'

// Replace Layout with ModernLayout in App.jsx
<ModernLayout>
  {children}
</ModernLayout>`}
                </code>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-dark-800/50 rounded-xl p-6 border border-gray-700"
            >
              <h3 className="text-xl font-semibold text-white mb-3">2. Customize Menu Items</h3>
              <p className="text-gray-300 mb-4">
                Edit the ModernNavbar component to customize menu items and links:
              </p>
              <div className="bg-dark-900 rounded-lg p-4 font-mono text-sm text-gray-300">
                <code>
                  {`// In ModernNavbar.tsx
<MenuItem setActive={setActive} active={active} item="Your Menu">
  <div className="flex flex-col space-y-4 text-sm">
    <HoveredLink to="/your-route">Your Link</HoveredLink>
  </div>
</MenuItem>`}
                </code>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-dark-800/50 rounded-xl p-6 border border-gray-700"
            >
              <h3 className="text-xl font-semibold text-white mb-3">3. Style Customization</h3>
              <p className="text-gray-300 mb-4">
                The navbar uses your existing Tailwind theme colors and can be easily customized:
              </p>
              <div className="bg-dark-900 rounded-lg p-4 font-mono text-sm text-gray-300">
                <code>
                  {`// Customize colors in tailwind.config.js
colors: {
  primary: { /* your brand colors */ },
  dark: { /* your dark theme colors */ }
}`}
                </code>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default NavbarDemo;
