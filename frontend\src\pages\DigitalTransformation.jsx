import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Zap, TrendingUp, Users, Globe, Cpu, Cloud, ArrowRight, CheckCircle, Star, Rocket, BarChart3, Gauge, Target, Settings, Layers, Play } from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const DigitalTransformation = () => {
  const statsRef = useRef(null)

  useEffect(() => {
    // Stats counter animation
    gsap.fromTo('.stat-counter',
      { textContent: 0 },
      {
        textContent: (i, target) => target.getAttribute('data-value'),
        duration: 2,
        ease: 'power2.out',
        snap: { textContent: 1 },
        scrollTrigger: {
          trigger: statsRef.current,
          start: 'top 80%',
        }
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const transformationServices = [
    {
      icon: Cloud,
      title: 'Cloud Migration & Modernization',
      description: 'Seamlessly migrate your infrastructure to the cloud with zero downtime, enhanced security, and scalable architecture for future growth.',
      features: ['AWS/Azure/GCP Migration', 'Microservices Architecture', 'DevOps Implementation', 'Security & Compliance'],
      color: 'from-blue-500 to-indigo-500',
      price: 'From $25,000',
      timeline: '12-20 weeks',
      metrics: { efficiency: '+200% performance', cost: '40% cost reduction', uptime: '99.99% availability' }
    },
    {
      icon: Cpu,
      title: 'AI & Machine Learning Integration',
      description: 'Harness the power of AI to automate processes, gain insights, and create intelligent solutions that drive competitive advantage.',
      features: ['Custom AI Models', 'Data Analytics Platform', 'Process Automation', 'Predictive Analytics'],
      color: 'from-purple-500 to-pink-500',
      price: 'From $30,000',
      timeline: '16-24 weeks',
      metrics: { automation: '80% process automation', insights: '10x faster decisions', ROI: '350% return' }
    },
    {
      icon: Settings,
      title: 'Process Automation & Optimization',
      description: 'Streamline operations with intelligent automation, workflow optimization, and digital process transformation.',
      features: ['Workflow Automation', 'RPA Implementation', 'System Integration', 'Performance Monitoring'],
      color: 'from-emerald-500 to-teal-500',
      price: 'From $18,000',
      timeline: '8-16 weeks',
      metrics: { productivity: '+150% productivity', errors: '95% error reduction', time: '60% time savings' }
    },
    {
      icon: Globe,
      title: 'Digital Platform Development',
      description: 'Build comprehensive digital platforms that connect customers, partners, and systems in a unified ecosystem.',
      features: ['Platform Architecture', 'API Ecosystem', 'User Experience Design', 'Scalability Planning'],
      color: 'from-orange-500 to-red-500',
      price: 'From $35,000',
      timeline: '20-32 weeks',
      metrics: { engagement: '+300% user engagement', revenue: '+180% digital revenue', scale: '10x scalability' }
    }
  ]

  const stats = [
    { number: 500, label: 'Digital Transformations', suffix: '+', icon: Rocket, color: 'from-blue-500 to-indigo-500' },
    { number: 4.8, label: 'Client Satisfaction', suffix: '/5', icon: Star, color: 'from-yellow-500 to-orange-500' },
    { number: 250, label: 'Avg ROI Increase', suffix: '%', icon: TrendingUp, color: 'from-green-500 to-emerald-500' },
    { number: 99.9, label: 'Success Rate', suffix: '%', icon: Target, color: 'from-purple-500 to-pink-500' }
  ]

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen"
    >
      {/* Hero Section */}
      <section className="relative py-32 bg-gradient-to-b from-dark-950 via-dark-900 to-dark-950 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        </div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              className="inline-flex items-center bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full px-6 py-3 mb-8"
            >
              <div className="w-2 h-2 bg-blue-400 rounded-full mr-3 animate-pulse"></div>
              <Zap className="w-4 h-4 text-blue-400 mr-2" />
              <span className="text-blue-400 text-sm font-semibold uppercase tracking-wider">Enterprise Digital Transformation</span>
            </motion.div>
            
            <motion.h1 
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-5xl sm:text-6xl md:text-7xl font-black text-white mb-8 leading-none"
            >
              TRANSFORM
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                DIGITALLY
              </span>
            </motion.h1>
            
            <motion.p 
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-xl sm:text-2xl text-gray-300 max-w-4xl mx-auto mb-12 leading-relaxed"
            >
              Accelerate your business with comprehensive digital transformation. 
              Cloud migration, AI integration, and process automation for the future.
            </motion.p>
            
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex justify-center mb-16"
            >
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-12 py-4 rounded-2xl font-bold text-lg hover:shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 flex items-center justify-center space-x-3"
              >
                <span>Schedule Consultation</span>
                <ArrowRight className="w-5 h-5" />
              </motion.button>
            </motion.div>

            {/* Key Features */}
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
            >
              {[
                { icon: Cloud, text: '500+ Transformations', color: 'text-blue-400' },
                { icon: TrendingUp, text: '250% Avg ROI', color: 'text-purple-400' },
                { icon: Target, text: '99.9% Success Rate', color: 'text-pink-400' }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-center space-x-3 text-gray-300">
                  <item.icon className={item.color} size={20} />
                  <span className="font-medium">{item.text}</span>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-32 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div 
              className="inline-flex items-center bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Rocket className="text-blue-400 mr-3" size={16} />
              <span className="text-blue-400 text-sm font-semibold uppercase tracking-wider">Transformation Solutions</span>
            </motion.div>
            
            <h2 className="text-5xl md:text-6xl font-black text-white mb-8 leading-tight">
              DIGITAL TRANSFORMATION
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                SERVICES
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
              Modernize your business with cutting-edge technology solutions that drive growth and competitive advantage
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-12">
            {transformationServices.map((service, index) => {
              const Icon = service.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group"
                >
                  <div className="bg-gradient-to-br from-black/60 to-black/30 backdrop-blur-xl border border-white/20 rounded-3xl p-10 hover:border-white/30 transition-all duration-500 hover:transform hover:scale-[1.02] hover:-translate-y-2 relative overflow-hidden"
                    style={{ backdropFilter: 'blur(20px) saturate(180%)' }}
                  >
                    {/* Background Pattern */}
                    <div className="absolute inset-0 opacity-5 group-hover:opacity-15 transition-opacity duration-500">
                      <div className="absolute inset-0" style={{
                        backgroundImage: `radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.3) 1px, transparent 0)`,
                        backgroundSize: '24px 24px'
                      }}></div>
                    </div>

                    {/* Content */}
                    <div className="relative z-10">
                      {/* Icon and Pricing */}
                      <div className="flex items-start justify-between mb-8">
                        <div className={`w-20 h-20 bg-gradient-to-r ${service.color} rounded-3xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg`}>
                          <Icon className="w-10 h-10 text-white" />
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-white">{service.price}</div>
                          <div className="text-blue-400 text-sm font-medium">{service.timeline}</div>
                        </div>
                      </div>

                      {/* Title and Description */}
                      <h3 className="text-3xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors duration-300">
                        {service.title}
                      </h3>
                      <p className="text-gray-400 mb-8 leading-relaxed text-lg">
                        {service.description}
                      </p>

                      {/* Key Metrics */}
                      <div className="grid grid-cols-3 gap-4 mb-8">
                        {Object.entries(service.metrics).map(([key, value], idx) => (
                          <div key={idx} className="text-center bg-white/5 rounded-xl p-3 border border-white/10">
                            <div className="text-sm font-bold text-white">{value}</div>
                            <div className="text-gray-400 text-xs uppercase tracking-wider">{key}</div>
                          </div>
                        ))}
                      </div>

                      {/* Features */}
                      <div className="space-y-4 mb-8">
                        {service.features.map((feature, idx) => (
                          <motion.div
                            key={idx}
                            className="flex items-center space-x-3 group/feature"
                            initial={{ opacity: 0, x: -20 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.4, delay: idx * 0.1 }}
                            viewport={{ once: true }}
                          >
                            <div className={`w-3 h-3 bg-gradient-to-r ${service.color} rounded-full group-hover/feature:scale-125 transition-transform duration-300`}></div>
                            <span className="text-gray-300 group-hover/feature:text-white transition-colors duration-300 font-medium">{feature}</span>
                          </motion.div>
                        ))}
                      </div>

                      {/* CTA Button */}
                      <motion.button
                        whileHover={{ scale: 1.02, y: -2 }}
                        whileTap={{ scale: 0.98 }}
                        className={`w-full bg-gradient-to-r ${service.color} text-white font-bold py-4 rounded-2xl transition-all duration-300 relative overflow-hidden group/btn shadow-lg`}
                      >
                        <span className="relative z-10 flex items-center justify-center space-x-2">
                          <span>Get Started</span>
                          <ArrowRight size={16} />
                        </span>
                        {/* Button Shine Effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>
                      </motion.button>
                    </div>

                    {/* Hover Glow Effect */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl`}></div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>
    </motion.div>
  )
}

export default DigitalTransformation
