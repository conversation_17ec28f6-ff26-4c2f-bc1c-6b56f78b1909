import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useSearchParams } from 'react-router-dom'
import {
  GraduationCap,
  Upload,
  FileText,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  BookOpen,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  Award,
  Clock,
  Building,
  Shield,
  Save,
  Star
} from 'lucide-react'
import { internshipApplicationAPI } from '../lib/api'
import { emailService } from '../lib/emailService'

const InternshipApplication = () => {
  const [searchParams] = useSearchParams()
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',
    dateOfBirth: '',
    
    // Academic Information
    university: '',
    major: '',
    gpa: '',
    graduationDate: '',

    // Internship Details
    position: '',
    department: '',
    duration: '',
    startDate: '',

    // Experience & Skills
    previousInternships: '',
    relevantCourses: '',
    skills: [],
    projects: '',

    // Additional Information
    portfolioUrl: '',
    linkedinUrl: '',
    githubUrl: '',
    coverLetter: '',

    // Files
    resume: null,
    transcript: null,

    // Availability
    workDays: [],

    // References
    references: [
      { name: '', title: '', email: '', phone: '', relationship: '' },
      { name: '', title: '', email: '', phone: '', relationship: '' }
    ],

    // Legal
    backgroundCheck: false,
    agreement: false
  })

  const [currentStep, setCurrentStep] = useState(1)
  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isDraft, setIsDraft] = useState(false)
  const [uploadProgress, setUploadProgress] = useState({})
  const [skillInput, setSkillInput] = useState('')

  const steps = [
    { id: 1, title: 'Personal Info', icon: User, description: 'Basic personal details' },
    { id: 2, title: 'Academic Details', icon: BookOpen, description: 'Educational background' },
    { id: 3, title: 'Documents & Skills', icon: Upload, description: 'Files and skills' },
    { id: 4, title: 'Review & Submit', icon: CheckCircle, description: 'Final review' }
  ]

  // Function to map position to department
  const getDepartmentFromPosition = (position) => {
    const positionToDepartment = {
      'Frontend Development Intern': 'Engineering',
      'Backend Development Intern': 'Engineering',
      'Full Stack Development Intern': 'Engineering',
      'Mobile Development Intern': 'Engineering',
      'DevOps Intern': 'Engineering',
      'UI/UX Design Intern': 'Design',
      'Graphic Design Intern': 'Design',
      'Product Design Intern': 'Design',
      'Product Management Intern': 'Product',
      'Digital Marketing Intern': 'Marketing',
      'Content Marketing Intern': 'Marketing',
      'Social Media Intern': 'Marketing',
      'Business Development Intern': 'Business Development',
      'Sales Intern': 'Business Development',
      'Data Analytics Intern': 'Engineering',
      'QA Testing Intern': 'Engineering',
      'HR Intern': 'Human Resources',
      'Finance Intern': 'Finance',
      'Operations Intern': 'Operations'
    }
    return positionToDepartment[position] || 'Other'
  }

  // Pre-fill form data from URL parameters (position and department are non-changeable)
  useEffect(() => {
    const position = searchParams.get('position')
    const duration = searchParams.get('duration')

    if (position || duration) {
      const department = position ? getDepartmentFromPosition(position) : ''

      setFormData(prev => ({
        ...prev,
        position: position || prev.position,
        department: department || prev.department,
        duration: duration || prev.duration
      }))
    }
  }, [searchParams])

  const positions = [
    'Frontend Development Intern',
    'Backend Development Intern',
    'Full Stack Development Intern',
    'UI/UX Design Intern',
    'Product Management Intern',
    'Digital Marketing Intern',
    'Content Creation Intern',
    'Data Analytics Intern',
    'Business Development Intern',
    'HR Intern'
  ]

  const departments = [
    'Engineering',
    'Design',
    'Product',
    'Marketing',
    'Sales',
    'Human Resources',
    'Operations',
    'Finance'
  ]

  // Skills management functions
  const addSkill = (skill) => {
    if (skill.trim() && !formData.skills.includes(skill.trim())) {
      setFormData(prev => ({
        ...prev,
        skills: [...prev.skills, skill.trim()]
      }))
    }
  }

  const removeSkill = (skillToRemove) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.filter(skill => skill !== skillToRemove)
    }))
  }

  const handleSkillInputKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      if (skillInput.trim()) {
        addSkill(skillInput)
        setSkillInput('')
      }
    }
  }

  const workDaysOptions = [
    'Monday',
    'Tuesday', 
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday'
  ]

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const handleFileChange = (e) => {
    const { name, files } = e.target
    const file = files[0]

    if (file) {
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setErrors(prev => ({
          ...prev,
          [name]: 'File size must be less than 10MB'
        }))
        return
      }

      // Validate file type
      const allowedTypes = {
        resume: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        transcript: ['application/pdf', 'image/jpeg', 'image/png']
      }

      if (allowedTypes[name] && !allowedTypes[name].includes(file.type)) {
        setErrors(prev => ({
          ...prev,
          [name]: name === 'transcript'
            ? 'Invalid file type. Please upload PDF, JPG, or PNG files.'
            : 'Invalid file type. Please upload PDF, DOC, or DOCX files.'
        }))
        return
      }

      setFormData(prev => ({
        ...prev,
        [name]: file
      }))

      // Clear any previous errors
      if (errors[name]) {
        setErrors(prev => ({
          ...prev,
          [name]: ''
        }))
      }
    }
  }

  const handleWorkDaysChange = (day) => {
    setFormData(prev => ({
      ...prev,
      workDays: prev.workDays.includes(day)
        ? prev.workDays.filter(d => d !== day)
        : [...prev.workDays, day]
    }))
  }

  const handleReferenceChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      references: prev.references.map((ref, i) => 
        i === index ? { ...ref, [field]: value } : ref
      )
    }))
  }

  const validateStep = (step) => {
    const newErrors = {}

    switch (step) {
      case 1:
        // Personal Information Validation
        if (!formData.firstName?.trim()) newErrors.firstName = 'First name is required'
        if (!formData.lastName?.trim()) newErrors.lastName = 'Last name is required'
        if (!formData.email?.trim()) {
          newErrors.email = 'Email is required'
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
          newErrors.email = 'Please enter a valid email address'
        }
        if (!formData.phone?.trim()) {
          newErrors.phone = 'Phone number is required'
        } else if (!/^[\+]?[1-9][\d]{0,15}$/.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
          newErrors.phone = 'Please enter a valid phone number'
        }
        if (!formData.address?.trim()) newErrors.address = 'Address is required'
        if (!formData.city?.trim()) newErrors.city = 'City is required'
        if (!formData.country?.trim()) newErrors.country = 'Country is required'
        if (!formData.dateOfBirth?.trim()) newErrors.dateOfBirth = 'Date of birth is required'
        break

      case 2:
        // Academic Details Validation
        if (!formData.university?.trim()) newErrors.university = 'University/Institution is required'
        if (!formData.major?.trim()) newErrors.major = 'Major/Field of study is required'
        if (!formData.gpa?.trim()) newErrors.gpa = 'GPA is required'
        if (!formData.graduationDate?.trim()) newErrors.graduationDate = 'Expected graduation date is required'

        // Internship Details Validation
        if (!formData.position?.trim()) newErrors.position = 'Position is required'
        if (!formData.department?.trim()) newErrors.department = 'Department is required'
        break

      case 3:
        // Documents & Skills Validation
        if (!formData.resume) newErrors.resume = 'Resume is required'
        if (!formData.skills || formData.skills.length === 0) newErrors.skills = 'At least one skill is required'
        if (!formData.coverLetter?.trim()) newErrors.coverLetter = 'Cover letter is required'
        break

      case 4:
        // Final Validation
        if (!formData.backgroundCheck) newErrors.backgroundCheck = 'You must agree to background check'
        if (!formData.agreement) newErrors.agreement = 'You must agree to the terms and conditions'

        // Validate references if provided
        formData.references.forEach((ref, index) => {
          if (ref.name || ref.email || ref.title) { // If any field is filled, validate required fields
            if (!ref.name?.trim()) newErrors[`reference_${index}_name`] = `Reference ${index + 1} name is required`
            if (!ref.email?.trim()) newErrors[`reference_${index}_email`] = `Reference ${index + 1} email is required`
            if (!ref.title?.trim()) newErrors[`reference_${index}_title`] = `Reference ${index + 1} title is required`
            if (ref.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(ref.email)) {
              newErrors[`reference_${index}_email`] = `Reference ${index + 1} email is invalid`
            }
          }
        })
        break
    }

    console.log('Validation errors found:', newErrors)
    console.log('Validation passed:', Object.keys(newErrors).length === 0)

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 4))
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const saveAsDraft = async () => {
    setIsDraft(true)

    try {
      const { data, error } = await internshipApplicationAPI.saveDraft(formData)

      if (error) {
        throw new Error(error)
      }

      setFormData(prev => ({
        ...prev,
        applicationId: data.application_id
      }))

      setIsDraft(false)
      alert('Draft saved successfully!')

    } catch (error) {
      console.error('Error saving draft:', error)
      setIsDraft(false)
      alert('Error saving draft. Please try again.')
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!validateStep(currentStep)) return

    setIsSubmitting(true)

    try {
      // Prepare files for upload
      const files = {
        resume: formData.resume,
        transcript: formData.transcript
      }

      // Show upload progress for resume
      if (formData.resume) {
        setUploadProgress(prev => ({ ...prev, resume: 0 }))
        for (let i = 0; i <= 100; i += 20) {
          await new Promise(resolve => setTimeout(resolve, 200))
          setUploadProgress(prev => ({ ...prev, resume: i }))
        }
      }

      // Submit application
      const { data, error } = await internshipApplicationAPI.submit(formData, files)

      if (error) {
        throw new Error(error)
      }

      setFormData(prev => ({
        ...prev,
        applicationId: data.application_id,
        status: 'submitted',
        submittedAt: data.submitted_at
      }))

      // Send automated emails
      try {
        const applicantData = {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          position: formData.position,
          university: formData.university,
          major: formData.major,
          yearLevel: formData.yearLevel,
          gpa: formData.gpa,
          applicationId: data.application_id,
          resumeUrl: data.resume_url,
          transcriptUrl: data.transcript_url,
          portfolioUrl: formData.portfolioUrl,
          skills: formData.skills
        }

        // Send confirmation email to applicant and notification to admin
        const emailResults = await emailService.sendBatchEmails([
          () => emailService.sendInternshipApplicationConfirmation(applicantData),
          () => emailService.sendAdminInternshipApplicationNotification(applicantData)
        ])

        console.log('Email results:', emailResults)
      } catch (emailError) {
        console.error('Error sending emails:', emailError)
        // Don't fail the application submission if emails fail
      }

      setIsSubmitting(false)
      alert('Internship application submitted successfully! You will receive a confirmation email shortly.')

    } catch (error) {
      console.error('Error submitting application:', error)
      setIsSubmitting(false)
      alert(`Error submitting application: ${error.message}. Please try again.`)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6">Personal Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">First Name *</label>
                <input
                  type="text"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="Enter your first name"
                />
                {errors.firstName && <p className="text-red-400 text-sm mt-1">{errors.firstName}</p>}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Last Name *</label>
                <input
                  type="text"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="Enter your last name"
                />
                {errors.lastName && <p className="text-red-400 text-sm mt-1">{errors.lastName}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Email Address *</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="<EMAIL>"
                />
                {errors.email && <p className="text-red-400 text-sm mt-1">{errors.email}</p>}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Phone Number *</label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="+****************"
                />
                {errors.phone && <p className="text-red-400 text-sm mt-1">{errors.phone}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Date of Birth *</label>
              <input
                type="date"
                name="dateOfBirth"
                value={formData.dateOfBirth}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
              />
              {errors.dateOfBirth && <p className="text-red-400 text-sm mt-1">{errors.dateOfBirth}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Address</label>
              <input
                type="text"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                placeholder="Street address"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">City</label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="City"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">State</label>
                <input
                  type="text"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="State"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">ZIP Code</label>
                <input
                  type="text"
                  name="zipCode"
                  value={formData.zipCode}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="ZIP"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Country</label>
                <select
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                >
                  <option value="" className="bg-dark-900">Select Country</option>
                  <option value="India" className="bg-dark-900">India</option>
                  <option value="United States" className="bg-dark-900">United States</option>
                  <option value="Canada" className="bg-dark-900">Canada</option>
                  <option value="United Kingdom" className="bg-dark-900">United Kingdom</option>
                  <option value="Australia" className="bg-dark-900">Australia</option>
                  <option value="Germany" className="bg-dark-900">Germany</option>
                  <option value="France" className="bg-dark-900">France</option>
                  <option value="Netherlands" className="bg-dark-900">Netherlands</option>
                  <option value="Singapore" className="bg-dark-900">Singapore</option>
                  <option value="UAE" className="bg-dark-900">United Arab Emirates</option>
                  <option value="Other" className="bg-dark-900">Other</option>
                </select>
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6">Academic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">University/College *</label>
                <input
                  type="text"
                  name="university"
                  value={formData.university}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="Your university name"
                />
                {errors.university && <p className="text-red-400 text-sm mt-1">{errors.university}</p>}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Major/Field of Study *</label>
                <input
                  type="text"
                  name="major"
                  value={formData.major}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="Computer Science, Business, etc."
                />
                {errors.major && <p className="text-red-400 text-sm mt-1">{errors.major}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">GPA *</label>
                <input
                  type="number"
                  name="gpa"
                  value={formData.gpa}
                  onChange={handleInputChange}
                  step="0.01"
                  min="0"
                  max="4"
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="3.5"
                />
                {errors.gpa && <p className="text-red-400 text-sm mt-1">{errors.gpa}</p>}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Expected Graduation</label>
                <input
                  type="date"
                  name="graduationDate"
                  value={formData.graduationDate}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                />
              </div>
            </div>

            {/* Internship Details Section */}
            <div className="mt-8">
              <h4 className="text-xl font-semibold text-white mb-4">Internship Details</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Position *</label>
                  <input
                    type="text"
                    name="position"
                    value={formData.position}
                    readOnly
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-gray-300 cursor-not-allowed"
                    placeholder="Position will be auto-filled from application"
                  />
                  <p className="text-gray-500 text-xs mt-1">Position is automatically set based on the internship you applied for</p>
                  {errors.position && <p className="text-red-400 text-sm mt-1">{errors.position}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Department *</label>
                  <input
                    type="text"
                    name="department"
                    value={formData.department}
                    readOnly
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-gray-300 cursor-not-allowed"
                    placeholder="Department will be auto-filled based on position"
                  />
                  <p className="text-gray-500 text-xs mt-1">Department is automatically set based on the internship position</p>
                  {errors.department && <p className="text-red-400 text-sm mt-1">{errors.department}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Duration</label>
                  <select
                    name="duration"
                    value={formData.duration}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  >
                    <option value="" className="bg-dark-900">Select Duration</option>
                    <option value="3 months" className="bg-dark-900">3 months</option>
                    <option value="6 months" className="bg-dark-900">6 months</option>
                    <option value="12 months" className="bg-dark-900">12 months</option>
                    <option value="Other" className="bg-dark-900">Other</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Preferred Start Date</label>
                  <input
                    type="date"
                    name="startDate"
                    value={formData.startDate}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  />
                </div>

              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Relevant Coursework</label>
              <textarea
                name="relevantCourses"
                value={formData.relevantCourses}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300 resize-none"
                placeholder="List relevant courses you've taken..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Academic Projects</label>
              <textarea
                name="projects"
                value={formData.projects}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300 resize-none"
                placeholder="Describe any relevant academic or personal projects..."
              />
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6">Documents & Skills</h3>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Resume/CV *</label>
              <div className="border-2 border-dashed border-white/20 rounded-xl p-8 text-center hover:border-primary-400/50 transition-all duration-300">
                <Upload className="text-gray-400 mx-auto mb-4" size={48} />
                <p className="text-white mb-2">Upload your resume</p>
                <p className="text-gray-400 text-sm mb-4">PDF, DOC, or DOCX (Max 10MB)</p>
                <input
                  type="file"
                  name="resume"
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx"
                  className="hidden"
                  id="resume-upload"
                />
                <label
                  htmlFor="resume-upload"
                  className="bg-primary-500 text-white px-6 py-2 rounded-lg cursor-pointer hover:bg-primary-600 transition-colors duration-300"
                >
                  Choose File
                </label>
                {formData.resume && (
                  <p className="text-green-400 text-sm mt-2">✓ {formData.resume.name}</p>
                )}
                {errors.resume && <p className="text-red-400 text-sm mt-2">{errors.resume}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Skills *</label>
              <div className="space-y-3">
                {/* Skills Tags Display */}
                {formData.skills.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.skills.map((skill, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-500/20 text-primary-300 border border-primary-500/30"
                      >
                        {skill}
                        <button
                          type="button"
                          onClick={() => removeSkill(skill)}
                          className="ml-2 text-primary-400 hover:text-red-400 transition-colors duration-200"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                )}

                {/* Skills Input */}
                <input
                  type="text"
                  value={skillInput}
                  onChange={(e) => setSkillInput(e.target.value)}
                  onKeyDown={handleSkillInputKeyDown}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="Type a skill and press Enter or comma to add (e.g., JavaScript, React, Python)"
                />
                <p className="text-gray-500 text-xs">Press Enter or comma to add each skill as a tag</p>
              </div>
              {errors.skills && <p className="text-red-400 text-sm mt-1">{errors.skills}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Cover Letter *</label>
              <textarea
                name="coverLetter"
                value={formData.coverLetter}
                onChange={handleInputChange}
                rows={6}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300 resize-none"
                placeholder="Tell us why you're interested in this internship and what you hope to learn..."
              />
              {errors.coverLetter && <p className="text-red-400 text-sm mt-1">{errors.coverLetter}</p>}
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6">Review & Submit</h3>

            <div className="bg-white/5 rounded-xl p-6 space-y-4">
              <h4 className="text-lg font-semibold text-white mb-4">Application Summary</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Name:</span>
                  <span className="text-white ml-2">{formData.firstName} {formData.lastName}</span>
                </div>
                <div>
                  <span className="text-gray-400">Email:</span>
                  <span className="text-white ml-2">{formData.email}</span>
                </div>
                <div>
                  <span className="text-gray-400">Position:</span>
                  <span className="text-white ml-2">{formData.position}</span>
                </div>
                <div>
                  <span className="text-gray-400">Department:</span>
                  <span className="text-white ml-2">{formData.department}</span>
                </div>
                <div>
                  <span className="text-gray-400">University:</span>
                  <span className="text-white ml-2">{formData.university}</span>
                </div>
                <div>
                  <span className="text-gray-400">Major:</span>
                  <span className="text-white ml-2">{formData.major}</span>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              {/* Checkboxes */}
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="backgroundCheck"
                    checked={formData.backgroundCheck}
                    onChange={handleInputChange}
                    className="mr-3 h-4 w-4 text-primary-500 focus:ring-primary-400 border-gray-300 rounded"
                  />
                  <label className="text-sm text-gray-300">
                    I consent to a background check if required
                  </label>
                </div>
                {errors.backgroundCheck && <p className="text-red-400 text-sm mt-1">{errors.backgroundCheck}</p>}

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="agreement"
                    checked={formData.agreement}
                    onChange={handleInputChange}
                    className="mr-3 h-4 w-4 text-primary-500 focus:ring-primary-400 border-gray-300 rounded"
                  />
                  <label className="text-sm text-gray-300">
                    I agree to the terms and conditions *
                  </label>
                </div>
                {errors.agreement && <p className="text-red-400 text-sm mt-1">{errors.agreement}</p>}
              </div>
            </div>
          </div>
        )

      default:
        return <div>Step content not found</div>
    }
  }

  return (
    <div className="min-h-screen bg-dark-950 -mt-16 lg:-mt-20 pt-32">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.div
            className="inline-flex items-center bg-green-500/10 border border-green-500/20 rounded-full px-4 py-2 mb-6"
            whileHover={{ scale: 1.05 }}
          >
            <GraduationCap className="text-green-400 mr-2" size={16} />
            <span className="text-green-400 text-sm font-medium uppercase tracking-wider">Internship Application</span>
          </motion.div>
          <h1 className="text-4xl md:text-5xl font-black text-white mb-4 leading-tight">
            Start Your
            <br />
            <span className="bg-gradient-to-r from-green-400 via-emerald-400 to-teal-400 bg-clip-text text-transparent">
              Career Journey
            </span>
          </h1>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            Apply for an internship at Delta Xero Creations and gain valuable experience in a dynamic, innovative environment.
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-12">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const Icon = step.icon
              const isActive = currentStep === step.id
              const isCompleted = currentStep > step.id
              
              return (
                <div key={step.id} className="flex items-center">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center border-2 transition-all duration-300 ${
                    isCompleted 
                      ? 'bg-green-500 border-green-500' 
                      : isActive 
                        ? 'bg-primary-500 border-primary-500' 
                        : 'bg-white/5 border-white/20'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="text-white" size={20} />
                    ) : (
                      <Icon className={isActive ? 'text-white' : 'text-gray-400'} size={20} />
                    )}
                  </div>
                  <div className="ml-3 hidden md:block">
                    <p className={`text-sm font-medium ${isActive ? 'text-white' : 'text-gray-400'}`}>
                      Step {step.id}
                    </p>
                    <p className={`text-xs ${isActive ? 'text-green-400' : 'text-gray-500'}`}>
                      {step.title}
                    </p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-8 h-0.5 mx-4 ${isCompleted ? 'bg-green-500' : 'bg-white/20'}`}></div>
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Form */}
        <div className="bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 rounded-3xl p-8 md:p-12">
          <form onSubmit={handleSubmit}>
            {renderStepContent()}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-12">
              <button
                type="button"
                onClick={prevStep}
                disabled={currentStep === 1}
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                  currentStep === 1
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : 'bg-white/10 border border-white/20 text-white hover:bg-white/20'
                }`}
              >
                Previous
              </button>

              {currentStep < 4 ? (
                <button
                  type="button"
                  onClick={nextStep}
                  className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-8 py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300 flex items-center space-x-2"
                >
                  <span>Next Step</span>
                  <ArrowRight size={16} />
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-8 py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300 flex items-center space-x-2 disabled:opacity-50"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Submitting...</span>
                    </>
                  ) : (
                    <>
                      <span>Submit Application</span>
                      <CheckCircle size={16} />
                    </>
                  )}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default InternshipApplication
