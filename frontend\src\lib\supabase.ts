import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// Database types for TypeScript
export interface Database {
  public: {
    Tables: {
      job_applications: {
        Row: JobApplication
        Insert: Omit<JobApplication, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<JobApplication, 'id' | 'created_at'>>
      }
      internship_applications: {
        Row: InternshipApplication
        Insert: Omit<InternshipApplication, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<InternshipApplication, 'id' | 'created_at'>>
      }
      application_references: {
        Row: ApplicationReference
        Insert: Omit<ApplicationReference, 'id' | 'created_at'>
        Update: Partial<Omit<ApplicationReference, 'id' | 'created_at'>>
      }
      employees: {
        Row: Employee
        Insert: Omit<Employee, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Employee, 'id' | 'created_at'>>
      }
      interns: {
        Row: Intern
        Insert: Omit<Intern, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Intern, 'id' | 'created_at'>>
      }
      users: {
        Row: User
        Insert: Omit<User, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<User, 'id' | 'created_at'>>
      }
    }
  }
}

// Type definitions
export type ApplicationStatus = 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected' | 'withdrawn'
export type EmploymentType = 'full-time' | 'part-time' | 'contract' | 'freelance'
export type InternshipType = 'paid' | 'unpaid' | 'academic_credit'
export type WorkLocation = 'remote' | 'on-site' | 'hybrid'
export type UserRole = 'admin' | 'hr' | 'manager' | 'employee' | 'intern'

export interface JobApplication {
  id: string
  application_id: string
  
  // Personal Information
  first_name: string
  last_name: string
  email: string
  phone: string
  address?: string
  city?: string
  state?: string
  zip_code?: string
  country?: string
  date_of_birth?: string
  
  // Position Information
  position: string
  department: string
  expected_salary?: string
  available_start_date?: string
  employment_type?: EmploymentType
  work_location?: WorkLocation
  
  // Experience
  experience_level?: string
  current_company?: string
  current_position?: string
  notice_period?: string
  total_experience?: string
  relevant_experience?: string
  
  // Education
  education_level?: string
  university?: string
  graduation_year?: string
  gpa?: string
  degree?: string
  field_of_study?: string
  
  // Skills & Certifications
  technical_skills?: string
  soft_skills?: string
  certifications?: string
  languages?: string
  
  // Additional Information
  portfolio_url?: string
  linkedin_url?: string
  github_url?: string
  website_url?: string
  cover_letter?: string
  
  // Files
  resume_url?: string
  portfolio_file_url?: string
  cover_letter_file_url?: string
  
  // Legal & Compliance
  work_authorization?: string
  background_check_consent?: boolean
  drug_test_consent?: boolean
  visa_sponsorship_required?: boolean
  
  // Application Metadata
  status?: ApplicationStatus
  submitted_at?: string
  reviewed_at?: string
  reviewed_by?: string
  notes?: string
  rating?: number
  
  // Timestamps
  created_at: string
  updated_at: string
}

export interface InternshipApplication {
  id: string
  application_id: string
  
  // Personal Information
  first_name: string
  last_name: string
  email: string
  phone: string
  address?: string
  city?: string
  state?: string
  zip_code?: string
  country?: string
  date_of_birth?: string
  
  // Academic Information
  university: string
  major: string
  year_level: string
  gpa?: string
  graduation_date?: string
  
  // Internship Details
  position: string
  department: string
  duration?: string
  start_date?: string
  internship_type?: InternshipType
  hours_per_week?: number
  work_days?: string[]
  
  // Experience & Skills
  previous_internships?: string
  relevant_courses?: string
  skills?: string
  projects?: string
  
  // Additional Information
  portfolio_url?: string
  linkedin_url?: string
  github_url?: string
  cover_letter?: string
  
  // Files
  resume_url?: string
  transcript_url?: string
  
  // Legal
  work_authorization?: string
  background_check_consent?: boolean
  agreement_consent?: boolean
  
  // Application Metadata
  status?: ApplicationStatus
  submitted_at?: string
  reviewed_at?: string
  reviewed_by?: string
  notes?: string
  rating?: number
  
  // Timestamps
  created_at: string
  updated_at: string
}

export interface ApplicationReference {
  id: string
  job_application_id?: string
  internship_application_id?: string
  name: string
  title?: string
  company?: string
  email: string
  phone?: string
  relationship?: string
  created_at: string
}

export interface Employee {
  id: string
  user_id?: string
  employee_id: string
  first_name: string
  last_name: string
  email: string
  phone?: string
  address?: string
  city?: string
  state?: string
  zip_code?: string
  country?: string
  date_of_birth?: string
  position: string
  department: string
  employment_type?: EmploymentType
  work_location?: WorkLocation
  salary?: number
  start_date: string
  end_date?: string
  manager_id?: string
  is_active?: boolean
  created_at: string
  updated_at: string
}

export interface Intern {
  id: string
  user_id?: string
  intern_id: string
  first_name: string
  last_name: string
  email: string
  phone?: string
  university: string
  major: string
  year_level: string
  position: string
  department: string
  internship_type?: InternshipType
  start_date: string
  end_date: string
  hours_per_week?: number
  supervisor_id?: string
  is_active?: boolean
  created_at: string
  updated_at: string
}

export interface User {
  id: string
  email: string
  password_hash?: string
  first_name: string
  last_name: string
  role?: UserRole
  is_active?: boolean
  created_at: string
  updated_at: string
  last_login?: string
}

// Storage bucket names
export const STORAGE_BUCKETS = {
  RESUMES: 'resumes',
  PORTFOLIOS: 'portfolios',
  TRANSCRIPTS: 'transcripts',
  COVER_LETTERS: 'cover-letters'
} as const
