import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import AdminNavigation from '../../components/AdminNavigation'
import {
  MessageSquare,
  Search,
  Filter,
  Eye,
  Mail,
  Phone,
  Calendar,
  Clock,
  Star,
  Check,
  X,
  Edit,
  Trash2,
  ArrowLeft,
  Send,
  DollarSign,
  User,
  Building
} from 'lucide-react'
import { quoteRequestAPI } from '../../lib/api'

const QuoteManagement = () => {
  const [quoteRequests, setQuoteRequests] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [selectedQuote, setSelectedQuote] = useState(null)
  const [showDetailModal, setShowDetailModal] = useState(false)

  // Fetch quote requests on component mount
  useEffect(() => {
    fetchQuoteRequests()
  }, [])

  const fetchQuoteRequests = async () => {
    try {
      setLoading(true)
      const { data, error } = await quoteRequestAPI.getAll()
      
      if (error) {
        throw new Error(error)
      }

      setQuoteRequests(data || [])
    } catch (err) {
      console.error('Error fetching quote requests:', err)
      // Fallback to empty array
      setQuoteRequests([])
    } finally {
      setLoading(false)
    }
  }

  // Filter quote requests based on search and status
  const filteredQuotes = quoteRequests.filter(quote => {
    const matchesSearch = quote.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quote.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quote.message.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = filterStatus === 'all' || quote.status === filterStatus
    
    return matchesSearch && matchesStatus
  })

  // Handle status change
  const handleStatusChange = async (quoteId, newStatus) => {
    try {
      const { error } = await quoteRequestAPI.updateStatus(quoteId, newStatus)
      
      if (error) {
        throw new Error(error)
      }

      // Update local state
      setQuoteRequests(prev => prev.map(quote => 
        quote.id === quoteId ? { ...quote, status: newStatus } : quote
      ))

      // Update selected quote if it's the one being changed
      if (selectedQuote && selectedQuote.id === quoteId) {
        setSelectedQuote(prev => ({ ...prev, status: newStatus }))
      }

    } catch (err) {
      console.error('Error updating quote status:', err)
      alert('Error updating status. Please try again.')
    }
  }

  // Handle delete quote
  const handleDeleteQuote = async (quoteId) => {
    if (!confirm('Are you sure you want to delete this quote request? This action cannot be undone.')) {
      return
    }

    try {
      const { error } = await quoteRequestAPI.delete(quoteId)
      
      if (error) {
        throw new Error(error)
      }

      // Remove from local state
      setQuoteRequests(prev => prev.filter(quote => quote.id !== quoteId))
      
      // Close modal if this quote was selected
      if (selectedQuote && selectedQuote.id === quoteId) {
        setShowDetailModal(false)
      }

    } catch (err) {
      console.error('Error deleting quote:', err)
      alert('Error deleting quote. Please try again.')
    }
  }

  // Handle send email
  const handleSendEmail = (quote) => {
    const subject = `Quote for your project inquiry`
    const body = `Dear ${quote.name},\n\nThank you for your interest in our services. We have reviewed your project requirements and will provide you with a detailed quote shortly.\n\nBest regards,\nDelta Xero Creations Team`
    const mailtoUrl = `mailto:${quote.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
    window.open(mailtoUrl, '_blank')
  }

  // Format time ago
  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    
    return date.toLocaleDateString()
  }

  // Get status color
  const getStatusColor = (status) => {
    const colors = {
      'new': 'text-green-400 bg-green-500/10 border-green-500/20',
      'reviewing': 'text-blue-400 bg-blue-500/10 border-blue-500/20',
      'quoted': 'text-purple-400 bg-purple-500/10 border-purple-500/20',
      'accepted': 'text-emerald-400 bg-emerald-500/10 border-emerald-500/20',
      'rejected': 'text-red-400 bg-red-500/10 border-red-500/20',
      'completed': 'text-gray-400 bg-gray-500/10 border-gray-500/20'
    }
    return colors[status] || 'text-gray-400 bg-gray-500/10 border-gray-500/20'
  }

  return (
    <div className="min-h-screen bg-dark-950">
      <AdminNavigation />
      <div className="pt-20 pb-20 md:pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-black text-white mb-2">Quote Management</h1>
              <p className="text-gray-400">Manage customer quote requests and project inquiries</p>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[
              { title: 'Total Requests', value: quoteRequests.length, color: 'from-blue-500 to-cyan-500' },
              { title: 'New Requests', value: quoteRequests.filter(q => q.status === 'new').length, color: 'from-green-500 to-emerald-500' },
              { title: 'In Review', value: quoteRequests.filter(q => q.status === 'reviewing').length, color: 'from-yellow-500 to-orange-500' },
              { title: 'Quoted', value: quoteRequests.filter(q => q.status === 'quoted').length, color: 'from-purple-500 to-pink-500' }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-dark-800/50 border border-gray-700/50 rounded-2xl p-6"
              >
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${stat.color} flex items-center justify-center mb-4`}>
                  <MessageSquare className="text-white" size={24} />
                </div>
                <h3 className="text-2xl font-bold text-white mb-1">{stat.value}</h3>
                <p className="text-gray-400 text-sm">{stat.title}</p>
              </motion.div>
            ))}
          </div>

          {/* Filters */}
          <div className="bg-dark-800/50 border border-gray-700/50 rounded-2xl p-6 mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search by name, email, or message..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-dark-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none"
                  />
                </div>
              </div>
              <div className="flex gap-4">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-4 py-3 bg-dark-700 border border-gray-600 rounded-xl text-white focus:border-primary-400 focus:outline-none"
                >
                  <option value="all">All Status</option>
                  <option value="new">New</option>
                  <option value="reviewing">Reviewing</option>
                  <option value="quoted">Quoted</option>
                  <option value="accepted">Accepted</option>
                  <option value="rejected">Rejected</option>
                  <option value="completed">Completed</option>
                </select>
              </div>
            </div>
          </div>

          {/* Quote Requests List */}
          <div className="bg-dark-800/50 border border-gray-700/50 rounded-2xl overflow-hidden">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
              </div>
            ) : filteredQuotes.length > 0 ? (
              <div className="divide-y divide-gray-700/50">
                {filteredQuotes.map((quote) => (
                  <motion.div
                    key={quote.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="p-6 hover:bg-dark-700/30 transition-all duration-300"
                  >
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-white">{quote.name}</h3>
                          <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(quote.status)}`}>
                            {quote.status}
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-400 mb-3">
                          <div className="flex items-center space-x-1">
                            <Mail size={14} />
                            <span>{quote.email}</span>
                          </div>
                          {quote.company && (
                            <div className="flex items-center space-x-1">
                              <Building size={14} />
                              <span>{quote.company}</span>
                            </div>
                          )}
                          <div className="flex items-center space-x-1">
                            <Calendar size={14} />
                            <span>{formatTimeAgo(quote.created_at)}</span>
                          </div>
                        </div>
                        <p className="text-gray-300 text-sm mb-3 line-clamp-2">{quote.message}</p>
                        <div className="flex flex-wrap gap-2">
                          {quote.services?.map((service, index) => (
                            <span key={index} className="bg-primary-500/20 text-primary-400 px-2 py-1 rounded text-xs">
                              {service}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex justify-between items-center">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleStatusChange(quote.id, 'reviewing')}
                          className="bg-blue-500/20 border border-blue-500/30 rounded-lg px-3 py-1 text-blue-400 hover:bg-blue-500/30 transition-all duration-300 text-sm"
                        >
                          Mark Reviewing
                        </button>
                        <button
                          onClick={() => handleStatusChange(quote.id, 'quoted')}
                          className="bg-purple-500/20 border border-purple-500/30 rounded-lg px-3 py-1 text-purple-400 hover:bg-purple-500/30 transition-all duration-300 text-sm"
                        >
                          Mark Quoted
                        </button>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleSendEmail(quote)}
                          className="bg-gray-600/20 border border-gray-600/30 rounded-lg px-3 py-1 text-gray-300 hover:bg-gray-600/30 transition-all duration-300 text-sm"
                        >
                          <Mail size={14} />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedQuote(quote)
                            setShowDetailModal(true)
                          }}
                          className="bg-green-500/20 border border-green-500/30 rounded-lg px-3 py-1 text-green-400 hover:bg-green-500/30 transition-all duration-300 text-sm"
                        >
                          <Eye size={14} />
                        </button>
                        <button
                          onClick={() => handleDeleteQuote(quote.id)}
                          className="bg-red-600/20 border border-red-600/30 rounded-lg px-3 py-1 text-red-300 hover:bg-red-600/30 transition-all duration-300 text-sm"
                        >
                          <Trash2 size={14} />
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <MessageSquare size={48} className="mx-auto mb-4 text-gray-600" />
                <h3 className="text-lg font-medium text-gray-400 mb-2">No quote requests found</h3>
                <p className="text-gray-500">Quote requests will appear here when customers submit them.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default QuoteManagement
