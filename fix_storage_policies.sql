-- Fix Storage Policies for File Uploads
-- Run this in your Supabase SQL Editor

-- Drop existing storage policies that might be blocking uploads
DROP POLICY IF EXISTS "Anyone can upload resumes" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can upload portfolios" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can upload transcripts" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can upload cover letters" ON storage.objects;

-- Create new storage policies that allow anonymous uploads

-- Allow anyone to upload files to resumes bucket
CREATE POLICY "Enable upload for anonymous users - resumes" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'resumes');

-- Allow anyone to upload files to portfolios bucket
CREATE POLICY "Enable upload for anonymous users - portfolios" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'portfolios');

-- Allow anyone to upload files to transcripts bucket
CREATE POLICY "Enable upload for anonymous users - transcripts" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'transcripts');

-- Allow anyone to upload files to cover-letters bucket
CREATE POLICY "Enable upload for anonymous users - cover-letters" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'cover-letters');

-- Allow anyone to read uploaded files (for admins to download)
CREATE POLICY "Enable read for all users - resumes" ON storage.objects
    FOR SELECT USING (bucket_id = 'resumes');

CREATE POLICY "Enable read for all users - portfolios" ON storage.objects
    FOR SELECT USING (bucket_id = 'portfolios');

CREATE POLICY "Enable read for all users - transcripts" ON storage.objects
    FOR SELECT USING (bucket_id = 'transcripts');

CREATE POLICY "Enable read for all users - cover-letters" ON storage.objects
    FOR SELECT USING (bucket_id = 'cover-letters');

-- Alternative: Temporarily disable RLS on storage.objects for testing
-- ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;

-- Check current storage policies
SELECT policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE schemaname = 'storage' AND tablename = 'objects'
ORDER BY policyname;
