import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useSearchParams } from 'react-router-dom'
import {
  Briefcase,
  Upload,
  FileText,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  DollarSign,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  Download,
  Building,
  Clock,
  Shield,
  Save
} from 'lucide-react'
import { jobApplicationAPI } from '../lib/api'
import { emailService } from '../lib/emailService'

const JobApplication = () => {
  const [searchParams] = useSearchParams()
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',

    // Position Information
    position: '',
    department: '',
    availableStartDate: '',
    employmentType: 'full-time',
    workLocation: 'remote',

    // Experience
    experience: '',
    currentCompany: '',
    currentPosition: '',
    noticePeriod: '',
    totalExperience: '',
    relevantExperience: '',

    // Education
    education: '',
    university: '',
    graduationYear: '',
    gpa: '',
    degree: '',
    fieldOfStudy: '',

    // Skills & Certifications
    technicalSkills: '',
    softSkills: '',
    certifications: '',
    languages: '',

    // Additional Information
    portfolioUrl: '',
    linkedinUrl: '',
    githubUrl: '',
    websiteUrl: '',
    coverLetter: '',

    // Files
    resume: null,
    portfolio: null,
    coverLetterFile: null,

    // Legal & Compliance
    workAuthorization: '',
    backgroundCheck: false,
    drugTest: false,
    visaSponsorship: false,

    // References
    references: [
      { name: '', company: '', position: '', email: '', phone: '', relationship: '' },
      { name: '', company: '', position: '', email: '', phone: '', relationship: '' }
    ],

    // Application Metadata
    applicationId: null,
    submittedAt: null,
    status: 'draft'
  })

  const [currentStep, setCurrentStep] = useState(1)
  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isDraft, setIsDraft] = useState(false)
  const [uploadProgress, setUploadProgress] = useState({})

  const steps = [
    { id: 1, title: 'Personal Info', icon: User, description: 'Basic personal details' },
    { id: 2, title: 'Experience & Education', icon: FileText, description: 'Background info' },
    { id: 3, title: 'Skills & Documents', icon: Upload, description: 'Skills and files' },
    { id: 4, title: 'Review & Submit', icon: CheckCircle, description: 'Final review' }
  ]

  const positions = [
    'Frontend Developer',
    'Backend Developer',
    'Full Stack Developer',
    'UI/UX Designer',
    'Product Manager',
    'DevOps Engineer',
    'Data Scientist',
    'Mobile Developer',
    'QA Engineer',
    'Technical Lead',
    'Project Manager',
    'Business Analyst'
  ]

  const departments = [
    'Engineering',
    'Design',
    'Product',
    'Marketing',
    'Sales',
    'Human Resources',
    'Operations',
    'Finance',
    'Customer Success',
    'Data & Analytics'
  ]

  const experienceLevels = [
    'Entry Level (0-1 years)',
    'Junior (1-3 years)',
    'Mid-Level (3-5 years)',
    'Senior (5-8 years)',
    'Lead (8-12 years)',
    'Principal (12+ years)'
  ]

  // Pre-fill form data from URL parameters
  useEffect(() => {
    const position = searchParams.get('position')
    const department = searchParams.get('department')
    const employmentType = searchParams.get('type')
    const workLocation = searchParams.get('location')

    if (position || department || employmentType || workLocation) {
      setFormData(prev => ({
        ...prev,
        position: position || prev.position,
        department: department || prev.department,
        employmentType: employmentType ? employmentType.toLowerCase() : prev.employmentType,
        workLocation: workLocation ? workLocation.toLowerCase() : prev.workLocation
      }))
    }
  }, [searchParams])

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const handleFileChange = (e) => {
    const { name, files } = e.target
    const file = files[0]

    if (file) {
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setErrors(prev => ({
          ...prev,
          [name]: 'File size must be less than 10MB'
        }))
        return
      }

      // Validate file type
      const allowedTypes = {
        resume: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        portfolio: ['application/pdf', 'application/zip'],
        coverLetterFile: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
      }

      if (allowedTypes[name] && !allowedTypes[name].includes(file.type)) {
        setErrors(prev => ({
          ...prev,
          [name]: 'Invalid file type. Please upload PDF, DOC, or DOCX files.'
        }))
        return
      }

      setFormData(prev => ({
        ...prev,
        [name]: file
      }))

      // Clear any previous errors
      if (errors[name]) {
        setErrors(prev => ({
          ...prev,
          [name]: ''
        }))
      }
    }
  }

  const handleReferenceChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      references: prev.references.map((ref, i) =>
        i === index ? { ...ref, [field]: value } : ref
      )
    }))
  }

  const validateStep = (step) => {
    const newErrors = {}

    switch (step) {
      case 1:
        // Personal Information Validation
        if (!formData.firstName?.trim()) newErrors.firstName = 'First name is required'
        if (!formData.lastName?.trim()) newErrors.lastName = 'Last name is required'
        if (!formData.email?.trim()) {
          newErrors.email = 'Email is required'
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
          newErrors.email = 'Please enter a valid email address'
        }
        if (!formData.phone?.trim()) {
          newErrors.phone = 'Phone number is required'
        } else if (!/^[\+]?[1-9][\d]{0,15}$/.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
          newErrors.phone = 'Please enter a valid phone number'
        }
        if (!formData.address?.trim()) newErrors.address = 'Address is required'
        if (!formData.city?.trim()) newErrors.city = 'City is required'
        if (!formData.country?.trim()) newErrors.country = 'Country is required'
        break

      case 2:
        // Experience & Education Validation
        if (!formData.experience?.trim()) newErrors.experience = 'Experience level is required'
        if (!formData.education?.trim()) newErrors.education = 'Education level is required'
        if (!formData.availableStartDate?.trim()) newErrors.availableStartDate = 'Available start date is required'
        if (!formData.workAuthorization?.trim()) newErrors.workAuthorization = 'Work authorization status is required'
        break

      case 3:
        // Skills & Documents Validation
        if (!formData.resume) newErrors.resume = 'Resume is required'
        if (!formData.technicalSkills?.trim()) newErrors.technicalSkills = 'Technical skills are required'
        if (!formData.coverLetter?.trim()) newErrors.coverLetter = 'Cover letter is required'
        break

      case 4:
        // Final Validation
        if (!formData.backgroundCheck) newErrors.backgroundCheck = 'You must agree to background check'
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 4))
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const saveAsDraft = async () => {
    setIsDraft(true)

    try {
      const { data, error } = await jobApplicationAPI.saveDraft(formData)

      if (error) {
        throw new Error(error)
      }

      setFormData(prev => ({
        ...prev,
        applicationId: data.application_id
      }))

      setIsDraft(false)
      alert('Draft saved successfully!')

    } catch (error) {
      console.error('Error saving draft:', error)
      setIsDraft(false)
      alert('Error saving draft. Please try again.')
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!validateStep(currentStep)) return

    setIsSubmitting(true)

    try {
      // Prepare files for upload
      const files = {
        resume: formData.resume,
        portfolio: formData.portfolio,
        coverLetterFile: formData.coverLetterFile
      }

      // Show upload progress for resume
      if (formData.resume) {
        setUploadProgress(prev => ({ ...prev, resume: 0 }))
        for (let i = 0; i <= 100; i += 20) {
          await new Promise(resolve => setTimeout(resolve, 200))
          setUploadProgress(prev => ({ ...prev, resume: i }))
        }
      }

      // Submit application
      const { data, error } = await jobApplicationAPI.submit(formData, files)

      if (error) {
        throw new Error(error)
      }

      setFormData(prev => ({
        ...prev,
        applicationId: data.application_id,
        status: 'submitted',
        submittedAt: data.submitted_at
      }))

      // Send automated emails
      try {
        const applicantData = {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          position: formData.position,
          department: formData.department,
          experience: formData.experience,
          applicationId: data.application_id,
          resumeUrl: data.resume_url,
          coverLetter: formData.coverLetter,
          portfolioUrl: formData.portfolioUrl
        }

        // Send confirmation email to applicant and notification to admin
        const emailResults = await emailService.sendBatchEmails([
          () => emailService.sendJobApplicationConfirmation(applicantData),
          () => emailService.sendAdminJobApplicationNotification(applicantData)
        ])

        console.log('Email results:', emailResults)
      } catch (emailError) {
        console.error('Error sending emails:', emailError)
        // Don't fail the application submission if emails fail
      }

      setIsSubmitting(false)
      alert('Application submitted successfully! You will receive a confirmation email shortly.')

    } catch (error) {
      console.error('Error submitting application:', error)
      setIsSubmitting(false)
      alert(`Error submitting application: ${error.message}. Please try again.`)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6">Personal Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">First Name *</label>
                <input
                  type="text"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="Enter your first name"
                />
                {errors.firstName && <p className="text-red-400 text-sm mt-1">{errors.firstName}</p>}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Last Name *</label>
                <input
                  type="text"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="Enter your last name"
                />
                {errors.lastName && <p className="text-red-400 text-sm mt-1">{errors.lastName}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Email Address *</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="<EMAIL>"
                />
                {errors.email && <p className="text-red-400 text-sm mt-1">{errors.email}</p>}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Phone Number *</label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="+****************"
                />
                {errors.phone && <p className="text-red-400 text-sm mt-1">{errors.phone}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Address</label>
              <input
                type="text"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                placeholder="Street address"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">City</label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="City"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">State</label>
                <input
                  type="text"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="State"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">ZIP Code</label>
                <input
                  type="text"
                  name="zipCode"
                  value={formData.zipCode}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="ZIP"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Country</label>
                <select
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                >
                  <option value="" className="bg-dark-900">Select Country</option>
                  <option value="India" className="bg-dark-900">India</option>
                  <option value="United States" className="bg-dark-900">United States</option>
                  <option value="Canada" className="bg-dark-900">Canada</option>
                  <option value="United Kingdom" className="bg-dark-900">United Kingdom</option>
                  <option value="Australia" className="bg-dark-900">Australia</option>
                  <option value="Germany" className="bg-dark-900">Germany</option>
                  <option value="France" className="bg-dark-900">France</option>
                  <option value="Netherlands" className="bg-dark-900">Netherlands</option>
                  <option value="Singapore" className="bg-dark-900">Singapore</option>
                  <option value="UAE" className="bg-dark-900">United Arab Emirates</option>
                  <option value="Other" className="bg-dark-900">Other</option>
                </select>
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6">Experience & Education</h3>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Years of Experience *</label>
              <select
                name="experience"
                value={formData.experience}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
              >
                <option value="" className="bg-dark-900">Select Experience</option>
                <option value="0-1" className="bg-dark-900">0-1 years</option>
                <option value="1-3" className="bg-dark-900">1-3 years</option>
                <option value="3-5" className="bg-dark-900">3-5 years</option>
                <option value="5-10" className="bg-dark-900">5-10 years</option>
                <option value="10+" className="bg-dark-900">10+ years</option>
              </select>
              {errors.experience && <p className="text-red-400 text-sm mt-1">{errors.experience}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Current Company</label>
                <input
                  type="text"
                  name="currentCompany"
                  value={formData.currentCompany}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="Current company name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Current Position</label>
                <input
                  type="text"
                  name="currentPosition"
                  value={formData.currentPosition}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="Your current role"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Notice Period</label>
              <select
                name="noticePeriod"
                value={formData.noticePeriod}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
              >
                <option value="" className="bg-dark-900">Select Notice Period</option>
                <option value="immediate" className="bg-dark-900">Immediate</option>
                <option value="2-weeks" className="bg-dark-900">2 weeks</option>
                <option value="1-month" className="bg-dark-900">1 month</option>
                <option value="2-months" className="bg-dark-900">2 months</option>
                <option value="3-months" className="bg-dark-900">3 months</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Highest Education *</label>
              <select
                name="education"
                value={formData.education}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
              >
                <option value="" className="bg-dark-900">Select Education</option>
                <option value="high-school" className="bg-dark-900">High School</option>
                <option value="associate" className="bg-dark-900">Associate Degree</option>
                <option value="bachelor" className="bg-dark-900">Bachelor's Degree</option>
                <option value="master" className="bg-dark-900">Master's Degree</option>
                <option value="phd" className="bg-dark-900">PhD</option>
              </select>
              {errors.education && <p className="text-red-400 text-sm mt-1">{errors.education}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">University/Institution</label>
                <input
                  type="text"
                  name="university"
                  value={formData.university}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="University name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Graduation Year</label>
                <input
                  type="number"
                  name="graduationYear"
                  value={formData.graduationYear}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="2024"
                  min="1950"
                  max="2030"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Available Start Date *</label>
                <input
                  type="date"
                  name="availableStartDate"
                  value={formData.availableStartDate}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                />
                {errors.availableStartDate && <p className="text-red-400 text-sm mt-1">{errors.availableStartDate}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Employment Type</label>
                <select
                  name="employmentType"
                  value={formData.employmentType}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                >
                  <option value="full-time" className="bg-dark-900">Full-time</option>
                  <option value="part-time" className="bg-dark-900">Part-time</option>
                  <option value="contract" className="bg-dark-900">Contract</option>
                  <option value="freelance" className="bg-dark-900">Freelance</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Work Authorization *</label>
              <select
                name="workAuthorization"
                value={formData.workAuthorization}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
              >
                <option value="" className="bg-dark-900">Select Authorization Status</option>
                <option value="citizen" className="bg-dark-900">Citizen</option>
                <option value="permanent-resident" className="bg-dark-900">Permanent Resident</option>
                <option value="work-visa" className="bg-dark-900">Work Visa</option>
                <option value="student-visa" className="bg-dark-900">Student Visa</option>
                <option value="other" className="bg-dark-900">Other</option>
              </select>
              {errors.workAuthorization && <p className="text-red-400 text-sm mt-1">{errors.workAuthorization}</p>}
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6">Skills & Documents</h3>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Resume/CV *</label>
              <div className="border-2 border-dashed border-white/20 rounded-xl p-8 text-center hover:border-primary-400/50 transition-all duration-300">
                <Upload className="text-gray-400 mx-auto mb-4" size={48} />
                <p className="text-white mb-2">Upload your resume</p>
                <p className="text-gray-400 text-sm mb-4">PDF, DOC, or DOCX (Max 5MB)</p>
                <input
                  type="file"
                  name="resume"
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx"
                  className="hidden"
                  id="resume-upload"
                />
                <label
                  htmlFor="resume-upload"
                  className="bg-primary-500 text-white px-6 py-2 rounded-lg cursor-pointer hover:bg-primary-600 transition-colors duration-300"
                >
                  Choose File
                </label>
                {formData.resume && (
                  <p className="text-green-400 text-sm mt-2">✓ {formData.resume.name}</p>
                )}
              </div>
              {errors.resume && <p className="text-red-400 text-sm mt-1">{errors.resume}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Portfolio URL</label>
                <input
                  type="url"
                  name="portfolioUrl"
                  value={formData.portfolioUrl}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="https://yourportfolio.com"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">LinkedIn Profile</label>
                <input
                  type="url"
                  name="linkedinUrl"
                  value={formData.linkedinUrl}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  placeholder="https://linkedin.com/in/yourprofile"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">GitHub Profile</label>
              <input
                type="url"
                name="githubUrl"
                value={formData.githubUrl}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                placeholder="https://github.com/yourusername"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Technical Skills *</label>
              <textarea
                name="technicalSkills"
                value={formData.technicalSkills}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300 resize-none"
                placeholder="List your technical skills (e.g., React, Node.js, Python, AWS, etc.)"
              />
              {errors.technicalSkills && <p className="text-red-400 text-sm mt-1">{errors.technicalSkills}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Cover Letter *</label>
              <textarea
                name="coverLetter"
                value={formData.coverLetter}
                onChange={handleInputChange}
                rows={6}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300 resize-none"
                placeholder="Tell us why you're interested in this position and what makes you a great fit..."
              />
              {errors.coverLetter && <p className="text-red-400 text-sm mt-1">{errors.coverLetter}</p>}
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6">Review & Submit</h3>

            <div className="bg-white/5 rounded-xl p-6 space-y-4">
              <h4 className="text-lg font-semibold text-white">Application Summary</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Name:</span>
                  <span className="text-white ml-2">{formData.firstName} {formData.lastName}</span>
                </div>
                <div>
                  <span className="text-gray-400">Email:</span>
                  <span className="text-white ml-2">{formData.email}</span>
                </div>
                <div>
                  <span className="text-gray-400">Position:</span>
                  <span className="text-white ml-2">{formData.position || 'Not specified'}</span>
                </div>
                <div>
                  <span className="text-gray-400">Department:</span>
                  <span className="text-white ml-2">{formData.department || 'Not specified'}</span>
                </div>
                <div>
                  <span className="text-gray-400">Employment Type:</span>
                  <span className="text-white ml-2">{formData.employmentType}</span>
                </div>
                <div>
                  <span className="text-gray-400">Work Location:</span>
                  <span className="text-white ml-2">{formData.workLocation}</span>
                </div>
                <div>
                  <span className="text-gray-400">Experience:</span>
                  <span className="text-white ml-2">{formData.experience}</span>
                </div>
                <div>
                  <span className="text-gray-400">Start Date:</span>
                  <span className="text-white ml-2">{formData.availableStartDate}</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  name="backgroundCheck"
                  checked={formData.backgroundCheck}
                  onChange={handleInputChange}
                  className="w-4 h-4 text-primary-500 bg-white/5 border-white/20 rounded focus:ring-primary-400"
                />
                <label className="text-gray-300 text-sm">
                  I consent to a background check if required for this position *
                </label>
              </div>
              {errors.backgroundCheck && <p className="text-red-400 text-sm mt-1">{errors.backgroundCheck}</p>}

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  name="drugTest"
                  checked={formData.drugTest}
                  onChange={handleInputChange}
                  className="w-4 h-4 text-primary-500 bg-white/5 border-white/20 rounded focus:ring-primary-400"
                />
                <label className="text-gray-300 text-sm">
                  I consent to a drug test if required for this position
                </label>
              </div>
            </div>

            <div className="bg-primary-500/10 border border-primary-500/20 rounded-xl p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="text-primary-400 mt-0.5" size={20} />
                <div>
                  <p className="text-primary-400 font-medium text-sm">Important Notice</p>
                  <p className="text-gray-300 text-sm mt-1">
                    By submitting this application, you confirm that all information provided is accurate and complete.
                    False information may result in disqualification from consideration.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return <div>Step content not implemented yet</div>
    }
  }

  return (
    <div className="min-h-screen bg-dark-950 -mt-16 lg:-mt-20 pt-32">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.div
            className="inline-flex items-center bg-primary-500/10 border border-primary-500/20 rounded-full px-4 py-2 mb-6"
            whileHover={{ scale: 1.05 }}
          >
            <Briefcase className="text-primary-400 mr-2" size={16} />
            <span className="text-primary-400 text-sm font-medium uppercase tracking-wider">Job Application</span>
          </motion.div>
          <h1 className="text-4xl md:text-5xl font-black text-white mb-4 leading-tight">
            Join Our
            <br />
            <span className="bg-gradient-to-r from-primary-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent">
              Amazing Team
            </span>
          </h1>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            Take the next step in your career. Fill out our comprehensive application form to join Delta Xero Creations.
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-12">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const Icon = step.icon
              const isActive = currentStep === step.id
              const isCompleted = currentStep > step.id
              
              return (
                <div key={step.id} className="flex items-center">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center border-2 transition-all duration-300 ${
                    isCompleted 
                      ? 'bg-green-500 border-green-500' 
                      : isActive 
                        ? 'bg-primary-500 border-primary-500' 
                        : 'bg-white/5 border-white/20'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="text-white" size={20} />
                    ) : (
                      <Icon className={isActive ? 'text-white' : 'text-gray-400'} size={20} />
                    )}
                  </div>
                  <div className="ml-3 hidden md:block">
                    <p className={`text-sm font-medium ${isActive ? 'text-white' : 'text-gray-400'}`}>
                      Step {step.id}
                    </p>
                    <p className={`text-xs ${isActive ? 'text-primary-400' : 'text-gray-500'}`}>
                      {step.title}
                    </p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-8 h-0.5 mx-4 ${isCompleted ? 'bg-green-500' : 'bg-white/20'}`}></div>
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Form */}
        <div className="bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 rounded-3xl p-8 md:p-12">
          <form onSubmit={handleSubmit}>
            {renderStepContent()}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-12">
              <button
                type="button"
                onClick={prevStep}
                disabled={currentStep === 1}
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                  currentStep === 1
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : 'bg-white/10 border border-white/20 text-white hover:bg-white/20'
                }`}
              >
                Previous
              </button>

              {currentStep < 4 ? (
                <button
                  type="button"
                  onClick={nextStep}
                  className="bg-gradient-to-r from-primary-500 to-blue-500 text-white px-8 py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300 flex items-center space-x-2"
                >
                  <span>Next Step</span>
                  <ArrowRight size={16} />
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-8 py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300 flex items-center space-x-2 disabled:opacity-50"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Submitting...</span>
                    </>
                  ) : (
                    <>
                      <span>Submit Application</span>
                      <CheckCircle size={16} />
                    </>
                  )}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default JobApplication
