import React, { useState } from 'react'
import { motion } from 'framer-motion'
import AdminNavigation from '../../components/AdminNavigation'
import { 
  GraduationCap, 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Mail, 
  Phone, 
  MapPin,
  Calendar,
  Clock,
  Award,
  MoreVertical,
  Download,
  Upload,
  Star,
  CheckCircle
} from 'lucide-react'

const InternManagement = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [interns, setInterns] = useState([
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      position: 'Frontend Development Intern',
      department: 'Engineering',
      startDate: '2024-01-15',
      endDate: '2024-06-15',
      duration: '6 months',
      status: 'Active',
      progress: 75,
      university: 'Stanford University',
      year: 'Junior',
      gpa: '3.8',
      skills: ['React', 'JavaScript', 'CSS'],
      mentor: '<PERSON>',
      projects: ['E-commerce Dashboard', 'Mobile App UI'],
      rating: 4.5
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      position: 'UI/UX Design Intern',
      department: 'Design',
      startDate: '2024-02-01',
      endDate: '2024-07-01',
      duration: '5 months',
      status: 'Active',
      progress: 60,
      university: 'MIT',
      year: 'Senior',
      gpa: '3.9',
      skills: ['Figma', 'Adobe XD', 'User Research'],
      mentor: 'Sarah Smith',
      projects: ['Mobile App Redesign', 'Design System'],
      rating: 4.8
    },
    {
      id: 3,
      name: 'David Rodriguez',
      email: '<EMAIL>',
      phone: '+****************',
      position: 'Backend Development Intern',
      department: 'Engineering',
      startDate: '2023-09-01',
      endDate: '2024-02-01',
      duration: '6 months',
      status: 'Completed',
      progress: 100,
      university: 'UC Berkeley',
      year: 'Graduate',
      gpa: '3.7',
      skills: ['Python', 'Django', 'PostgreSQL'],
      mentor: 'Mike Johnson',
      projects: ['API Development', 'Database Optimization'],
      rating: 4.6
    },
    {
      id: 4,
      name: 'Lisa Park',
      email: '<EMAIL>',
      phone: '+****************',
      position: 'Marketing Intern',
      department: 'Marketing',
      startDate: '2024-01-01',
      endDate: '2024-04-01',
      duration: '3 months',
      status: 'On Hold',
      progress: 40,
      university: 'NYU',
      year: 'Sophomore',
      gpa: '3.6',
      skills: ['Social Media', 'Content Creation', 'Analytics'],
      mentor: 'Emily Davis',
      projects: ['Social Media Campaign', 'Content Strategy'],
      rating: 4.2
    }
  ])

  const statuses = ['all', 'Active', 'Completed', 'On Hold', 'Terminated']

  const filteredInterns = interns.filter(intern => {
    const matchesSearch = intern.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         intern.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         intern.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         intern.university.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = filterStatus === 'all' || intern.status === filterStatus
    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'Active': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'Completed': return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'On Hold': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'Terminated': return 'bg-red-500/20 text-red-400 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getProgressColor = (progress) => {
    if (progress >= 80) return 'bg-green-500'
    if (progress >= 60) return 'bg-blue-500'
    if (progress >= 40) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  return (
    <div className="min-h-screen bg-dark-950">
      <AdminNavigation />
      <div className="pt-20 pb-20 md:pb-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-black text-white mb-2">Intern Management</h1>
            <p className="text-gray-400">Manage your interns and track their progress</p>
          </div>
          <div className="flex items-center space-x-4">
            <button className="bg-white/10 border border-white/20 rounded-xl px-4 py-2 text-white hover:bg-white/20 transition-all duration-300 flex items-center space-x-2">
              <Upload size={16} />
              <span>Import</span>
            </button>
            <button className="bg-white/10 border border-white/20 rounded-xl px-4 py-2 text-white hover:bg-white/20 transition-all duration-300 flex items-center space-x-2">
              <Download size={16} />
              <span>Export</span>
            </button>
            <button className="bg-gradient-to-r from-primary-500 to-blue-500 text-white px-6 py-2 rounded-xl font-medium hover:shadow-lg transition-all duration-300 flex items-center space-x-2">
              <Plus size={16} />
              <span>Add Intern</span>
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {[
            { title: 'Active Interns', value: interns.filter(i => i.status === 'Active').length, color: 'from-green-500 to-emerald-500' },
            { title: 'Completed', value: interns.filter(i => i.status === 'Completed').length, color: 'from-blue-500 to-cyan-500' },
            { title: 'Average Rating', value: '4.5', color: 'from-yellow-500 to-orange-500' },
            { title: 'Total Projects', value: interns.reduce((acc, intern) => acc + intern.projects.length, 0), color: 'from-purple-500 to-pink-500' }
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 rounded-2xl p-6"
            >
              <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center mb-4`}>
                <GraduationCap className="text-white" size={24} />
              </div>
              <h3 className="text-2xl font-bold text-white mb-1">{stat.value}</h3>
              <p className="text-gray-400 text-sm">{stat.title}</p>
            </motion.div>
          ))}
        </div>

        {/* Filters */}
        <div className="bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 rounded-2xl p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search interns..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                />
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="text-gray-400" size={20} />
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="bg-white/5 border border-white/20 rounded-xl px-4 py-3 text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                >
                  {statuses.map(status => (
                    <option key={status} value={status} className="bg-dark-900 text-white">
                      {status === 'all' ? 'All Statuses' : status}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Intern Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredInterns.map((intern, index) => (
            <motion.div
              key={intern.id}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 rounded-2xl p-6 hover:border-white/20 transition-all duration-300"
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-blue-500 rounded-xl flex items-center justify-center">
                    <span className="text-white font-bold text-lg">
                      {intern.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-white font-bold">{intern.name}</h3>
                    <p className="text-gray-400 text-sm">{intern.position}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1">
                    <Star className="text-yellow-400" size={16} />
                    <span className="text-white text-sm font-medium">{intern.rating}</span>
                  </div>
                  <button className="text-gray-400 hover:text-white transition-colors duration-300">
                    <MoreVertical size={20} />
                  </button>
                </div>
              </div>

              {/* Status and Progress */}
              <div className="flex items-center justify-between mb-4">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(intern.status)}`}>
                  {intern.status}
                </span>
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400 text-sm">Progress:</span>
                  <div className="w-20 h-2 bg-white/10 rounded-full overflow-hidden">
                    <div 
                      className={`h-full ${getProgressColor(intern.progress)} transition-all duration-300`}
                      style={{ width: `${intern.progress}%` }}
                    ></div>
                  </div>
                  <span className="text-white text-sm font-medium">{intern.progress}%</span>
                </div>
              </div>

              {/* Details Grid */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-gray-300">
                    <Mail size={14} />
                    <span className="text-sm truncate">{intern.email}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-300">
                    <GraduationCap size={14} />
                    <span className="text-sm">{intern.university}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-300">
                    <Award size={14} />
                    <span className="text-sm">GPA: {intern.gpa}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-gray-300">
                    <Calendar size={14} />
                    <span className="text-sm">{intern.duration}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-300">
                    <Clock size={14} />
                    <span className="text-sm">{intern.year}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-300">
                    <CheckCircle size={14} />
                    <span className="text-sm">Mentor: {intern.mentor}</span>
                  </div>
                </div>
              </div>

              {/* Skills */}
              <div className="mb-4">
                <p className="text-gray-400 text-xs mb-2">Skills</p>
                <div className="flex flex-wrap gap-2">
                  {intern.skills.map((skill, skillIndex) => (
                    <span
                      key={skillIndex}
                      className="bg-primary-500/20 text-primary-400 px-2 py-1 rounded-lg text-xs"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              {/* Projects */}
              <div className="mb-4">
                <p className="text-gray-400 text-xs mb-2">Current Projects</p>
                <div className="space-y-1">
                  {intern.projects.map((project, projectIndex) => (
                    <div key={projectIndex} className="text-white text-sm">• {project}</div>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-white/10">
                <div className="text-gray-400 text-sm">
                  {new Date(intern.startDate).toLocaleDateString()} - {new Date(intern.endDate).toLocaleDateString()}
                </div>
                <div className="flex items-center space-x-2">
                  <button className="p-2 bg-white/10 rounded-lg hover:bg-white/20 transition-all duration-300 text-gray-400 hover:text-white">
                    <Eye size={16} />
                  </button>
                  <button className="p-2 bg-white/10 rounded-lg hover:bg-white/20 transition-all duration-300 text-gray-400 hover:text-white">
                    <Edit size={16} />
                  </button>
                  <button className="p-2 bg-red-500/20 rounded-lg hover:bg-red-500/30 transition-all duration-300 text-red-400 hover:text-red-300">
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {filteredInterns.length === 0 && (
          <div className="text-center py-12">
            <GraduationCap className="text-gray-400 mx-auto mb-4" size={64} />
            <h3 className="text-xl font-bold text-white mb-2">No interns found</h3>
            <p className="text-gray-400 mb-6">Try adjusting your search or filter criteria</p>
            <button className="bg-gradient-to-r from-primary-500 to-blue-500 text-white px-6 py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300">
              Add First Intern
            </button>
          </div>
        )}
      </div>
      </div>
    </div>
  )
}

export default InternManagement
