import { supabase, STORAGE_BUCKETS } from './supabase'
import type { JobApplication, InternshipApplication, ApplicationReference } from './supabase'

// Utility function to get file URL for viewing (now uses signed URLs as primary)
export const getFileUrl = async (filePath: string, bucketName: string = 'resumes'): Promise<string | null> => {
  if (!filePath) return null

  console.log('Original file path:', filePath)

  // If it's already a full URL, try to get a fresh signed URL for better access
  if (filePath.startsWith('http')) {
    console.log('Full URL detected, extracting path for signed URL generation')

    // Extract the file path from the URL for signed URL generation
    const match = filePath.match(/\/storage\/v1\/object\/public\/([^\/]+)\/(.+)$/)
    if (match) {
      const bucket = match[1]
      const path = match[2]

      try {
        const { data, error } = await supabase.storage
          .from(bucket)
          .createSignedUrl(path, 3600) // 1 hour expiry

        if (error) {
          console.error('Error creating signed URL:', error)
          return filePath // Return original URL as fallback
        }

        console.log('Generated signed URL from full URL:', data.signedUrl)
        return data.signedUrl
      } catch (error) {
        console.error('Error getting signed URL:', error)
        return filePath
      }
    }
    return filePath
  }

  // Generate signed URL for path
  try {
    const { data, error } = await supabase.storage
      .from(bucketName)
      .createSignedUrl(filePath, 3600)

    if (error) {
      console.error('Error creating signed URL:', error)
      // Fallback to public URL
      const { data: publicData } = supabase.storage.from(bucketName).getPublicUrl(filePath)
      return publicData.publicUrl
    }

    console.log('Generated signed URL:', data.signedUrl)
    return data.signedUrl
  } catch (error) {
    console.error('Error getting signed URL:', error)
    return filePath
  }
}

// Keep the old function for backward compatibility
export const getPublicFileUrl = (filePath: string, bucketName: string = 'resumes'): string | null => {
  if (!filePath) return null

  // If it's already a full URL, return as is
  if (filePath.startsWith('http')) {
    return filePath
  }

  // Generate public URL (fallback method)
  try {
    const { data } = supabase.storage.from(bucketName).getPublicUrl(filePath)
    return data.publicUrl
  } catch (error) {
    console.error('Error getting public URL:', error)
    return filePath
  }
}

// Utility function to get signed URL as fallback
export const getSignedFileUrl = async (filePath: string, bucketName: string = 'resumes'): Promise<string | null> => {
  if (!filePath) return null

  console.log('Getting signed URL for:', filePath)

  // If it's already a full URL, return as is
  if (filePath.startsWith('http')) {
    // Extract the file path from the URL for signed URL generation
    const match = filePath.match(/\/storage\/v1\/object\/public\/([^\/]+)\/(.+)$/)
    if (match) {
      const bucket = match[1]
      const path = match[2]

      try {
        const { data, error } = await supabase.storage
          .from(bucket)
          .createSignedUrl(path, 3600) // 1 hour expiry

        if (error) {
          console.error('Error creating signed URL:', error)
          return filePath
        }

        console.log('Generated signed URL:', data.signedUrl)
        return data.signedUrl
      } catch (error) {
        console.error('Error getting signed URL:', error)
        return filePath
      }
    }
    return filePath
  }

  // Generate signed URL for path
  try {
    const { data, error } = await supabase.storage
      .from(bucketName)
      .createSignedUrl(filePath, 3600)

    if (error) {
      console.error('Error creating signed URL:', error)
      return null
    }

    console.log('Generated signed URL:', data.signedUrl)
    return data.signedUrl
  } catch (error) {
    console.error('Error getting signed URL:', error)
    return null
  }
}

// Test function to check file access
export const testFileAccess = (fileName: string = 'APP-1752871432210-84ui9rqzk/resume.pdf') => {
  try {
    console.log('Testing file access for:', fileName)

    // Use the same method as upload function
    const { data } = supabase.storage.from('resumes').getPublicUrl(fileName)
    console.log('Generated public URL:', data.publicUrl)

    // Try to fetch the URL to see if it's accessible
    fetch(data.publicUrl, { method: 'HEAD' })
      .then(response => {
        console.log('File accessibility test:', response.status, response.statusText)
      })
      .catch(fetchError => {
        console.error('File fetch test error:', fetchError)
      })

    return data.publicUrl

  } catch (error) {
    console.error('Test file access error:', error)
    return null
  }
}

// Utility function to check storage buckets (read-only)
export const checkStorageBuckets = async () => {
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets()

    if (error) {
      console.error('Error listing buckets:', error)
      return []
    }

    console.log('Existing buckets:', buckets?.map(b => ({ name: b.name, public: b.public })))

    // Check if resumes bucket is public
    const resumesBucket = buckets?.find(b => b.name === 'resumes')
    if (resumesBucket) {
      console.log('Resumes bucket public status:', resumesBucket.public)
      if (!resumesBucket.public) {
        console.warn('Resumes bucket is not public - files may not be accessible')
      }
    }

    return buckets || []
  } catch (error) {
    console.error('Error checking storage buckets:', error)
    return []
  }
}

// File upload utilities
export const uploadFile = async (
  file: File,
  bucket: string,
  path: string
): Promise<{ url: string; error: null } | { url: null; error: string }> => {
  try {
    const fileExt = file.name.split('.').pop()
    const fileName = `${path}.${fileExt}`
    
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: true
      })

    if (error) {
      console.error('Upload error:', error)
      return { url: null, error: error.message }
    }

    const { data: { publicUrl } } = supabase.storage
      .from(bucket)
      .getPublicUrl(fileName)

    return { url: publicUrl, error: null }
  } catch (error) {
    console.error('Upload error:', error)
    return { url: null, error: 'Failed to upload file' }
  }
}

// Job Application API
export const jobApplicationAPI = {
  // Submit a new job application
  submit: async (formData: any, files: { resume?: File; portfolio?: File; coverLetterFile?: File }) => {
    try {
      // Generate unique application ID
      const applicationId = `APP-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      
      // Upload files if provided
      let resumeUrl = null
      let portfolioUrl = null
      let coverLetterUrl = null

      if (files.resume) {
        const result = await uploadFile(files.resume, STORAGE_BUCKETS.RESUMES, `${applicationId}/resume`)
        if (result.error) throw new Error(result.error)
        resumeUrl = result.url
      }

      if (files.portfolio) {
        const result = await uploadFile(files.portfolio, STORAGE_BUCKETS.PORTFOLIOS, `${applicationId}/portfolio`)
        if (result.error) throw new Error(result.error)
        portfolioUrl = result.url
      }

      if (files.coverLetterFile) {
        const result = await uploadFile(files.coverLetterFile, STORAGE_BUCKETS.COVER_LETTERS, `${applicationId}/cover-letter`)
        if (result.error) throw new Error(result.error)
        coverLetterUrl = result.url
      }

      // Prepare application data
      const applicationData = {
        application_id: applicationId,
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        address: formData.address,
        city: formData.city,
        state: formData.state,
        zip_code: formData.zipCode,
        country: formData.country,
        position: formData.position,
        department: formData.department,
        available_start_date: formData.availableStartDate,
        employment_type: formData.employmentType,
        work_location: formData.workLocation,
        experience_level: formData.experience,
        current_company: formData.currentCompany,
        current_position: formData.currentPosition,
        notice_period: formData.noticePeriod,
        total_experience: formData.totalExperience,
        relevant_experience: formData.relevantExperience,
        education_level: formData.education,
        university: formData.university,
        graduation_year: formData.graduationYear,
        gpa: formData.gpa,
        degree: formData.degree,
        field_of_study: formData.fieldOfStudy,
        technical_skills: formData.technicalSkills,
        soft_skills: formData.softSkills,
        certifications: formData.certifications,
        languages: formData.languages,
        portfolio_url: formData.portfolioUrl,
        linkedin_url: formData.linkedinUrl,
        github_url: formData.githubUrl,
        website_url: formData.websiteUrl,
        cover_letter: formData.coverLetter,
        resume_url: resumeUrl,
        portfolio_file_url: portfolioUrl,
        cover_letter_file_url: coverLetterUrl,
        work_authorization: formData.workAuthorization,
        background_check_consent: formData.backgroundCheck,
        drug_test_consent: formData.drugTest,
        visa_sponsorship_required: formData.visaSponsorship,
        status: 'submitted',
        submitted_at: new Date().toISOString()
      }

      // Insert application
      const { data: application, error: appError } = await supabase
        .from('job_applications')
        .insert(applicationData)
        .select()
        .single()

      if (appError) throw appError

      // Insert references if provided
      if (formData.references && formData.references.length > 0) {
        const references = formData.references
          .filter((ref: any) => ref.name && ref.email)
          .map((ref: any) => ({
            job_application_id: application.id,
            name: ref.name,
            title: ref.position,
            company: ref.company,
            email: ref.email,
            phone: ref.phone,
            relationship: ref.relationship
          }))

        if (references.length > 0) {
          const { error: refError } = await supabase
            .from('application_references')
            .insert(references)

          if (refError) console.error('Error inserting references:', refError)
        }
      }

      return { data: application, error: null }
    } catch (error) {
      console.error('Error submitting job application:', error)
      return { data: null, error: error.message }
    }
  },

  // Save as draft
  saveDraft: async (formData: any) => {
    try {
      const applicationId = formData.applicationId || `DRAFT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      
      const draftData = {
        application_id: applicationId,
        first_name: formData.firstName || '',
        last_name: formData.lastName || '',
        email: formData.email || '',
        phone: formData.phone || '',
        position: formData.position || '',
        department: formData.department || '',
        status: 'draft'
      }

      const { data, error } = await supabase
        .from('job_applications')
        .upsert(draftData, { onConflict: 'application_id' })
        .select()
        .single()

      if (error) throw error

      return { data, error: null }
    } catch (error) {
      console.error('Error saving draft:', error)
      return { data: null, error: error.message }
    }
  },

  // Get all applications (admin)
  getAll: async () => {
    try {
      const { data, error } = await supabase
        .from('job_applications')
        .select(`
          *,
          application_references (*)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error fetching applications:', error)
      return { data: null, error: error.message }
    }
  },

  // Get application by ID
  getById: async (id: string) => {
    try {
      const { data, error } = await supabase
        .from('job_applications')
        .select(`
          *,
          application_references (*)
        `)
        .eq('id', id)
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error fetching application:', error)
      return { data: null, error: error.message }
    }
  },

  // Update application status
  updateStatus: async (id: string, status: string, notes?: string) => {
    try {
      const { data, error } = await supabase
        .from('job_applications')
        .update({
          status,
          notes,
          reviewed_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error updating application status:', error)
      return { data: null, error: error.message }
    }
  }
}

// Internship Application API
export const internshipApplicationAPI = {
  // Submit a new internship application
  submit: async (formData: any, files: { resume?: File; transcript?: File }) => {
    try {
      // Generate unique application ID
      const applicationId = `INT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      
      // Upload files if provided
      let resumeUrl = null
      let transcriptUrl = null

      if (files.resume) {
        const result = await uploadFile(files.resume, STORAGE_BUCKETS.RESUMES, `${applicationId}/resume`)
        if (result.error) throw new Error(result.error)
        resumeUrl = result.url
      }

      if (files.transcript) {
        const result = await uploadFile(files.transcript, STORAGE_BUCKETS.TRANSCRIPTS, `${applicationId}/transcript`)
        if (result.error) throw new Error(result.error)
        transcriptUrl = result.url
      }

      // Prepare application data
      const applicationData = {
        application_id: applicationId,
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        address: formData.address,
        city: formData.city,
        state: formData.state,
        zip_code: formData.zipCode,
        country: formData.country,
        date_of_birth: formData.dateOfBirth,
        university: formData.university,
        major: formData.major,
        gpa: formData.gpa,
        graduation_date: formData.graduationDate,
        position: formData.position,
        department: formData.department,
        duration: formData.duration,
        start_date: formData.startDate,
        work_days: formData.workDays,
        previous_internships: formData.previousInternships,
        relevant_courses: formData.relevantCourses,
        skills: Array.isArray(formData.skills) ? formData.skills : [],
        projects: formData.projects,
        portfolio_url: formData.portfolioUrl,
        linkedin_url: formData.linkedinUrl,
        github_url: formData.githubUrl,
        cover_letter: formData.coverLetter,
        resume_url: resumeUrl,
        transcript_url: transcriptUrl,
        background_check_consent: formData.backgroundCheck,
        agreement_consent: formData.agreement,
        status: 'submitted',
        submitted_at: new Date().toISOString()
      }

      // Insert application
      const { data: application, error: appError } = await supabase
        .from('internship_applications')
        .insert(applicationData)
        .select()
        .single()

      if (appError) throw appError

      // Insert references if provided
      if (formData.references && formData.references.length > 0) {
        const references = formData.references
          .filter((ref: any) => ref.name && ref.email)
          .map((ref: any) => ({
            internship_application_id: application.id,
            name: ref.name,
            title: ref.title,
            company: ref.company || ref.title,
            email: ref.email,
            phone: ref.phone,
            relationship: ref.relationship
          }))

        if (references.length > 0) {
          const { error: refError } = await supabase
            .from('application_references')
            .insert(references)

          if (refError) console.error('Error inserting references:', refError)
        }
      }

      return { data: application, error: null }
    } catch (error) {
      console.error('Error submitting internship application:', error)
      return { data: null, error: error.message }
    }
  },

  // Save as draft
  saveDraft: async (formData: any) => {
    try {
      const applicationId = formData.applicationId || `INT-DRAFT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      
      const draftData = {
        application_id: applicationId,
        first_name: formData.firstName || '',
        last_name: formData.lastName || '',
        email: formData.email || '',
        phone: formData.phone || '',
        university: formData.university || '',
        major: formData.major || '',
        position: formData.position || '',
        department: formData.department || '',
        status: 'draft'
      }

      const { data, error } = await supabase
        .from('internship_applications')
        .upsert(draftData, { onConflict: 'application_id' })
        .select()
        .single()

      if (error) throw error

      return { data, error: null }
    } catch (error) {
      console.error('Error saving draft:', error)
      return { data: null, error: error.message }
    }
  },

  // Get all applications (admin)
  getAll: async () => {
    try {
      const { data, error } = await supabase
        .from('internship_applications')
        .select(`
          *,
          application_references (*)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error fetching applications:', error)
      return { data: null, error: error.message }
    }
  },

  // Update application status
  updateStatus: async (id: string, status: string, notes?: string) => {
    try {
      const { data, error } = await supabase
        .from('internship_applications')
        .update({
          status,
          notes,
          reviewed_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error updating application status:', error)
      return { data: null, error: error.message }
    }
  },

  // Delete application
  delete: async (id: string) => {
    try {
      const { error } = await supabase
        .from('internship_applications')
        .delete()
        .eq('id', id)

      if (error) throw error
      return { error: null }
    } catch (error) {
      console.error('Error deleting internship application:', error)
      return { error: error.message }
    }
  }
}

// Quote Request API
export const quoteRequestAPI = {
  // Submit a new quote request
  submit: async (formData: any) => {
    try {
      // Generate unique request ID
      const requestId = `QUO-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

      // Prepare quote request data
      const quoteData = {
        request_id: requestId,
        name: formData.name,
        email: formData.email,
        company: formData.company || null,
        phone: formData.phone || null,
        message: formData.message,
        services: formData.services || [],
        project_type: formData.projectType || null,
        budget_range: formData.budgetRange || null,
        timeline: formData.timeline || null,
        website_url: formData.websiteUrl || null,
        reference_urls: formData.referenceUrls || [],
        special_requirements: formData.specialRequirements || null,
        status: 'new',
        priority: 'normal'
      }

      // Insert quote request
      const { data: quoteRequest, error: quoteError } = await supabase
        .from('quote_requests')
        .insert(quoteData)
        .select()
        .single()

      if (quoteError) throw quoteError

      return { data: quoteRequest, error: null }
    } catch (error) {
      console.error('Error submitting quote request:', error)
      return { data: null, error: error.message }
    }
  },

  // Get all quote requests (admin)
  getAll: async () => {
    try {
      const { data, error } = await supabase
        .from('quote_requests')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error fetching quote requests:', error)
      return { data: null, error: error.message }
    }
  },

  // Update quote request status
  updateStatus: async (id: string, status: string, notes?: string) => {
    try {
      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      }

      if (notes) {
        updateData.quote_notes = notes
      }

      if (status === 'quoted') {
        updateData.quote_sent_at = new Date().toISOString()
      }

      if (status === 'reviewing') {
        updateData.responded_at = new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('quote_requests')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error updating quote request status:', error)
      return { data: null, error: error.message }
    }
  },

  // Delete quote request
  delete: async (id: string) => {
    try {
      const { error } = await supabase
        .from('quote_requests')
        .delete()
        .eq('id', id)

      if (error) throw error
      return { error: null }
    } catch (error) {
      console.error('Error deleting quote request:', error)
      return { error: error.message }
    }
  }
}

// Dashboard API
export const dashboardAPI = {
  // Get dashboard statistics
  getStats: async () => {
    try {
      // Get job applications count
      const { count: jobAppsCount, error: jobError } = await supabase
        .from('job_applications')
        .select('*', { count: 'exact', head: true })

      if (jobError) throw jobError

      // Get internship applications count
      const { count: internshipAppsCount, error: internshipError } = await supabase
        .from('internship_applications')
        .select('*', { count: 'exact', head: true })

      if (internshipError) throw internshipError

      // Get pending job applications count
      const { count: pendingJobAppsCount, error: pendingJobError } = await supabase
        .from('job_applications')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'submitted')

      if (pendingJobError) throw pendingJobError

      // Get pending internship applications count
      const { count: pendingInternshipAppsCount, error: pendingInternshipError } = await supabase
        .from('internship_applications')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'submitted')

      if (pendingInternshipError) throw pendingInternshipError

      // Get employees count (if table exists)
      const { count: employeesCount } = await supabase
        .from('employees')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)

      // Get interns count (if table exists)
      const { count: internsCount } = await supabase
        .from('interns')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)

      const data = {
        total_job_applications: jobAppsCount || 0,
        total_internship_applications: internshipAppsCount || 0,
        pending_job_applications: pendingJobAppsCount || 0,
        pending_internship_applications: pendingInternshipAppsCount || 0,
        total_employees: employeesCount || 0,
        total_interns: internsCount || 0
      }

      return { data, error: null }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      return { data: null, error: error.message }
    }
  },

  // Get recent applications
  getRecentApplications: async (limit = 10) => {
    try {
      // Get recent job applications
      const { data: jobApps, error: jobError } = await supabase
        .from('job_applications')
        .select('id, application_id, first_name, last_name, position, submitted_at')
        .order('submitted_at', { ascending: false })
        .limit(Math.ceil(limit / 2))

      if (jobError) throw jobError

      // Get recent internship applications
      const { data: internshipApps, error: internshipError } = await supabase
        .from('internship_applications')
        .select('id, application_id, first_name, last_name, position, submitted_at')
        .order('submitted_at', { ascending: false })
        .limit(Math.ceil(limit / 2))

      if (internshipError) throw internshipError

      // Combine and format applications
      const jobAppsFormatted = (jobApps || []).map(app => ({
        ...app,
        application_type: 'job'
      }))

      const internshipAppsFormatted = (internshipApps || []).map(app => ({
        ...app,
        application_type: 'internship'
      }))

      const allApps = [...jobAppsFormatted, ...internshipAppsFormatted]
        .sort((a, b) => new Date(b.submitted_at) - new Date(a.submitted_at))
        .slice(0, limit)

      return { data: allApps, error: null }
    } catch (error) {
      console.error('Error fetching recent applications:', error)
      return { data: null, error: error.message }
    }
  }
}

// User Management API
export const userAPI = {
  // Create a new user (admin only)
  create: async (userData: {
    username: string
    email: string
    password: string
    role: string
    firstName?: string
    lastName?: string
  }) => {
    try {
      // Hash the password (in production, this should be done server-side)
      const bcrypt = await import('bcryptjs')
      const saltRounds = 10
      const passwordHash = await bcrypt.hash(userData.password, saltRounds)

      const newUser = {
        username: userData.username,
        email: userData.email,
        password_hash: passwordHash,
        first_name: userData.firstName || null,
        last_name: userData.lastName || null,
        role: userData.role,
        is_temporary_password: true,
        must_change_password: true,
        is_active: true,
        created_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('users')
        .insert(newUser)
        .select()
        .single()

      if (error) throw error

      return { data, error: null }
    } catch (error: any) {
      console.error('Error creating user:', error)
      return { data: null, error: error.message }
    }
  },

  // Get all users (admin only)
  getAll: async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, username, email, first_name, last_name, role, is_active, created_at, last_login, is_temporary_password')
        .order('created_at', { ascending: false })

      if (error) throw error

      return { data, error: null }
    } catch (error: any) {
      console.error('Error fetching users:', error)
      return { data: null, error: error.message }
    }
  },

  // Get user by ID
  getById: async (id: string) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, username, email, first_name, last_name, role, is_active, created_at, last_login, is_temporary_password')
        .eq('id', id)
        .single()

      if (error) throw error

      return { data, error: null }
    } catch (error: any) {
      console.error('Error fetching user:', error)
      return { data: null, error: error.message }
    }
  },

  // Update user
  update: async (id: string, updates: any) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      return { data, error: null }
    } catch (error: any) {
      console.error('Error updating user:', error)
      return { data: null, error: error.message }
    }
  },

  // Delete user
  delete: async (id: string) => {
    try {
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', id)

      if (error) throw error

      return { error: null }
    } catch (error: any) {
      console.error('Error deleting user:', error)
      return { error: error.message }
    }
  },

  // Change password
  changePassword: async (id: string, newPassword: string) => {
    try {
      // Hash the new password
      const bcrypt = await import('bcryptjs')
      const saltRounds = 10
      const passwordHash = await bcrypt.hash(newPassword, saltRounds)

      const { data, error } = await supabase
        .from('users')
        .update({
          password_hash: passwordHash,
          is_temporary_password: false,
          must_change_password: false,
          password_changed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      return { data, error: null }
    } catch (error: any) {
      console.error('Error changing password:', error)
      return { data: null, error: error.message }
    }
  },

  // Login user
  login: async (usernameOrEmail: string, password: string) => {
    try {
      // Get user by username or email
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('id, username, email, password_hash, role, is_active, is_temporary_password, must_change_password')
        .or(`username.eq.${usernameOrEmail},email.eq.${usernameOrEmail}`)
        .single()

      if (userError || !user) {
        return { data: null, error: 'Invalid credentials' }
      }

      if (!user.is_active) {
        return { data: null, error: 'Account is deactivated' }
      }

      // Verify password
      const bcrypt = await import('bcryptjs')
      const isValidPassword = await bcrypt.compare(password, user.password_hash)

      if (!isValidPassword) {
        return { data: null, error: 'Invalid credentials' }
      }

      // Update last login
      await supabase
        .from('users')
        .update({ last_login: new Date().toISOString() })
        .eq('id', user.id)

      // Return user data without password hash
      const { password_hash, ...userData } = user
      return {
        data: userData,
        error: null,
        requiresPasswordChange: user.must_change_password || user.is_temporary_password
      }
    } catch (error: any) {
      console.error('Error during login:', error)
      return { data: null, error: error.message }
    }
  }
}
