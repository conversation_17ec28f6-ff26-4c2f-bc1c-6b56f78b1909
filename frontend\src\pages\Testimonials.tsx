import React from 'react'
import { motion } from 'framer-motion'
import TestimonialsSection from '../components/TestimonialsSection'

const Testimonials = () => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen"
    >
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-dark-950 via-dark-900 to-primary-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <motion.h1 
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              className="text-5xl md:text-6xl font-bold text-white mb-6"
            >
              Client <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Testimonials</span>
            </motion.h1>
            <motion.p 
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-xl text-gray-300 max-w-3xl mx-auto"
            >
              Discover what our clients have to say about their experience working with Delta Xero Creations. 
              Real stories from real businesses who've transformed their digital presence with us.
            </motion.p>
          </div>
        </div>
      </section>

      {/* Testimonials Component */}
      <TestimonialsSection />

      {/* Stats Section */}
      <section className="py-20 bg-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            {[
              { number: '150+', label: 'Happy Clients' },
              { number: '99%', label: 'Satisfaction Rate' },
              { number: '4.9/5', label: 'Average Rating' },
              { number: '24/7', label: 'Support Available' }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-dark-800 p-6 rounded-xl border border-gray-700"
              >
                <div className="text-3xl md:text-4xl font-bold text-primary-400 mb-2">
                  {stat.number}
                </div>
                <p className="text-gray-400">{stat.label}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary-900/20 to-dark-950">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Join Our <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Success Stories</span>?
          </h2>
          <p className="text-xl text-gray-400 mb-8">
            Let's create your success story together. Start your project today and experience the Delta Xero difference.
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-12 py-4 rounded-lg font-semibold text-lg hover:shadow-lg hover:shadow-primary-500/25 transition-all duration-300"
          >
            Start Your Project
          </motion.button>
        </div>
      </section>
    </motion.div>
  )
}

export default Testimonials
