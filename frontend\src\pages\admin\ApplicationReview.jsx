import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import AdminNavigation from '../../components/AdminNavigation'
import {
  FileText,
  Search,
  Filter,
  Eye,
  Download,
  Check,
  X,
  Clock,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Briefcase,
  GraduationCap,
  Star,
  MessageSquare,
  ExternalLink,
  User,
  Award,
  BookOpen,
  Trash2
} from 'lucide-react'
import { jobApplicationAPI, internshipApplicationAPI, getFileUrl, getPublicFileUrl, getSignedFileUrl, checkStorageBuckets, testFileAccess } from '../../lib/api'

const ApplicationReview = () => {
  const [activeTab, setActiveTab] = useState('job')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [selectedApplication, setSelectedApplication] = useState(null)
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [loadingResumeUrl, setLoadingResumeUrl] = useState(false)
  const [resumeUrlError, setResumeUrlError] = useState(false)

  const [applications, setApplications] = useState([])

  // Fetch applications data on component mount
  useEffect(() => {
    const fetchApplications = async () => {
      try {
        setLoading(true)
        setError(null)

        // Check storage buckets
        await checkStorageBuckets()

        // Fetch job applications
        const { data: jobApps, error: jobError } = await jobApplicationAPI.getAll()
        if (jobError) throw new Error(`Job applications: ${jobError}`)

        // Fetch internship applications
        const { data: internshipApps, error: internshipError } = await internshipApplicationAPI.getAll()
        if (internshipError) throw new Error(`Internship applications: ${internshipError}`)

        // Transform and combine applications
        const transformedJobApps = (jobApps || []).map(app => ({
          id: app.id,
          applicationId: app.application_id,
          type: 'job',
          applicantName: `${app.first_name} ${app.last_name}`,
          email: app.email,
          phone: app.phone,
          position: app.position,
          department: app.department,
          appliedDate: new Date(app.submitted_at).toLocaleDateString(),
          status: formatStatus(app.status),
          experience: app.experience_level || 'Not specified',
          location: `${app.city || ''}, ${app.state || ''}`.replace(', ,', '').replace(/^, |, $/g, '') || 'Not specified',
          workLocation: app.work_location,
          employmentType: app.employment_type,
          resumeUrl: app.resume_url,
          coverLetterUrl: app.cover_letter_file_url,
          portfolioUrl: app.portfolio_url || app.portfolio_file_url,
          skills: Array.isArray(app.technical_skills) ? app.technical_skills : (app.technical_skills ? app.technical_skills.split(',').map(s => s.trim()) : []),
          education: app.education_level || 'Not specified',
          university: app.university,
          graduationYear: app.graduation_year,
          currentCompany: app.current_company,
          currentPosition: app.current_position,
          noticePeriod: app.notice_period,
          workAuthorization: app.work_authorization,
          linkedinUrl: app.linkedin_url,
          githubUrl: app.github_url,
          websiteUrl: app.website_url,
          coverLetter: app.cover_letter,
          rating: null, // Can be added later
          notes: app.notes || '',
          createdAt: app.created_at,
          updatedAt: app.updated_at
        }))

        const transformedInternshipApps = (internshipApps || []).map(app => ({
          id: app.id,
          applicationId: app.application_id,
          type: 'internship',
          applicantName: `${app.first_name} ${app.last_name}`,
          email: app.email,
          phone: app.phone,
          position: app.position,
          department: app.department,
          appliedDate: new Date(app.submitted_at).toLocaleDateString(),
          status: formatStatus(app.status),
          experience: 'Student',
          location: `${app.city || ''}, ${app.state || ''}`.replace(', ,', '').replace(/^, |, $/g, '') || 'Not specified',
          duration: app.duration,
          internshipType: app.internship_type,
          hoursPerWeek: app.hours_per_week,
          resumeUrl: app.resume_url,
          transcriptUrl: app.transcript_url,
          portfolioUrl: app.portfolio_url,
          skills: Array.isArray(app.skills) ? app.skills : (app.skills ? app.skills.split(',').map(s => s.trim()) : []),
          education: `${app.year_level} - ${app.university}`,
          university: app.university,
          major: app.major,
          gpa: app.gpa,
          graduationDate: app.graduation_date,
          previousInternships: app.previous_internships,
          relevantCourses: app.relevant_courses,
          projects: app.projects,
          linkedinUrl: app.linkedin_url,
          githubUrl: app.github_url,
          coverLetter: app.cover_letter,
          workAuthorization: app.work_authorization,
          rating: null, // Can be added later
          notes: app.notes || '',
          createdAt: app.created_at,
          updatedAt: app.updated_at
        }))

        // Combine and sort by submission date
        const allApplications = [...transformedJobApps, ...transformedInternshipApps]
          .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))

        console.log('Loaded applications:', allApplications.length)
        if (allApplications.length > 0) {
          console.log('Sample application data:', allApplications[0])
          console.log('Sample resume URL:', allApplications[0].resumeUrl)
        }

        setApplications(allApplications)

      } catch (err) {
        console.error('Error fetching applications:', err)
        setError(err.message)

        // Fallback to empty array on error
        setApplications([])
      } finally {
        setLoading(false)
      }
    }

    fetchApplications()
  }, [])

  // Helper function to format status
  const formatStatus = (status) => {
    const statusMap = {
      'submitted': 'Pending',
      'under_review': 'Under Review',
      'reviewing': 'Reviewing',
      'interviewed': 'Interviewed',
      'approved': 'Approved',
      'accepted': 'Accepted',
      'rejected': 'Rejected',
      'withdrawn': 'Withdrawn'
    }
    return statusMap[status] || status
  }

  // Helper function to get status color
  const getStatusColor = (status) => {
    const colorMap = {
      'Pending': 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20',
      'Under Review': 'text-blue-400 bg-blue-500/10 border-blue-500/20',
      'Reviewing': 'text-blue-500 bg-blue-600/10 border-blue-600/20',
      'Interviewed': 'text-purple-400 bg-purple-500/10 border-purple-500/20',
      'Approved': 'text-green-400 bg-green-500/10 border-green-500/20',
      'Accepted': 'text-green-500 bg-green-600/10 border-green-600/20',
      'Rejected': 'text-red-400 bg-red-500/10 border-red-500/20',
      'Withdrawn': 'text-gray-400 bg-gray-500/10 border-gray-500/20'
    }
    return colorMap[status] || 'text-gray-400 bg-gray-500/10 border-gray-500/20'
  }

  // Handle status change
  const handleStatusChange = async (applicationId, newStatus) => {
    try {
      // Find the application to determine its type
      const application = applications.find(app => app.id === applicationId)
      if (!application) return

      // Map display status to database status
      const statusMapping = {
        'reviewing': 'reviewing',
        'interviewed': 'interviewed',
        'approved': 'approved',
        'rejected': 'rejected'
      }

      const statusValue = statusMapping[newStatus] || newStatus

      if (application.type === 'job') {
        const { error } = await jobApplicationAPI.updateStatus(applicationId, statusValue)
        if (error) throw new Error(error)
      } else {
        const { error } = await internshipApplicationAPI.updateStatus(applicationId, statusValue)
        if (error) throw new Error(error)
      }

      // Map back to display format for UI
      const displayStatusMapping = {
        'reviewing': 'Reviewing',
        'interviewed': 'Interviewed',
        'approved': 'Approved',
        'rejected': 'Rejected'
      }

      const displayStatus = displayStatusMapping[statusValue] || statusValue

      // Update local state
      setApplications(prev => prev.map(app =>
        app.id === applicationId
          ? { ...app, status: displayStatus }
          : app
      ))

      // Update selected application if it's the one being updated
      if (selectedApplication && selectedApplication.id === applicationId) {
        setSelectedApplication(prev => ({ ...prev, status: displayStatus }))
      }

      // Close modal after status update
      if (showDetailModal) {
        setShowDetailModal(false)
      }



    } catch (err) {
      console.error('Error updating application status:', err)
      alert('Failed to update application status. Please try again.')
    }
  }

  // Handle different status updates
  const handleAcceptApplication = (applicationId) => handleStatusChange(applicationId, 'approved')
  const handleRejectApplication = (applicationId) => handleStatusChange(applicationId, 'rejected')

  // Handle send email
  const handleSendEmail = (application) => {
    const subject = `Regarding your application for ${application.position}`
    const body = `Dear ${application.applicantName},\n\nThank you for your application for the ${application.position} position.\n\nBest regards,\nDelta Xero Creations Team`
    const mailtoUrl = `mailto:${application.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
    window.open(mailtoUrl, '_blank')
  }

  // Handle delete application
  const handleDeleteApplication = async (applicationId, applicationType) => {
    if (!confirm('Are you sure you want to delete this application? This action cannot be undone.')) {
      return
    }

    try {
      if (applicationType === 'job') {
        const { error } = await jobApplicationAPI.delete(applicationId)
        if (error) throw new Error(error)
      } else {
        const { error } = await internshipApplicationAPI.delete(applicationId)
        if (error) throw new Error(error)
      }

      // Remove from local state
      setApplications(prev => prev.filter(app => app.id !== applicationId))

      // Close modal if this application was selected
      if (selectedApplication && selectedApplication.id === applicationId) {
        setShowDetailModal(false)
      }

    } catch (err) {
      console.error('Error deleting application:', err)
      alert('Error deleting application. Please try again.')
    }
  }

  // Handle add note
  const handleAddNote = (application) => {
    const note = prompt(`Add a note for ${application.applicantName}:`)
    if (note && note.trim()) {
      // In a real application, you would save this to the database
      console.log(`Note added for ${application.applicantName}: ${note}`)
      alert('Note added successfully! (In a real app, this would be saved to the database)')
    }
  }

  // Try signed URL as fallback
  const trySignedUrl = async (application) => {
    setLoadingResumeUrl(true)
    setResumeUrlError(false)

    try {
      const fileUrl = await getFileUrl(application.resumeUrl, 'resumes')
      if (fileUrl) {
        const updatedApplication = {
          ...application,
          generatedResumeUrl: fileUrl,
          usingSignedUrl: fileUrl.includes('/sign/')
        }
        setSelectedApplication(updatedApplication)
      } else {
        setResumeUrlError(true)
      }
    } catch (error) {
      console.error('Error getting file URL:', error)
      setResumeUrlError(true)
    } finally {
      setLoadingResumeUrl(false)
    }
  }

  // Handle view details
  const handleViewDetails = async (application) => {
    setSelectedApplication(application)
    setShowDetailModal(true)
    setLoadingResumeUrl(true)
    setResumeUrlError(false)

    // Use the new primary method that tries signed URLs first
    try {
      const fileUrl = await getFileUrl(application.resumeUrl, 'resumes')
      if (fileUrl) {
        const applicationWithUrl = {
          ...application,
          generatedResumeUrl: fileUrl,
          usingSignedUrl: fileUrl.includes('/sign/')
        }
        setSelectedApplication(applicationWithUrl)
      } else {
        setResumeUrlError(true)
      }
    } catch (error) {
      console.error('Error generating file URL:', error)
      setResumeUrlError(true)
    } finally {
      setLoadingResumeUrl(false)
    }
  }

  // Handle download resume
  const handleDownloadResume = async (resumeUrl, applicantName) => {
    if (!resumeUrl) return

    try {
      // Get a fresh signed URL for download if needed
      let downloadUrl = resumeUrl
      if (!resumeUrl.includes('/sign/')) {
        console.log('Getting signed URL for download...')
        downloadUrl = await getFileUrl(resumeUrl, 'resumes') || resumeUrl
      }

      // Fetch the file as blob
      const response = await fetch(downloadUrl)
      if (!response.ok) throw new Error('Failed to fetch resume')

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)

      // Create download link
      const link = document.createElement('a')
      link.href = url
      link.download = `${applicantName.replace(/\s+/g, '_')}_Resume.pdf`
      document.body.appendChild(link)
      link.click()

      // Cleanup
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error downloading resume:', error)
      // Fallback: open in new tab
      window.open(resumeUrl, '_blank')
    }
  }

  const tabs = [
    { id: 'job', label: 'Job Applications', count: applications.filter(app => app.type === 'job').length },
    { id: 'internship', label: 'Internship Applications', count: applications.filter(app => app.type === 'internship').length }
  ]

  const statuses = ['all', 'Pending', 'Under Review', 'Approved', 'Rejected']

  const filteredApplications = applications.filter(app => {
    const matchesTab = app.type === activeTab
    const matchesSearch = app.applicantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.position.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = filterStatus === 'all' || app.status === filterStatus
    return matchesTab && matchesSearch && matchesStatus
  })

  return (
    <div className="min-h-screen bg-dark-950">
      <AdminNavigation />
      <div className="pt-20 pb-20 md:pb-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">Applications</h1>
            <p className="text-gray-400">Review and manage job and internship applications</p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => {
                console.log('Testing file access...')
                const url = testFileAccess()
                if (url) {
                  window.open(url, '_blank')
                }
              }}
              className="bg-blue-500/20 border border-blue-500/30 rounded-lg px-4 py-2 text-blue-400 hover:bg-blue-500/30 transition-all duration-300"
            >
              Test Public URL
            </button>
            <button
              onClick={async () => {
                console.log('Testing signed URL...')
                const signedUrl = await getSignedFileUrl('APP-1752871786069-z4j4jzlnf/resume.pdf', 'resumes')
                if (signedUrl) {
                  console.log('Signed URL:', signedUrl)
                  window.open(signedUrl, '_blank')
                } else {
                  console.error('Failed to get signed URL')
                }
              }}
              className="bg-green-500/20 border border-green-500/30 rounded-lg px-4 py-2 text-green-400 hover:bg-green-500/30 transition-all duration-300"
            >
              Test Signed URL
            </button>
            <button
              onClick={async () => {
                const testUrl = 'https://fvymmbniayvichrogmlt.supabase.co/storage/v1/object/public/resumes/APP-1752871786069-z4j4jzlnf/resume.pdf'
                console.log('Testing direct URL access:', testUrl)

                try {
                  const response = await fetch(testUrl, { method: 'HEAD' })
                  console.log('Direct URL test result:', response.status, response.statusText)
                  if (response.ok) {
                    window.open(testUrl, '_blank')
                  } else {
                    console.error('Direct URL failed with status:', response.status)
                  }
                } catch (error) {
                  console.error('Direct URL test error:', error)
                }
              }}
              className="bg-purple-500/20 border border-purple-500/30 rounded-lg px-4 py-2 text-purple-400 hover:bg-purple-500/30 transition-all duration-300"
            >
              Test Direct URL
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-8 border-b border-gray-700/50">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-6 py-3 font-medium transition-all duration-300 border-b-2 ${
                activeTab === tab.id
                  ? 'text-purple-400 border-purple-400'
                  : 'text-gray-400 border-transparent hover:text-white hover:border-gray-600'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Filters */}
        <div className="bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 rounded-2xl p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search applications..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                />
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="text-gray-400" size={20} />
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="bg-white/5 border border-white/20 rounded-xl px-4 py-3 text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                >
                  {statuses.map(status => (
                    <option key={status} value={status} className="bg-dark-900 text-white">
                      {status === 'all' ? 'All Statuses' : status}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
            <span className="ml-4 text-gray-400">Loading applications...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-6 mb-8">
            <div className="flex items-center space-x-3">
              <X className="text-red-400" size={20} />
              <div>
                <p className="text-red-400 font-medium">Error loading applications</p>
                <p className="text-gray-300 text-sm mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && applications.length === 0 && (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No applications found</h3>
            <p className="text-gray-400">Applications will appear here once submitted.</p>
          </div>
        )}

        {/* Applications List */}
        {!loading && !error && applications.length > 0 && (
          <div className="space-y-6">
            {filteredApplications.length === 0 ? (
              <div className="text-center py-8">
                <Search className="mx-auto h-8 w-8 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No matching applications</h3>
                <p className="text-gray-400">Try adjusting your search or filter criteria.</p>
              </div>
            ) : (
              filteredApplications.map((application, index) => (
            <motion.div
              key={application.id}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 rounded-2xl p-6 hover:border-white/20 transition-all duration-300"
            >
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                {/* Left Section - Applicant Info */}
                <div className="flex-1">
                  <div className="flex items-start space-x-4">
                    <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-blue-500 rounded-xl flex items-center justify-center">
                      <span className="text-white font-bold text-xl">
                        {application.applicantName.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-xl font-bold text-white">{application.applicantName}</h3>
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(application.status)}`}>
                          {application.status}
                        </span>
                        {application.type === 'job' ? (
                          <Briefcase className="text-blue-400" size={16} />
                        ) : (
                          <GraduationCap className="text-green-400" size={16} />
                        )}
                      </div>
                      <p className="text-lg text-gray-300 mb-3">{application.position}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2 text-gray-300">
                            <Mail size={14} />
                            <span className="text-sm">{application.email}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-gray-300">
                            <Phone size={14} />
                            <span className="text-sm">{application.phone}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-gray-300">
                            <MapPin size={14} />
                            <span className="text-sm">{application.location}</span>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2 text-gray-300">
                            <Calendar size={14} />
                            <span className="text-sm">Applied {new Date(application.appliedDate).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-gray-300">
                            <Clock size={14} />
                            <span className="text-sm">{application.experience}</span>
                          </div>
                          {application.type === 'internship' && (
                            <div className="flex items-center space-x-2 text-gray-300">
                              <BookOpen size={14} />
                              <span className="text-sm">{application.university} - {application.year}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Skills */}
                      <div className="mb-4">
                        <p className="text-gray-400 text-xs mb-2">Skills</p>
                        <div className="flex flex-wrap gap-2">
                          {application.skills.map((skill, skillIndex) => (
                            <span
                              key={skillIndex}
                              className="bg-primary-500/20 text-primary-400 px-2 py-1 rounded-lg text-xs"
                            >
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Rating and Notes */}
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <Star className="text-yellow-400" size={16} />
                          <span className="text-white font-medium">{application.rating}</span>
                        </div>
                        {application.notes && (
                          <div className="flex items-center space-x-2">
                            <MessageSquare size={14} className="text-gray-400" />
                            <span className="text-gray-300 text-sm">{application.notes}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right Section - Actions */}
                <div className="flex flex-col space-y-4 lg:w-64">
                  {/* Documents */}
                  <div className="space-y-2">
                    {application.portfolioUrl && (
                      <button
                        onClick={() => window.open(application.portfolioUrl, '_blank')}
                        className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white hover:bg-white/20 transition-all duration-300 flex items-center justify-center space-x-2"
                      >
                        <ExternalLink size={16} />
                        <span>Portfolio</span>
                      </button>
                    )}
                  </div>

                  {/* Status Actions */}
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    <button
                      onClick={() => handleAcceptApplication(application.id)}
                      className="bg-green-500/20 border border-green-500/30 rounded-lg px-3 py-2 text-green-400 hover:bg-green-500/30 transition-all duration-300 flex items-center justify-center space-x-1 text-sm"
                    >
                      <Check size={14} />
                      <span>Accept Application</span>
                    </button>
                    <button
                      onClick={() => handleRejectApplication(application.id)}
                      className="bg-red-500/20 border border-red-500/30 rounded-lg px-3 py-2 text-red-400 hover:bg-red-500/30 transition-all duration-300 flex items-center justify-center space-x-1 text-sm"
                    >
                      <X size={14} />
                      <span>Reject Application</span>
                    </button>
                  </div>

                  {/* Additional Actions */}
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleSendEmail(application)}
                      className="flex-1 bg-gray-600/20 border border-gray-600/30 rounded-lg px-3 py-2 text-gray-300 hover:bg-gray-600/30 transition-all duration-300 flex items-center justify-center space-x-1 text-sm"
                    >
                      <Mail size={14} />
                      <span>Send Email</span>
                    </button>
                    <button
                      onClick={() => handleDeleteApplication(application.id, application.type)}
                      className="flex-1 bg-red-600/20 border border-red-600/30 rounded-lg px-3 py-2 text-red-300 hover:bg-red-600/30 transition-all duration-300 flex items-center justify-center space-x-1 text-sm"
                    >
                      <Trash2 size={14} />
                      <span>Delete</span>
                    </button>
                  </div>

                  <button
                    onClick={() => handleViewDetails(application)}
                    className="w-full bg-primary-500/20 border border-primary-500/30 rounded-lg px-4 py-2 text-primary-400 hover:bg-primary-500/30 transition-all duration-300 flex items-center justify-center space-x-2"
                  >
                    <Eye size={16} />
                    <span>View Details</span>
                  </button>
                </div>
              </div>
            </motion.div>
              ))
            )}
          </div>
        )}

        {/* Application Detail Modal */}
        {showDetailModal && selectedApplication && (
          <div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowDetailModal(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-dark-800 rounded-2xl border border-gray-700 max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Modal Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-700">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-blue-500 rounded-xl flex items-center justify-center">
                    <span className="text-white font-bold">
                      {selectedApplication.applicantName.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">{selectedApplication.applicantName}</h2>
                    <p className="text-gray-400">{selectedApplication.position}</p>
                  </div>
                </div>
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="p-2 text-gray-400 hover:text-white transition-colors duration-300"
                >
                  <X size={24} />
                </button>
              </div>

              {/* Modal Content */}
              <div className="p-6 space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white mb-4">Contact Information</h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <Mail className="text-primary-400" size={16} />
                        <span className="text-gray-300">{selectedApplication.email}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Phone className="text-primary-400" size={16} />
                        <span className="text-gray-300">{selectedApplication.phone}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <MapPin className="text-primary-400" size={16} />
                        <span className="text-gray-300">{selectedApplication.location}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white mb-4">Application Details</h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <Calendar className="text-primary-400" size={16} />
                        <span className="text-gray-300">Applied: {selectedApplication.appliedDate}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Briefcase className="text-primary-400" size={16} />
                        <span className="text-gray-300">{selectedApplication.department}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(selectedApplication.status)}`}>
                          {selectedApplication.status}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Skills */}
                {selectedApplication.skills && selectedApplication.skills.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">Skills</h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedApplication.skills.map((skill, index) => (
                        <span
                          key={index}
                          className="bg-primary-500/20 text-primary-400 px-3 py-1 rounded-full text-sm font-medium"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Cover Letter */}
                {selectedApplication.coverLetter && (
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">Cover Letter</h3>
                    <div className="bg-dark-700/50 rounded-xl p-4 border border-gray-600/30">
                      <p className="text-gray-300 leading-relaxed whitespace-pre-wrap">
                        {selectedApplication.coverLetter}
                      </p>
                    </div>
                  </div>
                )}

                {/* Resume Viewer */}
                {selectedApplication.resumeUrl && (
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">Resume</h3>
                    <div className="bg-dark-700/50 rounded-xl border border-gray-600/30 overflow-hidden">
                      <div className="flex items-center justify-between p-4 border-b border-gray-600/30">
                        <span className="text-gray-300">Resume Preview</span>
                        
                      </div>
                      <div className="space-y-4">
                        {/* Debug Info */}
                        

                        {/* Action Buttons */}
                        

                        {/* Resume Viewer */}
                        {loadingResumeUrl ? (
                          <div className="flex items-center justify-center h-96 border border-gray-600 rounded-lg">
                            <div className="text-center">
                              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto mb-4"></div>
                              <p className="text-gray-400">Loading signed URL...</p>
                            </div>
                          </div>
                        ) : resumeUrlError ? (
                          <div className="flex items-center justify-center h-96 border border-gray-600 rounded-lg">
                            <div className="text-center">
                              <FileText size={48} className="mx-auto mb-4 text-red-400 opacity-50" />
                              <p className="text-red-400 mb-4">Unable to load resume</p>
                              <button
                                onClick={() => window.open(selectedApplication.resumeUrl, '_blank')}
                                className="bg-primary-500/20 border border-primary-500/30 rounded-lg px-4 py-2 text-primary-400 hover:bg-primary-500/30 transition-all duration-300"
                              >
                                Try Opening Directly
                              </button>
                            </div>
                          </div>
                        ) : selectedApplication.generatedResumeUrl ? (
                          <iframe
                            src={selectedApplication.generatedResumeUrl}
                            className="w-full h-96 border border-gray-600 rounded-lg"
                            title="Resume"
                            onLoad={() => console.log('Resume iframe loaded successfully')}
                            onError={(e) => {
                              console.error('Error loading resume iframe:', e)
                              if (!selectedApplication.usingSignedUrl) {
                                console.log('Trying signed URL as fallback...')
                                trySignedUrl(selectedApplication)
                              } else {
                                setResumeUrlError(true)
                              }
                            }}
                          />
                        ) : (
                          <div className="flex items-center justify-center h-96 border border-gray-600 rounded-lg">
                            <div className="text-center">
                              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto mb-4"></div>
                              <p className="text-gray-400">Loading resume...</p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="space-y-4 pt-6 border-t border-gray-700">
                  {/* Status Actions */}
                  <div className="grid grid-cols-2 gap-3">
                   
                    <button
                      onClick={() => handleAcceptApplication(selectedApplication.id)}
                      className="bg-green-500/20 border border-green-500/30 rounded-lg px-4 py-3 text-green-400 hover:bg-green-500/30 transition-all duration-300 flex items-center justify-center space-x-2"
                    >
                      <Check size={16} />
                      <span>Accept Application</span>
                    </button>
                    <button
                      onClick={() => handleRejectApplication(selectedApplication.id)}
                      className="bg-red-500/20 border border-red-500/30 rounded-lg px-4 py-3 text-red-400 hover:bg-red-500/30 transition-all duration-300 flex items-center justify-center space-x-2"
                    >
                      <X size={16} />
                      <span>Reject Application</span>
                    </button>
                  </div>

                  {/* Additional Actions */}
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                    <button
                      onClick={() => handleSendEmail(selectedApplication)}
                      className="bg-gray-600/20 border border-gray-600/30 rounded-lg px-4 py-3 text-gray-300 hover:bg-gray-600/30 transition-all duration-300 flex items-center justify-center space-x-2"
                    >
                      <Mail size={16} />
                      <span>Send Email</span>
                    </button>
                   
                    <button
                      onClick={() => handleDeleteApplication(selectedApplication.id, selectedApplication.type)}
                      className="bg-red-600/20 border border-red-600/30 rounded-lg px-4 py-3 text-red-300 hover:bg-red-600/30 transition-all duration-300 flex items-center justify-center space-x-2"
                    >
                      <Trash2 size={16} />
                      <span>Delete</span>
                    </button>
                    {selectedApplication.resumeUrl && (
                      <button
                        onClick={() => handleDownloadResume(selectedApplication.generatedResumeUrl || selectedApplication.resumeUrl, selectedApplication.applicantName)}
                        className="bg-blue-500/20 border border-blue-500/30 rounded-lg px-4 py-3 text-blue-400 hover:bg-blue-500/30 transition-all duration-300 flex items-center justify-center space-x-2"
                      >
                        <Download size={16} />
                        <span>Download Resume</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        )}
      </div>
      </div>
    </div>
  )
}

export default ApplicationReview
