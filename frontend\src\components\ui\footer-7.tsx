import React from "react";
import { Link } from "react-router-dom";
import { FaFacebook, FaInstagram, FaLinkedin, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaGithub } from "react-icons/fa";

interface Footer7Props {
  logo?: {
    url: string;
    src: string;
    alt: string;
    // title: string;
  };
  sections?: Array<{
    title: string;
    links: Array<{ name: string; href: string }>;
  }>;
  description?: string;
  socialLinks?: Array<{
    icon: React.ReactElement;
    href: string;
    label: string;
  }>;
  copyright?: string;
  legalLinks?: Array<{
    name: string;
    href: string;
  }>;
}

const defaultSections = [
  {
    title: "Services",
    links: [
      { name: "Web Development", href: "/web-development" },
      { name: "Mobile Apps", href: "/mobile-development" },
      { name: "UI/UX Design", href: "/ui-ux-design" },
      { name: "Performance Optimization", href: "/performance-optimization" },
      { name: "Experience Design", href: "/experience-design" },
      { name: "Digital Platforms", href: "/digital-experience-platforms" },
    ],
  },
  {
    title: "Company",
    links: [
      { name: "About Us", href: "/about" },
      { name: "Blog", href: "/blog" },
      { name: "Careers", href: "/jobs" },
    ],
  },
  {
    title: "Resources",
    links: [
      { name: "Get Quote", href: "/get-quote" },
      { name: "Support", href: "/contact" },
      { name: "Portfolio", href: "#" },
    ],
  },
];

const defaultSocialLinks = [
  { icon: <FaInstagram className="size-5" />, href: "#", label: "Instagram" },
  { icon: <FaFacebook className="size-5" />, href: "#", label: "Facebook" },
  { icon: <FaTwitter className="size-5" />, href: "#", label: "Twitter" },
  { icon: <FaLinkedin className="size-5" />, href: "#", label: "LinkedIn" },
  { icon: <FaGithub className="size-5" />, href: "#", label: "GitHub" },
];

const defaultLegalLinks = [
  { name: "Terms and Conditions", href: "/legal/terms" },
  { name: "Privacy Policy", href: "/legal/privacy" },
];

export const Footer7 = ({
  logo = {
    url: "/",
    src: "/logo.png",
    alt: "Delta Xero Creations Logo",
    // title: "Delta Xero Creations",
  },
  sections = defaultSections,
  description = "Affordable web and app services with cutting-edge technology. We bring your digital dreams to life with stunning animations and seamless user experiences.",
  socialLinks = defaultSocialLinks,
  copyright = "© 2025 Delta Xero Creations. All rights reserved.",
  legalLinks = defaultLegalLinks,
}: Footer7Props) => {
  return (
    <section className="py-20 px-10 md:pb-32 lg:pb-44 bg-dark-950 relative overflow-hidden">
      <div className="container mx-auto relative z-10">
        <div className="flex w-full flex-col justify-between gap-10 lg:flex-row lg:items-start lg:text-left">
          <div className="flex w-full flex-col justify-between gap-6 lg:items-start">
            {/* Logo */}
            <div className="flex items-center gap-2 lg:justify-start h-14 -ml-8">
              <Link to={logo.url}>
                <img
                  src={logo.src}
                  alt={logo.alt}
                  // title={logo.title}
                  className="h-24"
                />
              </Link>
              {/* <h2 className="text-xl font-semibold text-white">{logo.title}</h2> */}
            </div>
            <p className="max-w-[70%] text-sm text-gray-400">
              {description}
            </p>
            <ul className="flex items-center space-x-6 text-gray-400">
              {socialLinks.map((social, idx) => (
                <li key={idx} className="font-medium hover:text-primary-400 transition-colors duration-300">
                  <a href={social.href} aria-label={social.label}>
                    {social.icon}
                  </a>
                </li>
              ))}
            </ul>
          </div>
          <div className="grid w-full gap-6 md:grid-cols-3 lg:gap-20">
            {sections.map((section, sectionIdx) => (
              <div key={sectionIdx}>
                <h3 className="mb-4 font-bold text-white">{section.title}</h3>
                <ul className="space-y-3 text-sm text-gray-400">
                  {section.links.map((link, linkIdx) => (
                    <li
                      key={linkIdx}
                      className="font-medium hover:text-primary-400 transition-colors duration-300"
                    >
                      {link.href.startsWith('http') || link.href === '#' ? (
                        <a href={link.href} target={link.href.startsWith('http') ? '_blank' : '_self'} rel={link.href.startsWith('http') ? 'noopener noreferrer' : undefined}>
                          {link.name}
                        </a>
                      ) : (
                        <Link to={link.href}>{link.name}</Link>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
        <div className="mt-8 flex flex-col justify-between gap-4 border-t border-gray-700 py-8 text-xs font-medium text-gray-400 md:flex-row md:items-center md:text-left">
          <p className="order-2 lg:order-1">{copyright}</p>
          <ul className="order-1 flex flex-col gap-2 md:order-2 md:flex-row">
            {legalLinks.map((link, idx) => (
              <li key={idx} className="hover:text-primary-400 transition-colors duration-300">
                <Link to={link.href}> {link.name}</Link>
              </li>
            ))}
          </ul>
        </div>
      </div>
      
      {/* Large Delta Xero Creations Text - Auto-scaling to fit screen width */}
      <div className="absolute bottom-0 left-0 right-0 overflow-hidden pointer-events-none">
        <div
          className="font-black whitespace-nowrap transform translate-y-1/3 select-none bg-gradient-to-br from-primary-400/20 via-primary-500/15 to-primary-600/25 bg-clip-text text-transparent"
          style={{
            fontSize: 'clamp(4rem, 16vw, 20rem)',
            lineHeight: '1',
            width: '100%',
            textAlign: 'center'
          }}
        >
          DELTA XERO
        </div>
        {/* Additional gradient overlay for depth */}
        <div className="absolute inset-0 bg-gradient-to-t from-dark-950/60 via-transparent to-transparent"></div>
      </div>
    </section>
  );
};
