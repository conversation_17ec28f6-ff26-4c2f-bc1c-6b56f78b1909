import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";

const transition = {
  type: "spring",
  mass: 0.3,
  damping: 15,
  stiffness: 120,
  restDelta: 0.001,
  restSpeed: 0.001,
};

export const MenuItem = ({
  setActive,
  active,
  item,
  children,
}: {
  setActive: (item: string) => void;
  active: string | null;
  item: string;
  children?: React.ReactNode;
}) => {
  return (
    <div onMouseEnter={() => setActive(item)} className="relative">
      <motion.p
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className="cursor-pointer text-black hover:opacity-[0.9] dark:text-white font-medium px-4 py-2 rounded-lg hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-colors"
      >
        {item}
      </motion.p>
      {active !== null && (
        <motion.div
          initial={{ opacity: 0, scale: 0.96, y: 10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.96, y: 10 }}
          transition={{
            duration: 0.2,
            ease: "easeOut",
            layout: { duration: 0.3, ease: "easeOut" }
          }}
        >
          {active === item && (
            <div className="absolute top-[calc(100%_+_1.2rem)] left-1/2 transform -translate-x-1/2 pt-4">
              <motion.div
                layoutId="active-menu" // layoutId ensures smooth resizing animation
                transition={{
                  duration: 0.3,
                  ease: "easeOut",
                  layout: { duration: 0.3, ease: "easeOut" }
                }}
                className="bg-white/95 dark:bg-black/95 backdrop-blur-md rounded-2xl overflow-hidden border border-black/[0.1] dark:border-white/[0.1] shadow-2xl"
              >
                <motion.div
                  layout // layout ensures smooth resizing
                  transition={{ duration: 0.3, ease: "easeOut" }}
                  className="w-max h-full p-4"
                >
                  {children}
                </motion.div>
              </motion.div>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export const Menu = ({
  setActive,
  children,
}: {
  setActive: (item: string | null) => void;
  children: React.ReactNode;
}) => {
  return (
    <motion.nav
      onMouseLeave={() => setActive(null)} // resets the state
      className="relative w-full flex justify-center items-center space-x-4 px-8 py-6"
      layout
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      {children}
    </motion.nav>
  );
};

export const ProductItem = ({
  title,
  description,
  href,
  src,
}: {
  title: string;
  description: string;
  href: string;
  src: string;
}) => {
  return (
    <Link to={href} className="flex space-x-2">
      <img
        src={src}
        width={140}
        height={70}
        alt={title}
        className="flex-shrink-0 rounded-md shadow-2xl object-cover"
      />
      <div>
        <h4 className="text-xl font-bold mb-1 text-black dark:text-white">
          {title}
        </h4>
        <p className="text-neutral-700 text-sm max-w-[10rem] dark:text-neutral-300">
          {description}
        </p>
      </div>
    </Link>
  );
};

export const HoveredLink = ({ children, to, setActive, ...rest }: any) => {
  const handleClick = () => {
    if (setActive) {
      setActive(null); // Close dropdown when link is clicked
    }
  };

  return (
    <Link
      to={to}
      {...rest}
      onClick={handleClick}
      className="text-neutral-700 dark:text-neutral-200 hover:text-black "
    >
      {children}
    </Link>
  );
};
