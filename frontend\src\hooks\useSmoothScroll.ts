import { useEffect, useRef, useState } from 'react'
import Len<PERSON> from 'lenis'

let lenisInstance: Lenis | null = null

export const useSmoothScroll = () => {
  const rafRef = useRef<number>()
  const [isReady, setIsReady] = useState(false)

  useEffect(() => {
    // Small delay to ensure DOM is ready and prevent initial jitter
    const initTimeout = setTimeout(() => {
      // Initialize Lenis with optimized settings for smooth initial load
      lenisInstance = new Lenis({
        duration: 1.0, // Slightly faster for less jitter
        easing: (t: number) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
        direction: 'vertical',
        gestureDirection: 'vertical',
        smooth: true,
        mouseMultiplier: 0.8, // Reduced for smoother feel
        smoothTouch: false,
        touchMultiplier: 1.5, // Reduced for better control
        infinite: false,
        syncTouch: false,
        touchInertiaMultiplier: 25, // Reduced for less aggressive scrolling
        wheelMultiplier: 0.8, // Reduced for smoother wheel scrolling
        normalizeWheel: true,
        autoResize: true,
      })

      // Wait for <PERSON><PERSON> to be ready
      lenisInstance.on('scroll', () => {
        if (!isReady) {
          setIsReady(true)
        }
      })

      // Animation frame function
      function raf(time: number) {
        lenisInstance?.raf(time)
        rafRef.current = requestAnimationFrame(raf)
      }

      rafRef.current = requestAnimationFrame(raf)

      // Mark as ready after a short delay
      setTimeout(() => setIsReady(true), 100)
    }, 50)

    // Cleanup function
    return () => {
      clearTimeout(initTimeout)
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current)
      }
      if (lenisInstance) {
        lenisInstance.destroy()
        lenisInstance = null
      }
      setIsReady(false)
    }
  }, [])

  return { lenis: lenisInstance, isReady }
}

// Export lenis instance for external use
export const getSmoothScrollInstance = (): Lenis | null => lenisInstance

// GSAP ScrollTrigger integration
export const integrateLenisWithGSAP = () => {
  if (lenisInstance && typeof window !== 'undefined') {
    // Import GSAP dynamically to avoid SSR issues
    import('gsap/ScrollTrigger').then(({ ScrollTrigger }) => {
      // Update ScrollTrigger when Lenis scrolls
      lenisInstance!.on('scroll', ScrollTrigger.update)

      // Tell ScrollTrigger to use Lenis for scroll position
      ScrollTrigger.scrollerProxy(document.body, {
        scrollTop(value) {
          return arguments.length ? lenisInstance!.scrollTo(value, { immediate: true }) : lenisInstance!.scroll
        },
        getBoundingClientRect() {
          return { top: 0, left: 0, width: window.innerWidth, height: window.innerHeight }
        },
        pinType: document.body.style.transform ? 'transform' : 'fixed'
      })

      // Refresh ScrollTrigger after setup
      ScrollTrigger.refresh()
    })
  }
}

// Utility functions
export const scrollToTop = (immediate = false) => {
  if (lenisInstance) {
    lenisInstance.scrollTo(0, { immediate, duration: immediate ? 0 : 1.5 })
  } else {
    window.scrollTo({
      top: 0,
      behavior: immediate ? 'auto' : 'smooth'
    })
  }
}

export const scrollToElement = (target: string | Element, offset = -80) => {
  if (lenisInstance) {
    lenisInstance.scrollTo(target, { offset, duration: 1.2 })
  } else {
    if (typeof target === 'string') {
      const element = document.querySelector(target)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    } else if (target instanceof Element) {
      target.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }
}
