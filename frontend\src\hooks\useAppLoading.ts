import { useState, useEffect } from 'react';

interface UseAppLoadingOptions {
  simulateDbLoad?: boolean;
  simulateImageLoad?: boolean;
  minLoadTime?: number;
  maxLoadTime?: number;
}

export const useAppLoading = ({
  simulateDbLoad = true,
  simulateImageLoad = true,
  minLoadTime = 1000,
  maxLoadTime = 3000
}: UseAppLoadingOptions = {}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [loadingSteps, setLoadingSteps] = useState({
    database: simulateDbLoad,
    images: simulateImageLoad,
    fonts: true
  });

  useEffect(() => {
    const loadResources = async () => {
      const promises: Promise<void>[] = [];

      // Simulate database loading
      if (simulateDbLoad) {
        promises.push(
          new Promise((resolve) => {
            const dbLoadTime = Math.random() * (maxLoadTime - minLoadTime) + minLoadTime;
            setTimeout(() => {
              setLoadingSteps(prev => ({ ...prev, database: false }));
              resolve();
            }, dbLoadTime);
          })
        );
      }

      // Simulate image preloading
      if (simulateImageLoad) {
        promises.push(
          new Promise((resolve) => {
            const imageLoadTime = Math.random() * (maxLoadTime - minLoadTime) + minLoadTime;
            setTimeout(() => {
              setLoadingSteps(prev => ({ ...prev, images: false }));
              resolve();
            }, imageLoadTime);
          })
        );
      }

      // Load fonts (real check)
      promises.push(
        new Promise((resolve) => {
          if (document.fonts && document.fonts.ready) {
            document.fonts.ready.then(() => {
              setLoadingSteps(prev => ({ ...prev, fonts: false }));
              resolve();
            });
          } else {
            // Fallback for browsers without font loading API
            setTimeout(() => {
              setLoadingSteps(prev => ({ ...prev, fonts: false }));
              resolve();
            }, 500);
          }
        })
      );

      // Wait for all resources to load
      await Promise.all(promises);
      
      // Small delay to ensure smooth transition
      setTimeout(() => {
        setIsLoading(false);
      }, 200);
    };

    loadResources();
  }, [simulateDbLoad, simulateImageLoad, minLoadTime, maxLoadTime]);

  const isStillLoading = Object.values(loadingSteps).some(Boolean) || isLoading;

  return {
    isLoading: isStillLoading,
    loadingSteps,
    progress: {
      database: !loadingSteps.database,
      images: !loadingSteps.images,
      fonts: !loadingSteps.fonts
    }
  };
};
