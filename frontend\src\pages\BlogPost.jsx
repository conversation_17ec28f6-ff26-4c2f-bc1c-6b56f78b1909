import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link, useParams } from 'react-router-dom';

const BlogPost = () => {
  const { slug } = useParams();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // This would normally come from an API or database
  // For demo purposes, we're hardcoding a sample blog post
  const post = {
    title: 'How to Build Scalable Web Applications with Modern Frameworks',
    slug: 'scalable-web-applications-modern-frameworks',
    image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
    category: 'Web Development',
    date: 'April 19, 2025',
    readTime: 8,
    author: {
      name: 'Delta Xero Team',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      bio: 'Our expert development team at Delta Xero Creations shares insights on modern web development, best practices, and industry trends to help businesses succeed in the digital world.'
    },
    content: `
      <h2>Introduction</h2>
      <p>Building scalable web applications is crucial for businesses that want to grow and adapt to changing demands. At Delta Xero Creations, we've helped numerous clients build applications that can handle increased traffic, feature additions, and evolving business requirements without compromising performance.</p>

      <p>In this comprehensive guide, we'll explore the key principles, technologies, and best practices for creating web applications that scale effectively with your business growth.</p>

      <h2>Understanding Scalability</h2>
      <p>Scalability refers to an application's ability to handle increased workload efficiently. There are two main types of scalability:</p>

      <ul>
        <li><strong>Vertical Scaling (Scale Up):</strong> Adding more power to existing machines by increasing CPU, RAM, or storage.</li>
        <li><strong>Horizontal Scaling (Scale Out):</strong> Adding more machines to the pool of resources to handle increased load.</li>
      </ul>

      <h2>Modern Framework Choices</h2>

      <h3>React with Next.js</h3>
      <p>Next.js provides excellent scalability features including server-side rendering, static site generation, and automatic code splitting. These features ensure fast loading times and better SEO performance as your application grows.</p>

      <h3>Node.js Backend</h3>
      <p>Node.js excels in handling concurrent requests efficiently, making it ideal for scalable applications. Its non-blocking I/O operations and event-driven architecture allow for high-performance backend services.</p>

      <h3>Database Considerations</h3>
      <p>Choose between SQL and NoSQL databases based on your scalability needs:</p>
      <ul>
        <li><strong>PostgreSQL:</strong> Excellent for complex queries and ACID compliance</li>
        <li><strong>MongoDB:</strong> Great for flexible schemas and horizontal scaling</li>
        <li><strong>Redis:</strong> Perfect for caching and session management</li>
      </ul>

      <h2>Architecture Patterns for Scalability</h2>

      <h3>Microservices Architecture</h3>
      <p>Breaking your application into smaller, independent services allows for better scalability, maintainability, and deployment flexibility. Each service can be scaled independently based on demand.</p>

      <h3>API-First Design</h3>
      <p>Designing your application with APIs at the core enables better separation of concerns and allows different parts of your application to scale independently.</p>

      <h3>Caching Strategies</h3>
      <p>Implement multiple layers of caching:</p>
      <ul>
        <li>Browser caching for static assets</li>
        <li>CDN caching for global content delivery</li>
        <li>Application-level caching for frequently accessed data</li>
        <li>Database query caching</li>
      </ul>

      <h2>Performance Optimization Techniques</h2>

      <h3>Code Splitting and Lazy Loading</h3>
      <p>Load only the code that's needed for the current page or feature. This reduces initial bundle size and improves loading times.</p>

      <h3>Image Optimization</h3>
      <p>Use modern image formats like WebP, implement responsive images, and leverage CDNs for faster image delivery.</p>

      <h3>Database Optimization</h3>
      <p>Implement proper indexing, query optimization, and consider database sharding for large datasets.</p>

      <h2>Monitoring and Analytics</h2>
      <p>Implement comprehensive monitoring to understand your application's performance:</p>
      <ul>
        <li>Application Performance Monitoring (APM)</li>
        <li>Real User Monitoring (RUM)</li>
        <li>Error tracking and logging</li>
        <li>Infrastructure monitoring</li>
      </ul>

      <h2>Deployment and DevOps</h2>

      <h3>Containerization with Docker</h3>
      <p>Use Docker containers for consistent deployment across different environments and easier scaling.</p>

      <h3>Cloud Infrastructure</h3>
      <p>Leverage cloud platforms like AWS, Google Cloud, or Azure for auto-scaling capabilities and managed services.</p>

      <h3>CI/CD Pipelines</h3>
      <p>Implement continuous integration and deployment for faster, more reliable releases.</p>

      <h2>Conclusion</h2>
      <p>Building scalable web applications requires careful planning, the right technology choices, and ongoing optimization. At Delta Xero Creations, we specialize in creating applications that grow with your business while maintaining excellent performance and user experience.</p>

      <p>Remember that scalability is not just about handling more users—it's about building applications that can adapt to changing requirements, integrate new features seamlessly, and maintain performance under varying loads.</p>

      <p>If you're looking to build a scalable web application for your business, our team at Delta Xero Creations is here to help. We combine modern frameworks with proven architectural patterns to deliver solutions that scale with your success.</p>
    `,
    relatedPosts: [
      {
        id: 2,
        title: 'Why Affordable Web Development Doesn\'t Mean Compromising Quality',
        slug: 'affordable-web-development-quality',
        image: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
        category: 'Business'
      },
      {
        id: 5,
        title: 'Building Your First Professional Portfolio Website',
        slug: 'professional-portfolio-website-guide',
        image: 'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
        category: 'Web Development'
      }
    ]
  };

  return (
    <section className="py-32 bg-gradient-to-b from-dark-950 via-primary-900/10 to-dark-950">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Breadcrumbs */}
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center text-sm text-gray-400">
              <Link to="/" className="hover:text-primary-400 transition-colors">Home</Link>
              <span className="mx-2">/</span>
              <Link to="/blog" className="hover:text-primary-400 transition-colors">Blog</Link>
              <span className="mx-2">/</span>
              <span className="text-gray-300">{post.title}</span>
            </div>
          </motion.div>

          {/* Post Header */}
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-6">
              {post.title}
            </h1>

            <div className="flex items-center mb-6">
              <img
                src={post.author.avatar}
                alt={post.author.name}
                className="w-12 h-12 rounded-full mr-4"
              />
              <div>
                <div className="text-white font-medium">{post.author.name}</div>
                <div className="flex items-center text-sm text-gray-400">
                  <span>{post.date}</span>
                  <span className="mx-2">•</span>
                  <span>{post.readTime} min read</span>
                  <span className="mx-2">•</span>
                  <span className="text-primary-400">{post.category}</span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Featured Image */}
          <motion.div
            className="mb-10"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <img
              src={post.image}
              alt={post.title}
              className="w-full h-auto rounded-xl"
            />
          </motion.div>

          {/* Post Content */}
          <motion.div
            className="prose prose-invert prose-lg max-w-none mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            dangerouslySetInnerHTML={{ __html: post.content }}
          />

          {/* Author Bio */}
          <motion.div
            className="bg-dark-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="flex items-center">
              <img
                src={post.author.avatar}
                alt={post.author.name}
                className="w-16 h-16 rounded-full mr-4"
              />
              <div>
                <h3 className="text-xl font-bold text-white mb-2">About {post.author.name}</h3>
                <p className="text-gray-300">{post.author.bio}</p>
              </div>
            </div>
          </motion.div>

          {/* Related Posts */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <h2 className="text-2xl font-bold text-white mb-6">Related Articles</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {post.relatedPosts.map(relatedPost => (
                <Link key={relatedPost.id} to={`/blog/${relatedPost.slug}`} className="block group">
                  <div className="bg-dark-800/50 backdrop-blur-sm border border-gray-700 rounded-xl overflow-hidden hover:shadow-lg hover:shadow-primary-500/10 hover:border-primary-500/30 transition-all duration-300">
                    <div className="relative">
                      <img
                        src={relatedPost.image}
                        alt={relatedPost.title}
                        className="w-full h-40 object-cover transition-transform duration-500 group-hover:scale-105"
                      />
                      <div className="absolute top-4 right-4 bg-dark-900/80 backdrop-blur-sm text-white text-xs font-medium px-3 py-1 rounded-full">
                        {relatedPost.category}
                      </div>
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-bold text-white group-hover:text-primary-400 transition-colors">
                        {relatedPost.title}
                      </h3>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </motion.div>

          {/* Back to Blog */}
          <motion.div
            className="mt-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <Link
              to="/blog"
              className="inline-flex items-center text-primary-400 hover:text-primary-300 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              Back to All Articles
            </Link>
          </motion.div>

          {/* Newsletter CTA */}
          <motion.div
            className="mt-16 text-center bg-dark-800/30 backdrop-blur-sm border border-gray-700 rounded-2xl p-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <h3 className="text-2xl font-bold text-white mb-4">
              Stay Updated with <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">Delta Xero</span>
            </h3>
            <p className="text-gray-300 mb-6">
              Get the latest insights on web development, design trends, and business growth delivered to your inbox.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-4 py-3 bg-dark-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-primary-500 focus:outline-none"
              />
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg hover:shadow-primary-500/25 transition-all duration-300"
              >
                Subscribe
              </motion.button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default BlogPost;
