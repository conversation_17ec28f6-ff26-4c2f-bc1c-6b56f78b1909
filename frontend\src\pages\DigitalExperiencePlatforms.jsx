import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Link } from 'react-router-dom'
import { 
  Code, 
  Globe, 
  Zap, 
  Shield, 
  ArrowRight, 
  CheckCircle,
  Server,
  Database,
  Cloud,
  Layers,
  Rocket,
  TrendingUp
} from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const DigitalExperiencePlatforms = () => {
  const statsRef = useRef(null)
  const heroRef = useRef(null)

  useEffect(() => {
    // Hero animation
    gsap.fromTo(heroRef.current?.children,
      { y: 100, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1.2,
        stagger: 0.2,
        ease: 'power3.out'
      }
    )

    // Stats counter animation
    gsap.fromTo('.stat-counter',
      { textContent: 0 },
      {
        textContent: (i, target) => target.getAttribute('data-value'),
        duration: 2,
        ease: 'power2.out',
        snap: { textContent: 1 },
        scrollTrigger: {
          trigger: statsRef.current,
          start: 'top 80%',
        }
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const technologies = [
    { name: 'React/Next.js 14', icon: Code, description: 'Latest React with App Router' },
    { name: 'TypeScript', icon: Layers, description: 'Type-safe development' },
    { name: 'Microservices', icon: Server, description: 'Scalable architecture' },
    { name: 'AI Integration', icon: Cloud, description: 'GPT-4, Claude, Gemini APIs' }
  ]

  const features = [
    {
      icon: Globe,
      title: 'Omnichannel Experiences',
      description: 'Seamless user journeys across web, mobile, and emerging platforms with consistent branding.',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Zap,
      title: 'Core Web Vitals Optimized',
      description: 'Google-certified performance with sub-second loading and 99.9% uptime guarantee.',
      color: 'from-yellow-500 to-orange-500'
    },
    {
      icon: Shield,
      title: 'Zero-Trust Security',
      description: 'Enterprise-grade security with SOC 2 compliance, GDPR readiness, and advanced threat protection.',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Database,
      title: 'AI-Powered Personalization',
      description: 'Machine learning algorithms that adapt content and experiences to individual user behavior.',
      color: 'from-purple-500 to-pink-500'
    }
  ]

  const stats = [
    { number: 200, label: 'Enterprise Platforms Built', suffix: '+' },
    { number: 98, label: 'Client Satisfaction Rate', suffix: '%' },
    { number: 75, label: 'Average Performance Boost', suffix: '%' },
    { number: 99.9, label: 'Uptime Guarantee', suffix: '%' }
  ]

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="-mt-16 lg:-mt-20"
    >
      {/* Hero Section */}
      <section className="relative pt-24 pb-20 min-h-screen flex items-center justify-center bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-primary-500/5 to-blue-500/5 rounded-full blur-3xl"></div>
        </div>

        {/* Animated Grid Background */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.3) 1px, transparent 0)`,
            backgroundSize: '50px 50px'
          }}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div ref={heroRef}>
            {/* Badge */}
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Code className="text-blue-400 mr-3" size={16} />
              <span className="text-blue-400 text-sm font-semibold uppercase tracking-wider">Digital Experience Platforms</span>
            </motion.div>

            {/* Main Heading */}
            <h1 className="text-5xl md:text-7xl font-black text-white mb-8 leading-tight">
              BUILD POWERFUL
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-cyan-400 to-teal-400 bg-clip-text text-transparent">
                WEB EXPERIENCES
              </span>
            </h1>

            {/* Subheading */}
            <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
              Transform your business with enterprise-grade digital experience platforms that drive growth and engagement.
              Built with modern frameworks, cloud-native architecture, and AI-powered features for 2024 and beyond.
            </p>

            {/* CTA Button */}
            <Link to="/get-quote">
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-12 py-5 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-300 flex items-center space-x-3 mx-auto relative overflow-hidden group"
              >
                <span className="relative z-10">Get Quote</span>
                <ArrowRight className="relative z-10" size={20} />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
              </motion.button>
            </Link>

            {/* Key Features */}
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-4xl mx-auto mt-16"
            >
              {technologies.map((tech, index) => (
                <div key={index} className="flex flex-col items-center space-y-3 text-gray-300">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-2xl flex items-center justify-center">
                    <tech.icon className="text-blue-400" size={24} />
                  </div>
                  <span className="font-medium text-center">{tech.name}</span>
                  <span className="text-sm text-gray-400 text-center">{tech.description}</span>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section ref={statsRef} className="py-20 bg-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-4xl md:text-5xl font-black text-white mb-2">
                  <span className="stat-counter" data-value={stat.number}>0</span>
                  <span className="text-blue-400">{stat.suffix}</span>
                </div>
                <div className="text-gray-400 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Rocket className="text-blue-400 mr-3" size={16} />
              <span className="text-blue-400 text-sm font-semibold uppercase tracking-wider">Platform Features</span>
            </motion.div>
            <h2 className="text-5xl md:text-6xl font-black text-white mb-8 leading-tight">
              ENTERPRISE-GRADE
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-cyan-400 to-teal-400 bg-clip-text text-transparent">
                WEB SOLUTIONS
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
              Built with modern technologies and best practices for maximum performance and scalability
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="h-full bg-gradient-to-br from-black/60 to-black/30 backdrop-blur-xl border border-white/20 rounded-3xl p-8 group-hover:border-white/30 transition-all duration-500 hover:transform hover:scale-[1.02] hover:-translate-y-3 relative">
                  {/* Icon */}
                  <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="text-white" size={28} />
                  </div>

                  {/* Content */}
                  <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-400 leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Hover Glow */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl`}></div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Code className="text-blue-400 mr-3" size={16} />
              <span className="text-blue-400 text-sm font-semibold uppercase tracking-wider">Ready to Start?</span>
            </motion.div>

            <h2 className="text-5xl md:text-6xl font-black text-white mb-8 leading-tight">
              BUILD YOUR
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-cyan-400 to-teal-400 bg-clip-text text-transparent">
                DIGITAL PLATFORM
              </span>
            </h2>

            <p className="text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed">
              Transform your business with a powerful web platform that scales with your growth
            </p>



            {/* Trust Indicators */}
            <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
              {[
                { icon: Shield, text: 'Secure & Compliant', color: 'text-green-400' },
                { icon: Zap, text: 'Lightning Fast', color: 'text-yellow-400' },
                { icon: TrendingUp, text: 'Scalable Growth', color: 'text-blue-400' },
                { icon: CheckCircle, text: 'Quality Assured', color: 'text-emerald-400' }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex flex-col items-center space-y-3"
                >
                  <div className="w-12 h-12 bg-white/10 rounded-2xl flex items-center justify-center">
                    <item.icon className={item.color} size={24} />
                  </div>
                  <span className="text-gray-300 text-sm font-medium">{item.text}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    </motion.div>
  )
}

export default DigitalExperiencePlatforms
