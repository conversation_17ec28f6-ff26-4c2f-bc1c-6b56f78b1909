# Delta Xero Creations - Codebase Documentation

## Project Overview
Delta Xero Creations is a modern web development company website built with React, TypeScript, and Vite. The project features advanced animations, a comprehensive admin panel, and job/internship application systems.

## Technology Stack
- **Frontend Framework**: React 19.1.0 with TypeScript
- **Build Tool**: Vite 7.0.4
- **Styling**: Tailwind CSS 3.4.17
- **Animations**: Framer Motion 12.23.6, GSAP 3.13.0
- **Routing**: React Router DOM 7.6.3
- **Form Handling**: React Hook Form 7.60.0 with Yup validation
- **Icons**: Lucide React 0.525.0, React Icons 5.5.0
- **3D Graphics**: Spline React 4.1.0, Cobe 0.6.4

## Project Structure

### Root Directory
```
frontend/
├── src/
│   ├── components/          # Reusable UI components
│   ├── pages/              # Page components
│   ├── hooks/              # Custom React hooks
│   ├── lib/                # Utility libraries
│   ├── data/               # Static data and configurations
│   ├── assets/             # Images, icons, and static assets
│   ├── styles/             # CSS and styling files
│   └── utils/              # Utility functions
├── public/                 # Public assets
└── package.json           # Dependencies and scripts
```

## Routing Structure

### Main Website Routes (with ModernLayout)
- `/` - Home page
- `/about` - About page
- `/services` - Services overview
- `/jobs` - Jobs listing
- `/blog` - Blog listing
- `/blog/:slug` - Individual blog posts
- `/contact` - Contact page
- `/quote`, `/get-quote` - Quote request page

### Service-Specific Pages
- `/web-development` - Web development services
- `/mobile-development` - Mobile development services
- `/ui-ux-design` - UI/UX design services
- `/api-development` - API development services
- `/brand-strategy` - Brand strategy services
- `/digital-transformation` - Digital transformation services

### Advanced Service Pages
- `/digital-experience-platforms` - Digital experience platforms
- `/mobile-engineering` - Mobile engineering services
- `/experience-design` - Experience design services
- `/performance-optimization` - Performance optimization services

### Industry-Specific Pages
- `/ecommerce` - E-commerce solutions
- `/saas` - SaaS solutions
- `/healthcare` - Healthcare solutions
- `/fintech` - Fintech solutions
- `/education` - Education solutions
- `/banking` - Banking solutions

### Application Forms
- `/apply/job`, `/careers/apply` - Job application form
- `/apply/internship`, `/internships/apply` - Internship application form

### Admin Panel Routes (without main navbar)
- `/admin`, `/admin/dashboard` - Admin dashboard
- `/admin/employees` - Employee management
- `/admin/interns` - Intern management
- `/admin/applications` - Application review
- `/admin/applications/:id` - Individual application details
- `/admin/users` - User management

## Key Components

### Layout Components
1. **ModernLayout.tsx** - Main layout wrapper with navbar and footer
2. **ModernNavbar.tsx** - Advanced navbar with animations and dropdowns
3. **AdminNavigation.jsx** - Admin panel navigation
4. **DeltaXeroFooter.tsx** - Company footer component
5. **ScrollToTop.tsx** - Scroll to top functionality

### UI Components
Located in `src/components/ui/`:
- **navbar-menu** - Advanced navbar menu system
- **footer-7.tsx** - Footer component with social links

### Business Components
- **PortfolioInfiniteMenu.tsx** - Portfolio showcase with infinite menu
- **PortfolioSection.tsx** - Portfolio section component
- **ServicesSection.tsx** - Services overview section
- **TestimonialsSection.tsx** - Client testimonials
- **DisplayCardsDemo.tsx** - Card display component

## Form Systems

### Job Application Form (`JobApplication.jsx`)
**Multi-step form with 5 steps:**
1. Personal Information
2. Position Details
3. Experience & Education
4. Documents & Links
5. Review & Submit

**Data Structure:**
```javascript
{
  // Personal Information
  firstName, lastName, email, phone, address, city, state, zipCode, country,
  
  // Position Information
  position, department, expectedSalary, availableStartDate, employmentType,
  
  // Experience
  experience, currentCompany, currentPosition, noticePeriod,
  
  // Education
  education, university, graduationYear, gpa,
  
  // Additional Information
  portfolioUrl, linkedinUrl, githubUrl, coverLetter,
  
  // Files
  resume, portfolio,
  
  // Legal
  workAuthorization, backgroundCheck, drugTest,
  
  // References
  references: [{ name, company, position, email, phone }]
}
```

### Internship Application Form (`InternshipApplication.jsx`)
**Multi-step form with 5 steps:**
1. Personal Info
2. Academic Details
3. Internship Info
4. Documents
5. Review & Submit

**Data Structure:**
```javascript
{
  // Personal Information
  firstName, lastName, email, phone, address, city, state, zipCode, country, dateOfBirth,
  
  // Academic Information
  university, major, year, gpa, graduationDate,
  
  // Internship Details
  position, department, duration, startDate, internshipType,
  
  // Experience & Skills
  previousInternships, relevantCourses, skills, projects,
  
  // Additional Information
  portfolioUrl, linkedinUrl, githubUrl, coverLetter,
  
  // Files
  resume, transcript,
  
  // Availability
  hoursPerWeek, workDays: [],
  
  // References
  references: [{ name, title, email, phone, relationship }],
  
  // Legal
  workAuthorization, backgroundCheck, agreement
}
```

## Admin Panel System

### Admin Dashboard (`AdminDashboard.jsx`)
- **Statistics Overview**: Total employees, interns, applications, pending reviews
- **Recent Activities**: Real-time activity feed
- **Quick Actions**: Add employee, add intern, review applications, post job

### Application Management
- **ApplicationReview.jsx** - List and filter applications
- **ApplicationDetail.jsx** - Detailed view of individual applications
- Status management: Pending, Under Review, Approved, Rejected

### Data Management
- **EmployeeManagement** - Manage company employees
- **InternManagement** - Manage interns
- **UserManagement** - System user management

## Current Data Handling
- **Mock Data**: Currently using static mock data for demonstrations
- **Form Submissions**: Simulated API calls with setTimeout
- **No Database Integration**: No current backend or database connection

## Assets and Branding
- **Logo**: Hosted on Supabase storage
- **Color Scheme**: Dark theme with blue accents
- **Animations**: Extensive use of Framer Motion and GSAP
- **Responsive Design**: Mobile-first approach with Tailwind CSS

## Development Configuration
- **TypeScript**: Strict mode enabled with path mapping
- **ESLint**: Configured for React and TypeScript
- **Vite**: Fast development server and build tool
- **PostCSS**: For Tailwind CSS processing

## Next Steps Required
1. **Database Integration**: Implement Supabase for data persistence
2. **Form Enhancement**: Add proper validation and file upload handling
3. **API Integration**: Connect forms to backend services
4. **Authentication**: Implement user authentication system
5. **Real-time Updates**: Add real-time functionality for admin panel
