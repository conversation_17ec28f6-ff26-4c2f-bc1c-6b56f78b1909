import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface QuoteFeature {
  icon: React.ComponentType<any>
  title: string
  description: string
  color: string
  highlight?: string
  stats?: string
}

interface QuoteBentoGridProps {
  features: QuoteFeature[]
  className?: string
}

const QuoteBentoGrid: React.FC<QuoteBentoGridProps> = ({ features, className }) => {
  // Define layout for quote page (6 features) - Optimized for conversion
  const getGridLayout = () => {
    return [
      { size: 'hero', position: 'col-span-1 md:col-span-2 lg:col-span-2 row-span-2' },
      { size: 'medium', position: 'col-span-1 md:col-span-1 lg:col-span-1 row-span-1' },
      { size: 'medium', position: 'col-span-1 md:col-span-1 lg:col-span-1 row-span-1' },
      { size: 'wide', position: 'col-span-1 md:col-span-2 lg:col-span-2 row-span-1' },
      { size: 'medium', position: 'col-span-1 md:col-span-1 lg:col-span-1 row-span-1' },
      { size: 'medium', position: 'col-span-1 md:col-span-1 lg:col-span-1 row-span-1' }
    ]
  }

  const layout = getGridLayout()

  return (
    <div className={cn(
      "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 auto-rows-[180px] max-w-7xl mx-auto",
      className
    )}>
      {features.map((feature, index) => {
        const Icon = feature.icon
        const layoutConfig = layout[index] || { size: 'medium', position: 'col-span-1 row-span-1' }
        
        return (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 40, scale: 0.9 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ 
              duration: 0.8, 
              delay: index * 0.1,
              type: "spring",
              stiffness: 80
            }}
            viewport={{ once: true }}
            className={cn(
              layoutConfig.position,
              "group relative overflow-hidden rounded-2xl border border-white/10 bg-gradient-to-br from-dark-800/50 to-dark-900/50 backdrop-blur-xl hover:border-white/20 transition-all duration-700 cursor-pointer hover:scale-[1.02]"
            )}
          >
            {/* Background Gradient */}
            <div className={cn(
              "absolute inset-0 bg-gradient-to-br opacity-10 group-hover:opacity-20 transition-opacity duration-700",
              feature.color
            )} />
            
            {/* Animated Background Pattern */}
            <div className="absolute inset-0 opacity-5 group-hover:opacity-15 transition-opacity duration-700">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.3) 1px, transparent 0)`,
                backgroundSize: '24px 24px'
              }}></div>
            </div>

            {/* Content */}
            <div className={cn(
              "relative z-10 h-full flex flex-col justify-between",
              layoutConfig.size === 'hero' ? 'p-8' : layoutConfig.size === 'wide' ? 'p-6' : 'p-5'
            )}>
              {/* Header */}
              <div className="flex-1">
                <div className={cn(
                  "rounded-2xl bg-gradient-to-br flex items-center justify-center group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-xl mb-4",
                  feature.color,
                  layoutConfig.size === 'hero' ? 'w-16 h-16 rounded-3xl' : 'w-12 h-12'
                )}>
                  <Icon className={cn(
                    "text-white",
                    layoutConfig.size === 'hero' ? 'w-8 h-8' : 'w-6 h-6'
                  )} />
                </div>
                
                {/* Title */}
                <h3 className={cn(
                  "font-bold group-hover:text-white transition-colors duration-500 leading-tight mb-2",
                  layoutConfig.size === 'hero' ? 'text-xl md:text-2xl text-white' : 
                  layoutConfig.size === 'wide' ? 'text-lg md:text-xl text-white' : 'text-md md:text-lg text-white'
                )}>
                  {feature.title}
                </h3>

                {/* Stats/Highlight */}
                {feature.stats && (
                  <div className={cn(
                    "font-bold bg-gradient-to-r bg-clip-text text-transparent mb-2",
                    feature.color,
                    layoutConfig.size === 'hero' ? 'text-lg' : 'text-sm'
                  )}>
                    {feature.stats}
                  </div>
                )}

                {/* Description */}
                <p className={cn(
                  "text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-500",
                  layoutConfig.size === 'hero' ? 'text-sm' : 'text-xs'
                )}
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: layoutConfig.size === 'hero' ? 4 : layoutConfig.size === 'wide' ? 2 : 3,
                  WebkitBoxOrient: 'vertical' as const,
                  overflow: 'hidden'
                }}>
                  {feature.description}
                </p>
              </div>

              {/* Highlight Badge */}
              {feature.highlight && (
                <div className="mt-auto">
                  <div className={cn(
                    "inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r text-white text-xs font-bold shadow-lg",
                    feature.color
                  )}>
                    <span>{feature.highlight}</span>
                  </div>
                </div>
              )}
            </div>

            {/* Hover Effect Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700" />
            
            {/* Border Glow Effect */}
            <div className={cn(
              "absolute inset-0 rounded-2xl bg-gradient-to-br opacity-0 group-hover:opacity-40 transition-opacity duration-700 blur-xl -z-10",
              feature.color
            )} />
          </motion.div>
        )
      })}
    </div>
  )
}

export default QuoteBentoGrid
