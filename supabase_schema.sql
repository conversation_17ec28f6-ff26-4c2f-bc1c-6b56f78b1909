-- Delta Xero Creations - Supabase Database Schema
-- This file contains all the SQL commands to create the database structure for the application

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE application_status AS ENUM ('draft', 'submitted', 'under_review', 'approved', 'rejected', 'withdrawn');
CREATE TYPE employment_type AS ENUM ('full-time', 'part-time', 'contract', 'freelance');
CREATE TYPE internship_type AS ENUM ('paid', 'unpaid', 'academic_credit');
CREATE TYPE work_location AS ENUM ('remote', 'on-site', 'hybrid');
CREATE TYPE user_role AS ENUM ('admin', 'hr', 'manager', 'employee', 'intern');

-- Users table for authentication and user management
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    first_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    role user_role DEFAULT 'employee',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE
);

-- Job applications table
CREATE TABLE job_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    application_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- Personal Information
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    zip_code VARCHAR(20),
    country VARCHAR(100),
    date_of_birth DATE,
    
    -- Position Information
    position VARCHAR(200) NOT NULL,
    department VARCHAR(100) NOT NULL,
    expected_salary VARCHAR(100), -- Optional field, not collected in current form
    available_start_date DATE,
    employment_type employment_type DEFAULT 'full-time',
    work_location work_location DEFAULT 'remote',
    
    -- Experience
    experience_level VARCHAR(100),
    current_company VARCHAR(200),
    current_position VARCHAR(200),
    notice_period VARCHAR(100),
    total_experience VARCHAR(100),
    relevant_experience VARCHAR(100),
    
    -- Education
    education_level VARCHAR(100),
    university VARCHAR(200),
    graduation_year VARCHAR(10),
    gpa VARCHAR(10),
    degree VARCHAR(200),
    field_of_study VARCHAR(200),
    
    -- Skills & Certifications
    technical_skills TEXT,
    soft_skills TEXT,
    certifications TEXT,
    languages TEXT,
    
    -- Additional Information
    portfolio_url VARCHAR(500),
    linkedin_url VARCHAR(500),
    github_url VARCHAR(500),
    website_url VARCHAR(500),
    cover_letter TEXT,
    
    -- Files (stored as URLs to Supabase storage)
    resume_url VARCHAR(500),
    portfolio_file_url VARCHAR(500),
    cover_letter_file_url VARCHAR(500),
    
    -- Legal & Compliance
    work_authorization VARCHAR(100),
    background_check_consent BOOLEAN DEFAULT false,
    drug_test_consent BOOLEAN DEFAULT false,
    visa_sponsorship_required BOOLEAN DEFAULT false,
    
    -- Application Metadata
    status application_status DEFAULT 'draft',
    submitted_at TIMESTAMP WITH TIME ZONE,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES users(id),
    notes TEXT,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Internship applications table
CREATE TABLE internship_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    application_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- Personal Information
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    zip_code VARCHAR(20),
    country VARCHAR(100),
    date_of_birth DATE,
    
    -- Academic Information
    university VARCHAR(200) NOT NULL,
    major VARCHAR(200) NOT NULL,
    year_level VARCHAR(50) NOT NULL,
    gpa VARCHAR(10),
    graduation_date DATE,
    
    -- Internship Details
    position VARCHAR(200) NOT NULL,
    department VARCHAR(100) NOT NULL,
    duration VARCHAR(100),
    start_date DATE,
    internship_type internship_type DEFAULT 'unpaid',
    hours_per_week INTEGER,
    work_days TEXT[], -- Array of days like ['Monday', 'Tuesday']
    
    -- Experience & Skills
    previous_internships TEXT,
    relevant_courses TEXT,
    skills TEXT,
    projects TEXT,
    
    -- Additional Information
    portfolio_url VARCHAR(500),
    linkedin_url VARCHAR(500),
    github_url VARCHAR(500),
    cover_letter TEXT,
    
    -- Files (stored as URLs to Supabase storage)
    resume_url VARCHAR(500),
    transcript_url VARCHAR(500),
    
    -- Legal
    work_authorization VARCHAR(100),
    background_check_consent BOOLEAN DEFAULT false,
    agreement_consent BOOLEAN DEFAULT false,
    
    -- Application Metadata
    status application_status DEFAULT 'draft',
    submitted_at TIMESTAMP WITH TIME ZONE,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES users(id),
    notes TEXT,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- References table for both job and internship applications
CREATE TABLE application_references (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_application_id UUID REFERENCES job_applications(id) ON DELETE CASCADE,
    internship_application_id UUID REFERENCES internship_applications(id) ON DELETE CASCADE,

    name VARCHAR(200) NOT NULL,
    title VARCHAR(200),
    company VARCHAR(200),
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    relationship VARCHAR(100),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure reference belongs to either job or internship application, not both
    CONSTRAINT check_application_type CHECK (
        (job_application_id IS NOT NULL AND internship_application_id IS NULL) OR
        (job_application_id IS NULL AND internship_application_id IS NOT NULL)
    )
);

-- Quote requests table
CREATE TABLE quote_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    request_id VARCHAR(50) UNIQUE NOT NULL,

    -- Contact Information
    name VARCHAR(200) NOT NULL,
    email VARCHAR(255) NOT NULL,
    company VARCHAR(200),
    phone VARCHAR(20),

    -- Project Information
    message TEXT NOT NULL,
    services TEXT[] NOT NULL, -- Array of service IDs
    project_type VARCHAR(100),
    budget_range VARCHAR(100),
    timeline VARCHAR(100),

    -- Additional Details
    website_url VARCHAR(500),
    reference_urls TEXT[],
    special_requirements TEXT,

    -- Status and Management
    status VARCHAR(50) DEFAULT 'new', -- new, reviewing, quoted, accepted, rejected, completed
    priority VARCHAR(20) DEFAULT 'normal', -- low, normal, high, urgent
    assigned_to UUID REFERENCES users(id),
    estimated_cost DECIMAL(10,2),
    quoted_cost DECIMAL(10,2),
    quote_notes TEXT,

    -- Response tracking
    responded_at TIMESTAMP WITH TIME ZONE,
    quote_sent_at TIMESTAMP WITH TIME ZONE,
    follow_up_date DATE,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Employees table
CREATE TABLE employees (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    employee_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- Personal Information
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    zip_code VARCHAR(20),
    country VARCHAR(100),
    date_of_birth DATE,
    
    -- Employment Information
    position VARCHAR(200) NOT NULL,
    department VARCHAR(100) NOT NULL,
    employment_type employment_type DEFAULT 'full-time',
    work_location work_location DEFAULT 'remote',
    salary DECIMAL(12, 2),
    start_date DATE NOT NULL,
    end_date DATE,
    manager_id UUID REFERENCES employees(id),
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Interns table
CREATE TABLE interns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    intern_id VARCHAR(50) UNIQUE NOT NULL,
    
    -- Personal Information
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    university VARCHAR(200) NOT NULL,
    major VARCHAR(200) NOT NULL,
    year_level VARCHAR(50) NOT NULL,
    
    -- Internship Information
    position VARCHAR(200) NOT NULL,
    department VARCHAR(100) NOT NULL,
    internship_type internship_type DEFAULT 'unpaid',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    hours_per_week INTEGER,
    supervisor_id UUID REFERENCES employees(id),
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Application status history for tracking changes
CREATE TABLE application_status_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_application_id UUID REFERENCES job_applications(id) ON DELETE CASCADE,
    internship_application_id UUID REFERENCES internship_applications(id) ON DELETE CASCADE,
    
    old_status application_status,
    new_status application_status NOT NULL,
    changed_by UUID REFERENCES users(id),
    notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure history belongs to either job or internship application, not both
    CONSTRAINT check_history_application_type CHECK (
        (job_application_id IS NOT NULL AND internship_application_id IS NULL) OR
        (job_application_id IS NULL AND internship_application_id IS NOT NULL)
    )
);

-- Create indexes for better performance
CREATE INDEX idx_job_applications_email ON job_applications(email);
CREATE INDEX idx_job_applications_status ON job_applications(status);
CREATE INDEX idx_job_applications_position ON job_applications(position);
CREATE INDEX idx_job_applications_submitted_at ON job_applications(submitted_at);

CREATE INDEX idx_internship_applications_email ON internship_applications(email);
CREATE INDEX idx_internship_applications_status ON internship_applications(status);
CREATE INDEX idx_internship_applications_position ON internship_applications(position);
CREATE INDEX idx_internship_applications_submitted_at ON internship_applications(submitted_at);

CREATE INDEX idx_employees_email ON employees(email);
CREATE INDEX idx_employees_department ON employees(department);
CREATE INDEX idx_employees_is_active ON employees(is_active);

CREATE INDEX idx_interns_email ON interns(email);
CREATE INDEX idx_interns_department ON interns(department);
CREATE INDEX idx_interns_is_active ON interns(is_active);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_job_applications_updated_at BEFORE UPDATE ON job_applications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_internship_applications_updated_at BEFORE UPDATE ON internship_applications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_employees_updated_at BEFORE UPDATE ON employees FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_interns_updated_at BEFORE UPDATE ON interns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE internship_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE application_references ENABLE ROW LEVEL SECURITY;
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE interns ENABLE ROW LEVEL SECURITY;
ALTER TABLE application_status_history ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

-- Job applications policies
CREATE POLICY "Anyone can insert job applications" ON job_applications FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can view own job applications" ON job_applications FOR SELECT USING (
    email = auth.email() OR auth.uid() IS NULL
);
CREATE POLICY "Admins can view all job applications" ON job_applications FOR SELECT USING (
    EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'hr'))
);
CREATE POLICY "Admins can update job applications" ON job_applications FOR UPDATE USING (
    EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'hr'))
);

-- Internship applications policies
CREATE POLICY "Anyone can insert internship applications" ON internship_applications FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can view own internship applications" ON internship_applications FOR SELECT USING (
    email = auth.email() OR auth.uid() IS NULL
);
CREATE POLICY "Admins can view all internship applications" ON internship_applications FOR SELECT USING (
    EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'hr'))
);
CREATE POLICY "Admins can update internship applications" ON internship_applications FOR UPDATE USING (
    EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'hr'))
);

-- References policies
CREATE POLICY "Admins can manage references" ON application_references FOR ALL USING (
    EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'hr'))
);

-- Employee and intern policies (admin only)
CREATE POLICY "Admins can manage employees" ON employees FOR ALL USING (
    EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'hr'))
);
CREATE POLICY "Admins can manage interns" ON interns FOR ALL USING (
    EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'hr'))
);

-- Status history policies
CREATE POLICY "Admins can manage status history" ON application_status_history FOR ALL USING (
    EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'hr'))
);

-- Create storage buckets for file uploads
INSERT INTO storage.buckets (id, name, public) VALUES
('resumes', 'resumes', false),
('portfolios', 'portfolios', false),
('transcripts', 'transcripts', false),
('cover-letters', 'cover-letters', false);

-- Storage policies
CREATE POLICY "Anyone can upload resumes" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'resumes');
CREATE POLICY "Anyone can upload portfolios" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'portfolios');
CREATE POLICY "Anyone can upload transcripts" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'transcripts');
CREATE POLICY "Anyone can upload cover letters" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'cover-letters');

CREATE POLICY "Admins can view all files" ON storage.objects FOR SELECT USING (
    EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'hr'))
);

-- Functions for application management
CREATE OR REPLACE FUNCTION get_application_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_job_applications', (SELECT COUNT(*) FROM job_applications),
        'total_internship_applications', (SELECT COUNT(*) FROM internship_applications),
        'pending_job_applications', (SELECT COUNT(*) FROM job_applications WHERE status = 'submitted'),
        'pending_internship_applications', (SELECT COUNT(*) FROM internship_applications WHERE status = 'submitted'),
        'total_employees', (SELECT COUNT(*) FROM employees WHERE is_active = true),
        'total_interns', (SELECT COUNT(*) FROM interns WHERE is_active = true),
        'applications_this_month', (
            SELECT COUNT(*) FROM (
                SELECT submitted_at FROM job_applications WHERE submitted_at >= date_trunc('month', CURRENT_DATE)
                UNION ALL
                SELECT submitted_at FROM internship_applications WHERE submitted_at >= date_trunc('month', CURRENT_DATE)
            ) AS monthly_apps
        )
    ) INTO result;

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update application status with history tracking
CREATE OR REPLACE FUNCTION update_application_status(
    app_type TEXT,
    app_id UUID,
    new_status application_status,
    notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    old_status application_status;
    current_user_id UUID;
BEGIN
    -- Get current user
    current_user_id := auth.uid();

    IF app_type = 'job' THEN
        -- Get current status
        SELECT status INTO old_status FROM job_applications WHERE id = app_id;

        -- Update application
        UPDATE job_applications
        SET status = new_status,
            reviewed_at = CASE WHEN new_status != 'submitted' THEN NOW() ELSE reviewed_at END,
            reviewed_by = CASE WHEN new_status != 'submitted' THEN current_user_id ELSE reviewed_by END,
            notes = COALESCE(update_application_status.notes, job_applications.notes)
        WHERE id = app_id;

        -- Insert history record
        INSERT INTO application_status_history (job_application_id, old_status, new_status, changed_by, notes)
        VALUES (app_id, old_status, new_status, current_user_id, update_application_status.notes);

    ELSIF app_type = 'internship' THEN
        -- Get current status
        SELECT status INTO old_status FROM internship_applications WHERE id = app_id;

        -- Update application
        UPDATE internship_applications
        SET status = new_status,
            reviewed_at = CASE WHEN new_status != 'submitted' THEN NOW() ELSE reviewed_at END,
            reviewed_by = CASE WHEN new_status != 'submitted' THEN current_user_id ELSE reviewed_by END,
            notes = COALESCE(update_application_status.notes, internship_applications.notes)
        WHERE id = app_id;

        -- Insert history record
        INSERT INTO application_status_history (internship_application_id, old_status, new_status, changed_by, notes)
        VALUES (app_id, old_status, new_status, current_user_id, update_application_status.notes);
    ELSE
        RETURN FALSE;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
