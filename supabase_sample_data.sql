-- Sample Data for Delta Xero Creations Database
-- This file contains sample data to populate the database for testing and development

-- Insert sample admin users
INSERT INTO users (email, first_name, last_name, role, is_active) VALUES
('<EMAIL>', 'Admin', 'User', 'admin', true),
('<EMAIL>', 'HR', 'Manager', 'hr', true),
('<EMAIL>', 'Project', 'Manager', 'manager', true);

-- Insert sample employees
INSERT INTO employees (employee_id, first_name, last_name, email, phone, position, department, employment_type, work_location, salary, start_date, is_active) VALUES
('EMP001', '<PERSON>', '<PERSON>', '<EMAIL>', '******-0101', 'Senior Frontend Developer', 'Engineering', 'full-time', 'remote', 95000.00, '2023-01-15', true),
('EMP002', '<PERSON>', '<PERSON>', '<EMAIL>', '******-0102', 'UI/UX Designer', 'Design', 'full-time', 'hybrid', 85000.00, '2023-02-01', true),
('EMP003', '<PERSON>', '<PERSON>', '<EMAIL>', '******-0103', 'Backend Developer', 'Engineering', 'full-time', 'remote', 90000.00, '2023-03-10', true),
('EMP004', 'Emily', 'Brown', '<EMAIL>', '******-0104', 'Product Manager', 'Product', 'full-time', 'on-site', 110000.00, '2022-11-20', true),
('EMP005', 'David', 'Wilson', '<EMAIL>', '******-0105', 'DevOps Engineer', 'Engineering', 'full-time', 'remote', 100000.00, '2023-04-05', true);

-- Insert sample interns
INSERT INTO interns (intern_id, first_name, last_name, email, phone, university, major, year_level, position, department, internship_type, start_date, end_date, hours_per_week, is_active) VALUES
('INT001', 'Alex', 'Chen', '<EMAIL>', '******-0201', 'Stanford University', 'Computer Science', 'Junior', 'Frontend Development Intern', 'Engineering', 'paid', '2024-06-01', '2024-08-31', 40, true),
('INT002', 'Maria', 'Garcia', '<EMAIL>', '******-0202', 'UC Berkeley', 'Design', 'Senior', 'UI/UX Design Intern', 'Design', 'paid', '2024-06-01', '2024-08-31', 30, true),
('INT003', 'James', 'Taylor', '<EMAIL>', '******-0203', 'MIT', 'Computer Science', 'Sophomore', 'Backend Development Intern', 'Engineering', 'unpaid', '2024-07-01', '2024-09-30', 20, true);

-- Insert sample job applications
INSERT INTO job_applications (
    application_id, first_name, last_name, email, phone, address, city, state, zip_code, country,
    position, department, expected_salary, available_start_date, employment_type, work_location,
    experience_level, current_company, current_position, total_experience, relevant_experience,
    education_level, university, graduation_year, gpa, degree, field_of_study,
    technical_skills, soft_skills, portfolio_url, linkedin_url, github_url, cover_letter,
    work_authorization, background_check_consent, drug_test_consent, status, submitted_at
) VALUES
('APP-2024-001', 'Robert', 'Anderson', '<EMAIL>', '******-1001', '123 Main St', 'San Francisco', 'CA', '94105', 'USA',
'Senior Full Stack Developer', 'Engineering', '$120,000 - $140,000', '2024-03-01', 'full-time', 'remote',
'Senior (5-8 years)', 'Tech Corp', 'Full Stack Developer', '6 years', '5 years',
'Bachelor''s Degree', 'UC San Diego', '2018', '3.7', 'Bachelor of Science', 'Computer Science',
'React, Node.js, TypeScript, PostgreSQL, AWS', 'Leadership, Communication, Problem Solving',
'https://robertanderson.dev', 'https://linkedin.com/in/robertanderson', 'https://github.com/robertanderson',
'I am excited to apply for the Senior Full Stack Developer position at Delta Xero Creations...',
'US Citizen', true, true, 'submitted', '2024-01-15 10:30:00+00'),

('APP-2024-002', 'Lisa', 'Thompson', '<EMAIL>', '******-1002', '456 Oak Ave', 'Austin', 'TX', '73301', 'USA',
'Product Manager', 'Product', '$100,000 - $120,000', '2024-02-15', 'full-time', 'hybrid',
'Mid-Level (3-5 years)', 'StartupXYZ', 'Associate Product Manager', '4 years', '4 years',
'Master''s Degree', 'University of Texas', '2020', '3.8', 'Master of Business Administration', 'Business Administration',
'Product Strategy, Data Analysis, Agile, Scrum', 'Strategic Thinking, Team Leadership, Communication',
'https://lisathompson.com', 'https://linkedin.com/in/lisathompson', '',
'I am passionate about creating products that solve real user problems...',
'US Citizen', true, true, 'under_review', '2024-01-20 14:15:00+00');

-- Insert sample internship applications
INSERT INTO internship_applications (
    application_id, first_name, last_name, email, phone, address, city, state, zip_code, country, date_of_birth,
    university, major, year_level, gpa, graduation_date,
    position, department, duration, start_date, internship_type, hours_per_week, work_days,
    previous_internships, relevant_courses, skills, projects, portfolio_url, linkedin_url, github_url, cover_letter,
    work_authorization, background_check_consent, agreement_consent, status, submitted_at
) VALUES
('INT-2024-001', 'Emma', 'Wilson', '<EMAIL>', '******-2001', '789 College St', 'Berkeley', 'CA', '94720', 'USA', '2002-05-15',
'UC Berkeley', 'Computer Science', 'Junior', '3.6', '2025-05-15',
'Frontend Development Intern', 'Engineering', '3 months', '2024-06-01', 'paid', 40, ARRAY['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
'Previous internship at local startup working on web development', 'Data Structures, Algorithms, Web Development, Database Systems',
'React, JavaScript, HTML/CSS, Git, Python', 'Personal portfolio website, E-commerce web app, Mobile-responsive dashboard',
'https://emmawilson.dev', 'https://linkedin.com/in/emmawilson', 'https://github.com/emmawilson',
'I am excited to apply for the Frontend Development Internship at Delta Xero Creations...',
'US Citizen', true, true, 'submitted', '2024-01-18 09:45:00+00'),

('INT-2024-002', 'Kevin', 'Lee', '<EMAIL>', '******-2002', '321 University Blvd', 'Stanford', 'CA', '94305', 'USA', '2003-08-22',
'Stanford University', 'Design', 'Sophomore', '3.8', '2026-06-15',
'UI/UX Design Intern', 'Design', '4 months', '2024-06-01', 'paid', 30, ARRAY['Monday', 'Tuesday', 'Wednesday', 'Thursday'],
'No previous internships but extensive coursework and personal projects', 'Design Thinking, Human-Computer Interaction, Visual Design, Prototyping',
'Figma, Adobe Creative Suite, Sketch, InVision, HTML/CSS', 'Mobile app redesign project, Website UI overhaul, Design system creation',
'https://kevinlee.design', 'https://linkedin.com/in/kevinlee', '',
'I am passionate about creating intuitive and beautiful user experiences...',
'US Citizen', true, true, 'under_review', '2024-01-22 16:20:00+00');

-- Insert sample references for applications
INSERT INTO application_references (job_application_id, name, title, company, email, phone, relationship) VALUES
((SELECT id FROM job_applications WHERE application_id = 'APP-2024-001'), 'Jennifer Martinez', 'Senior Engineering Manager', 'Tech Corp', '<EMAIL>', '******-3001', 'Direct Manager'),
((SELECT id FROM job_applications WHERE application_id = 'APP-2024-001'), 'Michael Chang', 'Lead Developer', 'Tech Corp', '<EMAIL>', '******-3002', 'Colleague'),
((SELECT id FROM job_applications WHERE application_id = 'APP-2024-002'), 'Rachel Green', 'VP of Product', 'StartupXYZ', '<EMAIL>', '******-3003', 'Direct Manager');

INSERT INTO application_references (internship_application_id, name, title, company, email, phone, relationship) VALUES
((SELECT id FROM internship_applications WHERE application_id = 'INT-2024-001'), 'Dr. Sarah Kim', 'Professor', 'UC Berkeley', '<EMAIL>', '******-4001', 'Academic Advisor'),
((SELECT id FROM internship_applications WHERE application_id = 'INT-2024-001'), 'Tom Rodriguez', 'CTO', 'Local Startup', '<EMAIL>', '******-4002', 'Previous Internship Supervisor'),
((SELECT id FROM internship_applications WHERE application_id = 'INT-2024-002'), 'Dr. Amanda Foster', 'Professor', 'Stanford University', '<EMAIL>', '******-4003', 'Academic Advisor');

-- Insert sample application status history
INSERT INTO application_status_history (job_application_id, old_status, new_status, changed_by, notes) VALUES
((SELECT id FROM job_applications WHERE application_id = 'APP-2024-001'), 'submitted', 'under_review', (SELECT id FROM users WHERE email = '<EMAIL>'), 'Initial review started'),
((SELECT id FROM job_applications WHERE application_id = 'APP-2024-002'), 'submitted', 'under_review', (SELECT id FROM users WHERE email = '<EMAIL>'), 'Moved to technical review');

INSERT INTO application_status_history (internship_application_id, old_status, new_status, changed_by, notes) VALUES
((SELECT id FROM internship_applications WHERE application_id = 'INT-2024-001'), 'submitted', 'under_review', (SELECT id FROM users WHERE email = '<EMAIL>'), 'Portfolio review in progress'),
((SELECT id FROM internship_applications WHERE application_id = 'INT-2024-002'), 'submitted', 'under_review', (SELECT id FROM users WHERE email = '<EMAIL>'), 'Design portfolio evaluation');

-- Create views for easier data access
CREATE VIEW application_summary AS
SELECT 
    'job' as application_type,
    id,
    application_id,
    first_name,
    last_name,
    email,
    position,
    department,
    status,
    submitted_at,
    created_at
FROM job_applications
UNION ALL
SELECT 
    'internship' as application_type,
    id,
    application_id,
    first_name,
    last_name,
    email,
    position,
    department,
    status,
    submitted_at,
    created_at
FROM internship_applications;

-- Create view for dashboard statistics
CREATE VIEW dashboard_stats AS
SELECT 
    (SELECT COUNT(*) FROM employees WHERE is_active = true) as total_employees,
    (SELECT COUNT(*) FROM interns WHERE is_active = true) as total_interns,
    (SELECT COUNT(*) FROM job_applications WHERE status = 'submitted') as pending_job_applications,
    (SELECT COUNT(*) FROM internship_applications WHERE status = 'submitted') as pending_internship_applications,
    (SELECT COUNT(*) FROM job_applications) as total_job_applications,
    (SELECT COUNT(*) FROM internship_applications) as total_internship_applications,
    (SELECT COUNT(*) FROM application_summary WHERE submitted_at >= CURRENT_DATE - INTERVAL '30 days') as applications_last_30_days,
    (SELECT COUNT(*) FROM application_summary WHERE submitted_at >= CURRENT_DATE - INTERVAL '7 days') as applications_last_7_days;

-- Useful queries for application management

-- Query to get all pending applications with applicant details
/*
SELECT 
    application_type,
    application_id,
    first_name || ' ' || last_name as full_name,
    email,
    position,
    department,
    submitted_at
FROM application_summary 
WHERE status = 'submitted'
ORDER BY submitted_at DESC;
*/

-- Query to get application statistics by department
/*
SELECT 
    department,
    application_type,
    COUNT(*) as total_applications,
    COUNT(CASE WHEN status = 'submitted' THEN 1 END) as pending,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
FROM application_summary
GROUP BY department, application_type
ORDER BY department, application_type;
*/

-- Query to get recent application activity
/*
SELECT 
    ash.created_at,
    'job' as type,
    ja.first_name || ' ' || ja.last_name as applicant_name,
    ja.position,
    ash.old_status,
    ash.new_status,
    u.first_name || ' ' || u.last_name as changed_by
FROM application_status_history ash
JOIN job_applications ja ON ash.job_application_id = ja.id
LEFT JOIN users u ON ash.changed_by = u.id
WHERE ash.job_application_id IS NOT NULL

UNION ALL

SELECT 
    ash.created_at,
    'internship' as type,
    ia.first_name || ' ' || ia.last_name as applicant_name,
    ia.position,
    ash.old_status,
    ash.new_status,
    u.first_name || ' ' || u.last_name as changed_by
FROM application_status_history ash
JOIN internship_applications ia ON ash.internship_application_id = ia.id
LEFT JOIN users u ON ash.changed_by = u.id
WHERE ash.internship_application_id IS NOT NULL

ORDER BY created_at DESC
LIMIT 20;
*/
