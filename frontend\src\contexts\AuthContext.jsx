import React, { createContext, useContext, useState, useEffect } from 'react'
import { userAPI } from '../lib/api'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // Check if user is logged in on app start
  useEffect(() => {
    const checkAuth = () => {
      try {
        const storedUser = localStorage.getItem('adminUser')
        const token = localStorage.getItem('adminToken')
        
        if (storedUser && token) {
          const userData = JSON.parse(storedUser)
          setUser(userData)
          setIsAuthenticated(true)
        }
      } catch (error) {
        console.error('Error checking auth:', error)
        // Clear invalid data
        localStorage.removeItem('adminUser')
        localStorage.removeItem('adminToken')
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  // Login function
  const login = async (usernameOrEmail, password) => {
    try {
      setLoading(true)
      const { data, error, requiresPasswordChange } = await userAPI.login(usernameOrEmail, password)
      
      if (error) {
        throw new Error(error)
      }

      // Check if user has admin privileges
      if (!['admin', 'supervisor'].includes(data.role)) {
        throw new Error('Access denied. Admin privileges required.')
      }

      // Store user data and token
      const token = `admin_${data.id}_${Date.now()}`
      localStorage.setItem('adminUser', JSON.stringify(data))
      localStorage.setItem('adminToken', token)
      
      setUser(data)
      setIsAuthenticated(true)
      
      return { 
        success: true, 
        requiresPasswordChange,
        user: data 
      }
    } catch (error) {
      console.error('Login error:', error)
      return { 
        success: false, 
        error: error.message 
      }
    } finally {
      setLoading(false)
    }
  }

  // Logout function
  const logout = () => {
    localStorage.removeItem('adminUser')
    localStorage.removeItem('adminToken')
    setUser(null)
    setIsAuthenticated(false)
  }

  // Change password function
  const changePassword = async (newPassword) => {
    try {
      if (!user) throw new Error('No user logged in')
      
      const { data, error } = await userAPI.changePassword(user.id, newPassword)
      
      if (error) {
        throw new Error(error)
      }

      // Update user data
      const updatedUser = { ...user, must_change_password: false, is_temporary_password: false }
      localStorage.setItem('adminUser', JSON.stringify(updatedUser))
      setUser(updatedUser)
      
      return { success: true }
    } catch (error) {
      console.error('Password change error:', error)
      return { success: false, error: error.message }
    }
  }

  // Check if user has admin privileges
  const isAdmin = () => {
    return user && ['admin', 'supervisor'].includes(user.role)
  }

  // Check if password change is required
  const requiresPasswordChange = () => {
    return user && (user.must_change_password || user.is_temporary_password)
  }

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    logout,
    changePassword,
    isAdmin,
    requiresPasswordChange
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
