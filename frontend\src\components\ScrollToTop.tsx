import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronUp } from 'lucide-react';
import { getSmoothScrollInstance, scrollToTop } from '../hooks/useSmoothScroll';

const ScrollToTop = () => {
  const { pathname } = useLocation();
  const [isVisible, setIsVisible] = useState(false);

  // Scroll to top when route changes
  useEffect(() => {
    scrollToTop(true); // immediate scroll on route change
  }, [pathname]);

  // Show/hide scroll to top button based on scroll position
  useEffect(() => {
    const toggleVisibility = () => {
      // Show button when user scrolls down 300px
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    const lenis = getSmoothScrollInstance();

    if (lenis) {
      // Use Lenis scroll event
      lenis.on('scroll', toggleVisibility);
      return () => lenis.off('scroll', toggleVisibility);
    } else {
      // Fallback to window scroll event
      let ticking = false;
      const handleScroll = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            toggleVisibility();
            ticking = false;
          });
          ticking = true;
        }
      };

      window.addEventListener('scroll', handleScroll, { passive: true });
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, []);

  // Smooth scroll to top function
  const handleScrollToTop = () => {
    scrollToTop(); // Use the imported function
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.button
          initial={{ opacity: 0, scale: 0.8, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 20 }}
          transition={{ 
            duration: 0.3, 
            ease: [0.16, 1, 0.3, 1] 
          }}
          onClick={handleScrollToTop}
          className="fixed bottom-8 right-8 z-50 group"
          aria-label="Scroll to top"
        >
          {/* Button Background with Glassmorphism */}
          <div className="relative">
            {/* Main Button */}
            <div className="w-12 h-12 bg-black/80 backdrop-blur-xl border border-white/20 rounded-full flex items-center justify-center shadow-2xl shadow-black/50 transition-all duration-300 group-hover:bg-black/90 group-hover:border-primary-400/50 group-hover:shadow-primary-500/25">
              <ChevronUp 
                className="w-5 h-5 text-white group-hover:text-primary-400 transition-colors duration-300" 
              />
            </div>

            {/* Hover Glow Effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary-500/20 to-primary-600/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md -z-10"></div>

            {/* Pulse Animation on Hover */}
            <div className="absolute inset-0 bg-primary-500/10 rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-ping transition-opacity duration-300"></div>
          </div>

          {/* Tooltip */}
          <div className="absolute bottom-full right-0 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
            <div className="bg-black/90 backdrop-blur-xl border border-white/20 text-white text-sm px-3 py-2 rounded-lg whitespace-nowrap shadow-xl">
              Scroll to top
              {/* Tooltip Arrow */}
              <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-black/90"></div>
            </div>
          </div>
        </motion.button>
      )}
    </AnimatePresence>
  );
};

export default ScrollToTop;
