import { TestimonialsColumn } from "@/components/ui/testimonials-columns-1";
import { motion } from "motion/react";

const testimonials = [
  {
    text: "Delta Xero Creations ne hamare business ko completely transform kar diya. Unki attention to detail aur innovative approach ne hamare expectations se kahin zyada deliver kiya.",
    image: "https://placehold.co/150x150/4F46E5/FFFFFF?text=AS",
    name: "<PERSON><PERSON><PERSON>",
    role: "CEO, TechVision India",
  },
  {
    text: "Working with Delta Xero team feels like having our own extended family. They truly understand Indian market needs and deliver exceptional results.",
    image: "https://placehold.co/150x150/EC4899/FFFFFF?text=PP",
    name: "<PERSON><PERSON>",
    role: "CTO, InnovateHub Mumbai",
  },
  {
    text: "Unka creativity aur technical expertise bilkul unmatched hai. Hamara e-commerce platform launch karne mein unhone amazing support diya. Highly recommended!",
    image: "https://placehold.co/150x150/10B981/FFFFFF?text=KR",
    name: "<PERSON><PERSON><PERSON>",
    role: "Marketing Director, GrowthCorp Hyderabad",
  },
  {
    text: "Their affordable pricing without compromising quality made our startup dream possible. GSAP animations aur smooth performance dekh kar clients impressed ho gaye!",
    image: "https://placehold.co/150x150/F59E0B/FFFFFF?text=RG",
    name: "Rohit Gupta",
    role: "Founder, StartupHub Delhi",
  },
  {
    text: "Delta Xero team ka expertise modern frameworks mein outstanding hai. Hamara mobile app launch karne mein unhone crucial role play kiya.",
    image: "https://placehold.co/150x150/8B5CF6/FFFFFF?text=SJ",
    name: "Sneha Joshi",
    role: "Product Manager, TechFlow Pune",
  },
  {
    text: "From concept to deployment, Delta Xero ne beyond expectations deliver kiya. Unka 24/7 support aur global team approach really impressive hai.",
    image: "https://placehold.co/150x150/EF4444/FFFFFF?text=VS",
    name: "Vikram Singh",
    role: "Operations Director, GlobalTech Bangalore",
  },
  {
    text: "Mobile-first approach aur responsive design implement karne ke baad hamara user engagement 300% badh gaya. Results dekh kar hum khush ho gaye!",
    image: "https://placehold.co/150x150/06B6D4/FFFFFF?text=AI",
    name: "Ananya Iyer",
    role: "Digital Marketing Lead, RetailPro Chennai",
  },
  {
    text: "GSAP animations aur modern web technologies mein unka expertise kamal ka hai. Hamara website launch karne ke baad clients ka response overwhelming tha.",
    image: "https://placehold.co/150x150/84CC16/FFFFFF?text=KM",
    name: "Karan Mehta",
    role: "Creative Director, DesignStudio Ahmedabad",
  },
  {
    text: "Delta Xero ka commitment affordable excellence ke liye remarkable hai. Hamara non-profit organization ke liye premium web development accessible banaya unhone.",
    image: "https://placehold.co/150x150/F97316/FFFFFF?text=RA",
    name: "Riya Agarwal",
    role: "Executive Director, HelpingHands Kolkata",
  },
];

const firstColumn = testimonials.slice(0, 3);
const secondColumn = testimonials.slice(3, 6);
const thirdColumn = testimonials.slice(6, 9);

const TestimonialsSection = () => {
  return (
    <section className="bg-dark-950 my-20 relative py-20">
      <div className="container z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.1, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
          className="flex flex-col items-center justify-center max-w-[540px] mx-auto"
        >
          <div className="flex justify-center">
          
          </div>

          <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tighter mt-5 text-white text-center">
            What our <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">clients say</span>
          </h2>
          <p className="text-center mt-5 opacity-75 text-gray-400 text-lg">
            See what our customers have to say about working with Delta Xero Creations.
          </p>
        </motion.div>

        <div className="flex justify-center gap-6 mt-10 [mask-image:linear-gradient(to_bottom,transparent,black_25%,black_75%,transparent)] max-h-[740px] overflow-hidden">
          <TestimonialsColumn testimonials={firstColumn} duration={15} />
          <TestimonialsColumn testimonials={secondColumn} className="hidden md:block" duration={19} />
          <TestimonialsColumn testimonials={thirdColumn} className="hidden lg:block" duration={17} />
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
