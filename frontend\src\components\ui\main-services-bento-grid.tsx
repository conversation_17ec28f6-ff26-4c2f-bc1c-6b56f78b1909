import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { ArrowRight, Star } from 'lucide-react'
import { cn } from '@/lib/utils'

interface MainService {
  icon: React.ComponentType<any>
  title: string
  subtitle: string
  description: string
  features: string[]
  technologies: string[]
  color: string
  price: string
  deliveryTime: string
  image: string
  metrics: Record<string, string>
  badge?: string
}

interface MainServicesBentoGridProps {
  services: MainService[]
  className?: string
}

const MainServicesBentoGrid: React.FC<MainServicesBentoGridProps> = ({ services, className }) => {
  // Define layout for main services (3 services) - Hero left, two cards stacked right
  const getGridLayout = () => {
    return [
      { size: 'hero', position: 'col-span-1 md:col-span-2 lg:col-span-2 row-span-2' },
      { size: 'medium', position: 'col-span-1 md:col-span-2 lg:col-span-1 row-span-1' },
      { size: 'medium', position: 'col-span-1 md:col-span-2 lg:col-span-1 row-span-1' }
    ]
  }

  const layout = getGridLayout()

  return (
    <div className={cn(
      "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 auto-rows-[280px] max-w-7xl mx-auto",
      className
    )}>
      {services.map((service, index) => {
        const Icon = service.icon
        const layoutConfig = layout[index] || { size: 'large', position: 'col-span-1 md:col-span-2 row-span-2' }
        
        return (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 40, scale: 0.9 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ 
              duration: 0.8, 
              delay: index * 0.2,
              type: "spring",
              stiffness: 80
            }}
            viewport={{ once: true }}
            className={cn(
              layoutConfig.position,
              "group relative overflow-hidden rounded-3xl border border-white/10 bg-gradient-to-br from-dark-800/50 to-dark-900/50 backdrop-blur-xl hover:border-white/20 transition-all duration-700 cursor-pointer"
            )}
          >
            {/* Background Image for Hero Card */}
            {layoutConfig.size === 'hero' && (
              <div className="absolute inset-0 opacity-20 group-hover:opacity-30 transition-opacity duration-700">
                <img 
                  src={service.image} 
                  alt={service.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-br from-dark-900/80 via-dark-900/60 to-dark-900/80" />
              </div>
            )}

            {/* Background Gradient */}
            <div className={cn(
              "absolute inset-0 bg-gradient-to-br opacity-10 group-hover:opacity-20 transition-opacity duration-700",
              service.color
            )} />
            
            {/* Animated Background Pattern */}
            <div className="absolute inset-0 opacity-5 group-hover:opacity-15 transition-opacity duration-700">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.3) 1px, transparent 0)`,
                backgroundSize: '32px 32px'
              }}></div>
            </div>

            {/* Badge - Only show for hero card */}
            {service.badge && layoutConfig.size === 'hero' && (
              <div className="absolute top-6 right-6 z-20">
                <div className={cn(
                  "px-3 py-1 rounded-full bg-gradient-to-r text-white text-xs font-bold flex items-center space-x-1 shadow-lg",
                  service.color
                )}>
                  <Star className="w-3 h-3" />
                  <span>{service.badge}</span>
                </div>
              </div>
            )}

            {/* Content */}
            <div className={cn(
              "relative z-10 h-full flex flex-col justify-between",
              layoutConfig.size === 'hero' ? 'p-8' : 'p-5'
            )}>
              {/* Header */}
              <div className={cn(
                "flex items-start justify-between",
                layoutConfig.size === 'hero' ? 'mb-6' : 'mb-4'
              )}>
                <div className={cn(
                  "rounded-2xl bg-gradient-to-br flex items-center justify-center group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-xl",
                  service.color,
                  layoutConfig.size === 'hero' ? 'w-16 h-16 rounded-3xl' : 'w-14 h-14'
                )}>
                  <Icon className={cn(
                    "text-white",
                    layoutConfig.size === 'hero' ? 'w-8 h-8' : 'w-7 h-7'
                  )} />
                </div>
                {layoutConfig.size !== 'hero' && (
                  <div className="text-right">
                    <div className="text-lg font-bold text-white">{service.price}</div>
                    <div className="text-primary-400 text-sm font-medium">{service.deliveryTime}</div>
                  </div>
                )}
              </div>
              
              {/* Title & Subtitle */}
              <div className={cn(
                layoutConfig.size === 'hero' ? 'mb-4' : 'mb-3'
              )}>
                <h3 className={cn(
                  "font-black group-hover:text-white transition-colors duration-500 leading-tight",
                  layoutConfig.size === 'hero' ? 'text-2xl md:text-3xl text-white mb-2' : 'text-xl text-white mb-1'
                )}>
                  {service.title}
                </h3>
                <p className={cn(
                  "font-medium bg-gradient-to-r bg-clip-text text-transparent",
                  service.color,
                  layoutConfig.size === 'hero' ? 'text-base' : 'text-sm'
                )}>
                  {service.subtitle}
                </p>
              </div>

              {/* Description */}
              <p className={cn(
                "text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-500 overflow-hidden",
                layoutConfig.size === 'hero' ? 'text-sm mb-6' : 'text-sm mb-4'
              )}
              style={{
                display: '-webkit-box',
                WebkitLineClamp: layoutConfig.size === 'hero' ? 4 : 2,
                WebkitBoxOrient: 'vertical' as const,
                overflow: 'hidden'
              }}>
                {service.description}
              </p>

              {/* Features for Hero Card */}
              {layoutConfig.size === 'hero' && (
                <div className="grid grid-cols-2 gap-2 mb-4">
                  {service.features.slice(0, 6).map((feature, idx) => (
                    <div key={idx} className="flex items-center space-x-2">
                      <div className={cn(
                        "w-1.5 h-1.5 rounded-full bg-gradient-to-r",
                        service.color
                      )} />
                      <span className="text-xs text-gray-300 truncate">{feature}</span>
                    </div>
                  ))}
                </div>
              )}

              {/* Technologies - Only show for hero card */}
              {layoutConfig.size === 'hero' && (
                <div className="flex flex-wrap gap-1 mb-4">
                  {service.technologies.slice(0, 6).map((tech, idx) => (
                    <span
                      key={idx}
                      className="px-2 py-1 bg-white/10 rounded-md text-gray-300 border border-white/20 text-xs"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              )}

              {/* Metrics for Hero Card */}
              {layoutConfig.size === 'hero' && (
                <div className="grid grid-cols-3 gap-3 mb-4">
                  {Object.entries(service.metrics).map(([key, value], idx) => (
                    <div key={idx} className="text-center bg-white/5 rounded-lg p-2 border border-white/10">
                      <div className="text-sm font-bold text-white">{value}</div>
                      <div className="text-gray-400 text-[10px] uppercase tracking-wider">{key}</div>
                    </div>
                  ))}
                </div>
              )}

              {/* Pricing for Hero Card */}
              {layoutConfig.size === 'hero' && (
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <div className="text-xl font-bold text-white">{service.price}</div>
                    <div className="text-primary-400 text-xs font-medium">{service.deliveryTime}</div>
                  </div>
                </div>
              )}

              {/* CTA Button */}
              <div className="mt-auto">
                <Link to="/get-quote">
                  <motion.button
                    whileHover={{ scale: 1.02, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                    className={cn(
                      "w-full bg-gradient-to-r text-white font-bold rounded-xl transition-all duration-500 relative overflow-hidden group/btn shadow-lg flex items-center justify-center space-x-2",
                      service.color,
                      layoutConfig.size === 'hero' ? 'py-3 text-base' : 'py-2.5 text-sm'
                    )}
                  >
                    <span className="relative z-10">Get Quote</span>
                    <ArrowRight className="relative z-10" size={layoutConfig.size === 'hero' ? 18 : 16} />
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>
                  </motion.button>
                </Link>
              </div>
            </div>

            {/* Hover Effect Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700" />
            
            {/* Border Glow Effect */}
            <div className={cn(
              "absolute inset-0 rounded-3xl bg-gradient-to-br opacity-0 group-hover:opacity-40 transition-opacity duration-700 blur-2xl -z-10",
              service.color
            )} />
          </motion.div>
        )
      })}
    </div>
  )
}

export default MainServicesBentoGrid
