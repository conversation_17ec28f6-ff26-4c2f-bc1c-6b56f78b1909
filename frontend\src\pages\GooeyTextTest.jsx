import React from 'react';
import { GooeyText } from '@/components/ui/gooey-text-morphing';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const GooeyTextTest = () => {
  return (
    <div className="min-h-screen bg-dark-950 flex flex-col">
      {/* Navigation */}
      <div className="fixed top-4 left-4 z-50">
        <Link 
          to="/" 
          className="flex items-center gap-2 px-4 py-2 bg-dark-800/80 backdrop-blur-sm border border-gray-700 rounded-lg text-white hover:text-primary-400 transition-colors duration-300"
        >
          <ArrowLeft size={16} />
          Back to Home
        </Link>
      </div>

      {/* Test Section 1: Loading Screen Text */}
      <section className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-8">Loading Screen Text Test</h1>
          <div className="h-32">
            <GooeyText
              texts={["Delta", "Xero", "Creations", "Welcome"]}
              morphTime={1.2}
              cooldownTime={0.4}
              className="font-bold"
              textClassName="text-4xl md:text-6xl bg-gradient-to-r from-primary-400 via-white to-primary-400 bg-clip-text text-transparent"
            />
          </div>
          <p className="text-gray-400 mt-4">This should cycle: Delta → Xero → Creations → Welcome → Delta...</p>
        </div>
      </section>

      {/* Test Section 2: Fast Cycling */}
      <section className="flex-1 flex items-center justify-center bg-dark-900">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-8">Fast Cycling Test</h2>
          <div className="h-24">
            <GooeyText
              texts={["Fast", "Quick", "Rapid", "Swift"]}
              morphTime={0.6}
              cooldownTime={0.2}
              className="font-bold"
              textClassName="text-3xl md:text-4xl text-primary-400"
            />
          </div>
          <p className="text-gray-400 mt-4">This should cycle quickly: Fast → Quick → Rapid → Swift → Fast...</p>
        </div>
      </section>

      {/* Test Section 3: Two Words */}
      <section className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-8">Two Words Test</h2>
          <div className="h-24">
            <GooeyText
              texts={["Hello", "World"]}
              morphTime={1.0}
              cooldownTime={0.5}
              className="font-bold"
              textClassName="text-3xl md:text-4xl text-green-400"
            />
          </div>
          <p className="text-gray-400 mt-4">This should alternate: Hello → World → Hello → World...</p>
        </div>
      </section>

      {/* Debug Info */}
      <div className="p-4 bg-dark-800 text-center">
        <p className="text-gray-400 text-sm">
          Watch each section to ensure smooth transitions and correct word cycling.
          <br />
          If any section shows the same word repeatedly or glitches, there's still an issue.
        </p>
      </div>
    </div>
  );
};

export default GooeyTextTest;
