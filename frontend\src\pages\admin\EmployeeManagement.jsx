import React, { useState } from 'react'
import { motion } from 'framer-motion'
import AdminNavigation from '../../components/AdminNavigation'
import {
  Users,
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  Eye,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Briefcase,
  Award,
  MoreVertical,
  Download,
  Upload,
  CheckCircle,
  Clock,
  User,
  Building,
  Star
} from 'lucide-react'

const EmployeeManagement = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterDepartment, setFilterDepartment] = useState('all')
  const [employees, setEmployees] = useState([
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      position: 'Senior Frontend Developer',
      department: 'Engineering',
      joinDate: '2023-01-15',
      status: 'Active',
      skills: ['React', 'TypeScript', 'Node.js', 'GraphQL'],
      location: 'New York, NY',
      experience: '5 years',
      manager: '<PERSON>',
      projects: ['E-commerce Platform', 'Mobile App'],
      performance: 'Excellent'
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      position: 'UI/UX Designer',
      department: 'Design',
      joinDate: '2023-03-20',
      status: 'Active',
      skills: ['Figma', 'Adobe XD', 'Sketch', 'Prototyping'],
      location: 'Los Angeles, CA',
      experience: '3 years',
      manager: 'David Chen',
      projects: ['Brand Redesign', 'Mobile Interface'],
      performance: 'Good'
    },
    {
      id: 3,
      name: 'Mike Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      position: 'Backend Developer',
      department: 'Engineering',
      joinDate: '2022-11-10',
      status: 'Active',
      skills: ['Python', 'Django', 'PostgreSQL', 'AWS'],
      location: 'Chicago, IL',
      experience: '4 years',
      manager: 'Sarah Wilson',
      projects: ['API Development', 'Database Optimization'],
      performance: 'Excellent'
    }
  ])

  const departments = ['all', 'Engineering', 'Design', 'Marketing', 'Sales', 'HR']

  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.position.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesDepartment = filterDepartment === 'all' || employee.department === filterDepartment
    return matchesSearch && matchesDepartment
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'Active': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'On Leave': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'Inactive': return 'bg-red-500/20 text-red-400 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  return (
    <div className="min-h-screen bg-dark-950">
      <AdminNavigation />
      <div className="pt-20 pb-24 md:pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-black text-white mb-2">Employee Management</h1>
              <p className="text-gray-400">Manage your team members and their information</p>
            </div>
            <button className="bg-gradient-to-r from-primary-500 to-blue-500 text-white px-6 py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300 flex items-center space-x-2">
              <Plus size={16} />
              <span>Add Employee</span>
            </button>
          </div>

          {/* Filters */}
          <div className="bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 rounded-2xl p-6 mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search employees..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  />
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Filter className="text-gray-400" size={20} />
                  <select
                    value={filterDepartment}
                    onChange={(e) => setFilterDepartment(e.target.value)}
                    className="bg-white/5 border border-white/20 rounded-xl px-4 py-3 text-white focus:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-400/20 transition-all duration-300"
                  >
                    {departments.map(dept => (
                      <option key={dept} value={dept} className="bg-dark-900 text-white">
                        {dept === 'all' ? 'All Departments' : dept}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Employee Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredEmployees.map((employee, index) => (
              <motion.div
                key={employee.id}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 rounded-2xl p-6 hover:border-white/20 transition-all duration-300 group"
              >
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-blue-500 rounded-xl flex items-center justify-center">
                      <User className="text-white" size={24} />
                    </div>
                    <div>
                      <h3 className="text-white font-bold">{employee.name}</h3>
                      <p className="text-gray-400 text-sm">{employee.position}</p>
                    </div>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(employee.status)}`}>
                    {employee.status}
                  </span>
                </div>

                {/* Employee Info */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-center space-x-2 text-gray-300">
                    <Mail size={14} />
                    <span className="text-sm">{employee.email}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-300">
                    <Building size={14} />
                    <span className="text-sm">Department: {employee.department}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-300">
                    <CheckCircle size={14} />
                    <span className="text-sm">Manager: {employee.manager}</span>
                  </div>
                </div>

                {/* Skills */}
                <div className="mb-4">
                  <p className="text-gray-400 text-xs mb-2">Skills</p>
                  <div className="flex flex-wrap gap-2">
                    {employee.skills.map((skill, skillIndex) => (
                      <span
                        key={skillIndex}
                        className="bg-primary-500/20 text-primary-400 px-2 py-1 rounded-lg text-xs"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Projects */}
                <div className="mb-4">
                  <p className="text-gray-400 text-xs mb-2">Current Projects</p>
                  <div className="space-y-1">
                    {employee.projects.map((project, projectIndex) => (
                      <div key={projectIndex} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-primary-400 rounded-full"></div>
                        <span className="text-gray-300 text-sm">{project}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Performance & Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-white/10">
                  <div className="flex items-center space-x-2">
                    <Star className="text-yellow-400" size={16} />
                    <span className="text-sm text-gray-300">{employee.performance}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="text-blue-400 hover:text-blue-300 transition-colors duration-300">
                      <Eye size={16} />
                    </button>
                    <button className="text-green-400 hover:text-green-300 transition-colors duration-300">
                      <Edit size={16} />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Empty State */}
          {filteredEmployees.length === 0 && (
            <div className="text-center py-12">
              <Users className="text-gray-400 mx-auto mb-4" size={64} />
              <h3 className="text-xl font-bold text-white mb-2">No employees found</h3>
              <p className="text-gray-400 mb-6">Try adjusting your search or filter criteria</p>
              <button className="bg-gradient-to-r from-primary-500 to-blue-500 text-white px-6 py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300">
                Add First Employee
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default EmployeeManagement