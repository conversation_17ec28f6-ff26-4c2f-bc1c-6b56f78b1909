import React, { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Menu, X, Code, Briefcase, Users, BookOpen, Phone } from 'lucide-react'
import { DeltaXeroFooter } from './DeltaXeroFooter'

const Layout = ({ children }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const location = useLocation()

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useEffect(() => {
    setIsMenuOpen(false)
  }, [location])

  const navItems = [
    { name: 'Home', path: '/', icon: Code },
    { name: 'Services', path: '/services', icon: Briefcase },
    { name: 'About', path: '/about', icon: Users },
    { name: 'Careers', path: '/jobs', icon: Briefcase },
    { name: 'Blog', path: '/blog', icon: BookOpen },
    { name: 'Get Quote', path: '/contact', icon: Phone },
  ]

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <motion.nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
          scrolled
            ? 'bg-black/90 backdrop-blur-xl border-b border-white/20 shadow-2xl shadow-black/30'
            : 'bg-transparent backdrop-blur-none border-b border-transparent shadow-none'
        }`}
        style={{
          backdropFilter: scrolled ? 'blur(20px) saturate(180%)' : 'none',
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center">
              <motion.img
                src="https://sdwgyjjcxdhdlcuvjadq.supabase.co/storage/v1/object/public/invoices//delta_zero_vertical_logo-removebg-preview.png"
                alt="Delta Xero Creations Logo"
                whileHover={{ scale: 1.05 }}
                className="h-8 w-auto"
              />
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-2">
              {navItems.map((item) => {
                const Icon = item.icon
                return (
                  <motion.div
                    key={item.path}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link
                      to={item.path}
                      className={`relative flex items-center space-x-2 px-4 py-2.5 rounded-xl transition-all duration-300 group ${
                        location.pathname === item.path
                          ? 'text-primary-400 bg-white/10 backdrop-blur-md border border-white/20 shadow-lg'
                          : 'text-gray-300 hover:text-white hover:bg-white/5 hover:backdrop-blur-md hover:border hover:border-white/10 hover:shadow-lg'
                      }`}
                      style={{
                        backdropFilter: location.pathname === item.path ? 'blur(12px) saturate(180%)' : undefined
                      }}
                    >
                      <Icon size={16} className="transition-colors duration-300" />
                      <span className="font-medium">{item.name}</span>

                      {/* Glassmorphism glow effect on hover */}
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500/20 to-primary-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 blur-sm"></div>

                      {/* Active indicator */}
                      {location.pathname === item.path && (
                        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-400 rounded-full"></div>
                      )}
                    </Link>
                  </motion.div>
                )
              })}
            </div>

            {/* Mobile menu button */}
            <motion.button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="md:hidden p-3 rounded-xl text-gray-300 hover:text-white transition-all duration-300 bg-white/5 hover:bg-white/10 backdrop-blur-md border border-white/10 hover:border-white/20 shadow-lg"
              style={{
                backdropFilter: 'blur(12px) saturate(180%)'
              }}
            >
              <motion.div
                animate={{ rotate: isMenuOpen ? 180 : 0 }}
                transition={{ duration: 0.3 }}
              >
                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </motion.div>
            </motion.button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <motion.div
          initial={{ opacity: 0, height: 0, y: -20 }}
          animate={{
            opacity: isMenuOpen ? 1 : 0,
            height: isMenuOpen ? 'auto' : 0,
            y: isMenuOpen ? 0 : -20,
          }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="md:hidden bg-black/20 backdrop-blur-xl border-t border-white/10 shadow-2xl"
          style={{
            backdropFilter: 'blur(20px) saturate(180%)'
          }}
        >
          <div className="px-6 py-6 space-y-3">
            {navItems.map((item, index) => {
              const Icon = item.icon
              return (
                <motion.div
                  key={item.path}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{
                    opacity: isMenuOpen ? 1 : 0,
                    x: isMenuOpen ? 0 : -20
                  }}
                  transition={{
                    duration: 0.3,
                    delay: isMenuOpen ? index * 0.1 : 0
                  }}
                >
                  <Link
                    to={item.path}
                    onClick={() => setIsMenuOpen(false)}
                    className={`relative flex items-center space-x-3 px-4 py-3.5 rounded-xl transition-all duration-300 group ${
                      location.pathname === item.path
                        ? 'text-primary-400 bg-white/15 backdrop-blur-md border border-white/20 shadow-lg'
                        : 'text-gray-300 hover:text-white hover:bg-white/10 hover:backdrop-blur-md hover:border hover:border-white/15 hover:shadow-lg'
                    }`}
                    style={{
                      backdropFilter: location.pathname === item.path ? 'blur(12px) saturate(180%)' : undefined
                    }}
                  >
                    <Icon size={20} className="transition-colors duration-300" />
                    <span className="font-medium">{item.name}</span>

                    {/* Glassmorphism glow effect on hover */}
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500/20 to-primary-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 blur-sm"></div>

                    {/* Active indicator */}
                    {location.pathname === item.path && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-primary-400 rounded-full"></div>
                    )}
                  </Link>
                </motion.div>
              )
            })}
          </div>
        </motion.div>
      </motion.nav>

      {/* Main Content */}
      <main className="pt-16">
        {children}
      </main>

      {/* Footer */}
      <DeltaXeroFooter />
    </div>
  )
}

export default Layout
