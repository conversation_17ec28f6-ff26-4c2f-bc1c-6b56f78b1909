import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Link } from 'react-router-dom'
import SolutionsBentoGrid from '../components/ui/solutions-bento-grid'
import { Code, Globe, Zap, Database, Shield, Rocket, ArrowRight, CheckCircle, Users, Award, Star, Clock, Target, Layers, Settings, Monitor, Smartphone, TrendingUp, BarChart3, Gauge, Lock, Cloud, Cpu } from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const WebDevelopment = () => {
  const statsRef = useRef(null)

  useEffect(() => {
    // Stats counter animation
    gsap.fromTo('.stat-counter',
      { textContent: 0 },
      {
        textContent: (i, target) => target.getAttribute('data-value'),
        duration: 2,
        ease: 'power2.out',
        snap: { textContent: 1 },
        scrollTrigger: {
          trigger: statsRef.current,
          start: 'top 80%',
        }
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const webServices = [
    {
      icon: Globe,
      title: 'Enterprise Web Applications',
      description: 'Scalable, high-performance web applications built with cutting-edge technologies for enterprise-level requirements.',
      features: ['React/Next.js Development', 'Server-Side Rendering', 'Advanced SEO Optimization', 'Performance Monitoring'],
      color: 'from-blue-500 to-cyan-500',
      price: 'From $8,000',
      timeline: '8-16 weeks',
      metrics: { performance: '99.9% uptime', speed: '< 2s load time', scale: '10M+ users' }
    },
    {
      icon: Database,
      title: 'E-commerce Solutions',
      description: 'Complete online commerce platforms with advanced features, payment processing, and business intelligence.',
      features: ['Multi-vendor Support', 'Advanced Analytics', 'Inventory Automation', 'Global Payment Processing'],
      color: 'from-green-500 to-emerald-500',
      price: 'From $12,000',
      timeline: '10-20 weeks',
      metrics: { conversion: '+40% sales', processing: '1M+ transactions', security: 'PCI DSS compliant' }
    },
    {
      icon: Zap,
      title: 'Progressive Web Apps',
      description: 'Next-generation web applications that deliver native app experiences with offline capabilities and push notifications.',
      features: ['Offline-First Architecture', 'Push Notifications', 'App Store Distribution', 'Background Sync'],
      color: 'from-purple-500 to-pink-500',
      price: 'From $6,000',
      timeline: '6-12 weeks',
      metrics: { engagement: '+60% retention', performance: '90+ Lighthouse score', reach: 'Cross-platform' }
    },
    {
      icon: Shield,
      title: 'API & Microservices',
      description: 'Robust, scalable backend architectures with microservices, APIs, and cloud-native infrastructure.',
      features: ['Microservices Architecture', 'GraphQL APIs', 'Cloud Infrastructure', 'Real-time Data Processing'],
      color: 'from-orange-500 to-red-500',
      price: 'From $10,000',
      timeline: '8-14 weeks',
      metrics: { reliability: '99.99% SLA', throughput: '10K+ req/sec', security: 'Enterprise-grade' }
    }
  ]

  const technologies = [
    { name: 'React', logo: '⚛️', category: 'Frontend', description: 'Modern UI library' },
    { name: 'Next.js', logo: '▲', category: 'Framework', description: 'Full-stack React framework' },
    { name: 'TypeScript', logo: '🔷', category: 'Language', description: 'Type-safe JavaScript' },
    { name: 'Node.js', logo: '🟢', category: 'Backend', description: 'JavaScript runtime' },
    { name: 'PostgreSQL', logo: '🐘', category: 'Database', description: 'Advanced SQL database' },
    { name: 'MongoDB', logo: '🍃', category: 'Database', description: 'NoSQL document database' },
    { name: 'AWS', logo: '☁️', category: 'Cloud', description: 'Cloud infrastructure' },
    { name: 'Docker', logo: '🐳', category: 'DevOps', description: 'Containerization platform' },
    { name: 'GraphQL', logo: '🔗', category: 'API', description: 'Query language for APIs' },
    { name: 'Redis', logo: '🔴', category: 'Cache', description: 'In-memory data store' },
    { name: 'Kubernetes', logo: '⚙️', category: 'Orchestration', description: 'Container orchestration' },
    { name: 'Terraform', logo: '🏗️', category: 'Infrastructure', description: 'Infrastructure as code' }
  ]

  const stats = [
    { number: 200, label: 'Web Applications Built', suffix: '+', icon: Globe, color: 'from-blue-500 to-cyan-500' },
    { number: 99.8, label: 'Uptime Guarantee', suffix: '%', icon: TrendingUp, color: 'from-green-500 to-emerald-500' },
    { number: 1.2, label: 'Average Load Time', suffix: 's', prefix: '<', icon: Gauge, color: 'from-purple-500 to-pink-500' },
    { number: 10, label: 'Million Users Served', suffix: 'M+', icon: Users, color: 'from-orange-500 to-red-500' }
  ]

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen"
    >
      {/* Hero Section */}
      <section className="relative py-32 bg-gradient-to-b from-dark-950 via-dark-900 to-dark-950 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              className="inline-flex items-center bg-gradient-to-r from-primary-500/10 to-cyan-500/10 border border-primary-500/20 rounded-full px-6 py-3 mb-8"
            >
              <div className="w-2 h-2 bg-primary-400 rounded-full mr-3 animate-pulse"></div>
              <Code className="w-4 h-4 text-primary-400 mr-2" />
              <span className="text-primary-400 text-sm font-semibold uppercase tracking-wider">Enterprise Web Development</span>
            </motion.div>

            <motion.h1
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-5xl sm:text-6xl md:text-7xl font-black text-white mb-8 leading-none"
            >
              BUILD THE
              <br />
              <span className="bg-gradient-to-r from-primary-400 via-cyan-400 to-blue-400 bg-clip-text text-transparent">
                FUTURE OF WEB
              </span>
            </motion.h1>

            <motion.p
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-xl sm:text-2xl text-gray-300 max-w-4xl mx-auto mb-12 leading-relaxed"
            >
              Enterprise-grade web applications built with cutting-edge technologies.
              Scalable, secure, and designed for the modern digital landscape.
            </motion.p>

            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex justify-center mb-16"
            >
              <Link to="/get-quote">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gradient-to-r from-primary-500 to-cyan-500 text-white px-12 py-4 rounded-2xl font-bold text-lg hover:shadow-2xl hover:shadow-primary-500/25 transition-all duration-300 flex items-center justify-center space-x-3"
                >
                  <span>Get Quote</span>
                  <ArrowRight className="w-5 h-5" />
                </motion.button>
              </Link>
            </motion.div>

            {/* Key Features */}
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
            >
              {[
                { icon: Gauge, text: '< 2s Load Time', color: 'text-green-400' },
                { icon: Lock, text: 'Enterprise Security', color: 'text-blue-400' },
                { icon: Cloud, text: '99.9% Uptime', color: 'text-purple-400' }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-center space-x-3 text-gray-300">
                  <item.icon className={item.color} size={20} />
                  <span className="font-medium">{item.text}</span>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-32 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-primary-500/10 to-cyan-500/10 border border-primary-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Rocket className="text-primary-400 mr-3" size={16} />
              <span className="text-primary-400 text-sm font-semibold uppercase tracking-wider">Our Solutions</span>
            </motion.div>

            <h2 className="text-5xl md:text-6xl font-black text-white mb-8 leading-tight">
              WEB DEVELOPMENT
              <br />
              <span className="bg-gradient-to-r from-primary-400 via-cyan-400 to-blue-400 bg-clip-text text-transparent">
                SERVICES
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
              Enterprise-grade web solutions designed to scale with your business and deliver exceptional performance
            </p>
          </motion.div>

          <SolutionsBentoGrid services={webServices} />
        </div>
      </section>

      {/* Technologies Section */}
      <section className="py-32 bg-gradient-to-b from-dark-950 via-dark-900 to-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border border-emerald-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Cpu className="text-emerald-400 mr-3" size={16} />
              <span className="text-emerald-400 text-sm font-semibold uppercase tracking-wider">Technology Stack</span>
            </motion.div>

            <h2 className="text-5xl md:text-6xl font-black text-white mb-8 leading-tight">
              CUTTING-EDGE
              <br />
              <span className="bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 bg-clip-text text-transparent">
                TECHNOLOGIES
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
              We leverage the most advanced technologies to build robust, scalable, and future-proof web solutions
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {technologies.map((tech, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-gradient-to-br from-black/60 to-black/30 backdrop-blur-xl border border-white/20 rounded-2xl p-8 text-center hover:border-white/30 transition-all duration-500 hover:transform hover:scale-[1.05] hover:-translate-y-2 relative overflow-hidden"
                  style={{ backdropFilter: 'blur(20px) saturate(180%)' }}
                >
                  {/* Background Glow */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-cyan-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>

                  {/* Content */}
                  <div className="relative z-10">
                    {/* Logo */}
                    <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                      {tech.logo}
                    </div>

                    {/* Name */}
                    <h3 className="text-white font-bold text-lg mb-2 group-hover:text-primary-400 transition-colors duration-300">
                      {tech.name}
                    </h3>

                    {/* Category */}
                    <div className="text-primary-400 text-sm font-medium mb-3 uppercase tracking-wider">
                      {tech.category}
                    </div>

                    {/* Description */}
                    <p className="text-gray-400 text-sm leading-relaxed">
                      {tech.description}
                    </p>
                  </div>

                  {/* Animated Border */}
                  <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                    style={{
                      background: `linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.3), transparent)`,
                      backgroundSize: '200% 200%',
                      animation: 'gradient-shift 3s ease infinite'
                    }}
                  ></div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section ref={statsRef} className="py-32 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-black text-white mb-4">
              PROVEN <span className="bg-gradient-to-r from-primary-400 to-cyan-400 bg-clip-text text-transparent">RESULTS</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Numbers that speak to our commitment to excellence and client success
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group"
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <div className="bg-gradient-to-br from-black/60 to-black/30 backdrop-blur-xl border border-white/20 p-8 rounded-3xl text-center group-hover:border-white/30 transition-all duration-500 relative overflow-hidden"
                    style={{ backdropFilter: 'blur(20px) saturate(180%)' }}
                  >
                    {/* Background Gradient */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-5 group-hover:opacity-15 transition-opacity duration-500`}></div>

                    {/* Content */}
                    <div className="relative z-10">
                      {/* Icon */}
                      <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg`}>
                        <Icon className="text-white" size={24} />
                      </div>

                      {/* Number */}
                      <div className="text-4xl md:text-5xl font-black text-white mb-3 group-hover:text-primary-300 transition-colors duration-300">
                        {stat.prefix}
                        <span className="stat-counter" data-value={stat.number}>0</span>
                        {stat.suffix}
                      </div>

                      {/* Label */}
                      <div className="text-gray-400 font-medium text-lg">{stat.label}</div>
                    </div>

                    {/* Hover Glow Effect */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl`}></div>

                    {/* Animated Border */}
                    <div className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                      style={{
                        background: `linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.3), transparent)`,
                        backgroundSize: '200% 200%',
                        animation: 'gradient-shift 3s ease infinite'
                      }}
                    ></div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Development Process Section */}
      <section className="py-20 bg-dark-900/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Our Development Process
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              A proven methodology that ensures your web application is delivered on time, within budget, and exceeds expectations
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "Discovery & Planning",
                description: "We analyze your requirements, target audience, and business goals to create a comprehensive project roadmap.",
                icon: Target,
                details: ["Requirement Analysis", "User Research", "Technical Planning", "Project Timeline"]
              },
              {
                step: "02",
                title: "Design & Prototyping",
                description: "Creating wireframes, mockups, and interactive prototypes to visualize the final product.",
                icon: Layers,
                details: ["Wireframing", "UI/UX Design", "Interactive Prototypes", "Design System"]
              },
              {
                step: "03",
                title: "Development & Testing",
                description: "Building your application with clean, scalable code and rigorous testing at every stage.",
                icon: Code,
                details: ["Frontend Development", "Backend Development", "Database Design", "Quality Assurance"]
              },
              {
                step: "04",
                title: "Launch & Support",
                description: "Deploying your application and providing ongoing maintenance and support services.",
                icon: Rocket,
                details: ["Deployment", "Performance Optimization", "Ongoing Support", "Feature Updates"]
              }
            ].map((process, index) => {
              const Icon = process.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="relative"
                >
                  <div className="bg-dark-800/50 border border-gray-700 rounded-2xl p-6 h-full hover:border-primary-500/50 transition-all duration-300">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                        {process.step}
                      </div>
                      <Icon className="w-8 h-8 text-primary-400" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-3">{process.title}</h3>
                    <p className="text-gray-400 mb-4 leading-relaxed">{process.description}</p>
                    <ul className="space-y-2">
                      {process.details.map((detail, idx) => (
                        <li key={idx} className="flex items-center text-sm text-gray-300">
                          <CheckCircle className="w-4 h-4 text-primary-400 mr-2 flex-shrink-0" />
                          {detail}
                        </li>
                      ))}
                    </ul>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-20 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Why Choose Delta Xero Creations for Web Development?
              </h2>
              <p className="text-xl text-gray-400 mb-8">
                We combine technical expertise with creative innovation to deliver web solutions that drive real business results.
              </p>

              <div className="space-y-6">
                {[
                  {
                    icon: Clock,
                    title: "Fast Delivery",
                    description: "We deliver projects 30% faster than industry average without compromising quality."
                  },
                  {
                    icon: Shield,
                    title: "Secure & Scalable",
                    description: "Built with security best practices and designed to scale with your business growth."
                  },
                  {
                    icon: Users,
                    title: "Expert Team",
                    description: "Our developers have 5+ years of experience in modern web technologies."
                  },
                  {
                    icon: Award,
                    title: "Quality Assurance",
                    description: "Rigorous testing ensures your application works flawlessly across all devices."
                  }
                ].map((feature, index) => {
                  const Icon = feature.icon
                  return (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-start space-x-4"
                    >
                      <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
                        <p className="text-gray-400">{feature.description}</p>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-primary-500/20 to-primary-600/20 rounded-2xl p-8 border border-primary-500/30">
                <h3 className="text-2xl font-bold text-white mb-6">What You Get</h3>
                <div className="space-y-4">
                  {[
                    "Responsive design that works on all devices",
                    "SEO-optimized for better search rankings",
                    "Fast loading speeds (< 3 seconds)",
                    "Modern, clean user interface",
                    "Secure coding practices",
                    "Cross-browser compatibility",
                    "Content management system",
                    "Analytics and tracking setup",
                    "6 months free support",
                    "Source code ownership"
                  ].map((benefit, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: 20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      viewport={{ once: true }}
                      className="flex items-center space-x-3"
                    >
                      <CheckCircle className="w-5 h-5 text-primary-400 flex-shrink-0" />
                      <span className="text-gray-300">{benefit}</span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Badge */}
            <motion.div
              className="inline-flex items-center bg-black/20 backdrop-blur-xl border border-white/10 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
              style={{ backdropFilter: 'blur(20px) saturate(180%)' }}
            >
              <Rocket className="text-primary-400 mr-3" size={16} />
              <span className="text-white text-sm font-medium">Ready to Transform?</span>
            </motion.div>

            {/* Title */}
            <h2 className="text-5xl md:text-7xl font-black text-white mb-8 leading-none">
              LET'S BUILD
              <br />
              <span className="bg-gradient-to-r from-primary-400 via-cyan-400 to-blue-400 bg-clip-text text-transparent">
                YOUR VISION
              </span>
            </h2>

            {/* Description */}
            <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
              Ready to transform your digital presence? Get a free consultation and discover how we can help your business grow.
            </p>

            {/* CTA Button */}
            <div className="flex justify-center mb-16">
              <Link to="/get-quote">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gradient-to-r from-primary-500 to-cyan-500 text-white px-12 py-4 rounded-2xl font-bold text-lg hover:shadow-2xl hover:shadow-primary-500/25 transition-all duration-300 flex items-center justify-center space-x-3"
                >
                  <span>Schedule Consultation</span>
                  <ArrowRight size={20} />
                </motion.button>
              </Link>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              {[
                { icon: CheckCircle, text: 'Free Consultation', color: 'text-green-400' },
                { icon: CheckCircle, text: 'Custom Solutions', color: 'text-blue-400' },
                { icon: CheckCircle, text: 'Ongoing Support', color: 'text-purple-400' }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  className="flex items-center justify-center space-x-3 text-gray-300"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <item.icon className={item.color} size={20} />
                  <span className="font-medium">{item.text}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    </motion.div>
  )
}

export default WebDevelopment

// Add CSS animations for gradient effects
const style = document.createElement('style')
style.textContent = `
  @keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes pulse-glow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
  }
`
if (!document.head.querySelector('style[data-web-dev-animations]')) {
  style.setAttribute('data-web-dev-animations', 'true')
  document.head.appendChild(style)
}
