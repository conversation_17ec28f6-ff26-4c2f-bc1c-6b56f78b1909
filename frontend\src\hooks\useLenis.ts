import { useEffect } from 'react'
import Lenis from 'lenis'

let lenis: Lenis | null = null

export const useLenis = (): <PERSON><PERSON> | null => {
  useEffect(() => {
    // Initialize Lenis
    lenis = new Lenis({
      duration: 1.2,
      easing: (t: number) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
      direction: 'vertical',
      gestureDirection: 'vertical',
      smooth: true,
      mouseMultiplier: 1,
      smoothTouch: false,
      touchMultiplier: 2,
      infinite: false,
    })

    // Animation frame function
    function raf(time: number) {
      lenis?.raf(time)
      requestAnimationFrame(raf)
    }

    requestAnimationFrame(raf)

    // Cleanup function
    return () => {
      if (lenis) {
        lenis.destroy()
        lenis = null
      }
    }
  }, [])

  return lenis
}

// Export lenis instance for external use
export const getLenis = (): Lenis | null => lenis
