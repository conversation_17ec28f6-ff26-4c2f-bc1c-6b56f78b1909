import React from 'react'
import { motion } from 'framer-motion'
import { Shield, FileText, AlertCircle, CheckCircle } from 'lucide-react'

const LegalTerms = () => {
  const sections = [
    {
      title: "1. Acceptance of Terms",
      content: "By accessing and using Delta Xero Creations' services, you accept and agree to be bound by the terms and provision of this agreement. These terms apply to all visitors, users, and others who access or use our services."
    },
    {
      title: "2. Description of Service",
      content: "Delta Xero Creations provides web development, mobile app development, UI/UX design, and digital transformation services. We reserve the right to modify, suspend, or discontinue any aspect of our services at any time."
    },
    {
      title: "3. User Responsibilities",
      content: "Users are responsible for providing accurate information, maintaining the confidentiality of their account credentials, and using our services in compliance with applicable laws and regulations."
    },
    {
      title: "4. Intellectual Property",
      content: "All content, features, and functionality of our services are owned by Delta Xero Creations and are protected by copyright, trademark, and other intellectual property laws. Custom work created for clients becomes the property of the client upon full payment."
    },
    {
      title: "5. Payment Terms",
      content: "Payment terms are specified in individual project agreements. Generally, we require a 50% deposit before project commencement and the remaining balance upon completion. Late payments may incur additional fees."
    },
    {
      title: "6. Project Delivery",
      content: "We strive to meet all agreed-upon deadlines. However, delivery dates are estimates and may be subject to change due to project complexity, client feedback cycles, or unforeseen circumstances."
    },
    {
      title: "7. Limitation of Liability",
      content: "Delta Xero Creations shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of our services, even if we have been advised of the possibility of such damages."
    },
    {
      title: "8. Termination",
      content: "Either party may terminate a project agreement with written notice. Upon termination, the client is responsible for payment of all work completed up to the termination date."
    },
    {
      title: "9. Governing Law",
      content: "These terms shall be governed by and construed in accordance with the laws of the jurisdiction where Delta Xero Creations operates, without regard to conflict of law principles."
    },
    {
      title: "10. Changes to Terms",
      content: "We reserve the right to modify these terms at any time. Changes will be effective immediately upon posting on our website. Continued use of our services constitutes acceptance of the modified terms."
    }
  ]

  return (
    <div className="min-h-screen bg-dark-950 text-white">
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        <div className="container mx-auto relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center bg-primary-500/10 border border-primary-500/20 rounded-full px-6 py-3 mb-8">
              <FileText className="text-primary-400 mr-3" size={20} />
              <span className="text-primary-400 text-sm font-semibold uppercase tracking-wider">Legal Information</span>
            </div>
            
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
              Terms and Conditions
            </h1>
            
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Please read these terms and conditions carefully before using our services.
            </p>
            
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-400">
              <div className="flex items-center space-x-2">
                <CheckCircle className="text-green-400" size={16} />
                <span>Last Updated: January 2025</span>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="text-blue-400" size={16} />
                <span>Legally Binding</span>
              </div>
            </div>
          </motion.div>
        </div>
        
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-500/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl"></div>
        </div>
      </section>

      {/* Terms Content */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto max-w-4xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="bg-dark-900/50 backdrop-blur-sm border border-gray-800 rounded-3xl p-8 md:p-12"
          >
            <div className="space-y-12">
              {sections.map((section, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 * index }}
                  className="border-b border-gray-800 pb-8 last:border-b-0 last:pb-0"
                >
                  <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
                    <div className="w-2 h-2 bg-primary-500 rounded-full mr-4"></div>
                    {section.title}
                  </h2>
                  <p className="text-gray-300 leading-relaxed text-lg">
                    {section.content}
                  </p>
                </motion.div>
              ))}
            </div>
            
            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="mt-16 p-8 bg-primary-500/5 border border-primary-500/20 rounded-2xl"
            >
              <div className="flex items-start space-x-4">
                <AlertCircle className="text-primary-400 mt-1" size={24} />
                <div>
                  <h3 className="text-xl font-bold text-white mb-2">Questions About These Terms?</h3>
                  <p className="text-gray-300 mb-4">
                    If you have any questions about these Terms and Conditions, please contact us:
                  </p>
                  <div className="space-y-2 text-gray-400">
                    <p>Email: <EMAIL></p>
                    <p>Phone: +****************</p>
                    <p>Address: 123 Innovation Drive, Tech City, TC 12345</p>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default LegalTerms
