import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { 
  Smartphone, 
  Tablet, 
  Zap, 
  Shield, 
  ArrowRight, 
  CheckCircle,
  Download,
  Users,
  Star,
  Layers,
  Rocket,
  TrendingUp
} from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const MobileEngineering = () => {
  const statsRef = useRef(null)
  const heroRef = useRef(null)

  useEffect(() => {
    // Hero animation
    gsap.fromTo(heroRef.current?.children,
      { y: 100, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1.2,
        stagger: 0.2,
        ease: 'power3.out'
      }
    )

    // Stats counter animation
    gsap.fromTo('.stat-counter',
      { textContent: 0 },
      {
        textContent: (i, target) => target.getAttribute('data-value'),
        duration: 2,
        ease: 'power2.out',
        snap: { textContent: 1 },
        scrollTrigger: {
          trigger: statsRef.current,
          start: 'top 80%',
        }
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const technologies = [
    { name: 'React Native 0.74', icon: Smartphone, description: 'Latest cross-platform framework' },
    { name: 'Flutter 3.24', icon: Layers, description: 'Google\'s modern UI toolkit' },
    { name: 'Kotlin Multiplatform', icon: Tablet, description: 'Native shared business logic' },
    { name: 'AI/ML Integration', icon: Download, description: 'On-device intelligence' }
  ]

  const features = [
    {
      icon: Smartphone,
      title: '5G-Optimized Performance',
      description: 'Ultra-fast apps leveraging 5G capabilities with edge computing and real-time features.',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Zap,
      title: 'AI-Powered Features',
      description: 'Integrated machine learning for personalization, predictive analytics, and smart automation.',
      color: 'from-yellow-500 to-orange-500'
    },
    {
      icon: Shield,
      title: 'Biometric Security',
      description: 'Advanced authentication with Face ID, Touch ID, and behavioral biometrics for maximum security.',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Users,
      title: 'Accessibility First',
      description: 'WCAG 2.1 compliant design ensuring inclusive experiences for all users.',
      color: 'from-blue-500 to-cyan-500'
    }
  ]

  const stats = [
    { number: 150, label: 'Mobile Apps Delivered', suffix: '+' },
    { number: 4.9, label: 'Average App Store Rating', suffix: '/5' },
    { number: 5, label: 'Million+ Downloads', suffix: 'M+' },
    { number: 97, label: 'Client Retention Rate', suffix: '%' }
  ]

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="-mt-16 lg:-mt-20"
    >
      {/* Hero Section */}
      <section className="relative pt-24 pb-20 min-h-screen flex items-center justify-center bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-full blur-3xl"></div>
        </div>

        {/* Animated Grid Background */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(168, 85, 247, 0.3) 1px, transparent 0)`,
            backgroundSize: '50px 50px'
          }}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div ref={heroRef}>
            {/* Badge */}
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Smartphone className="text-purple-400 mr-3" size={16} />
              <span className="text-purple-400 text-sm font-semibold uppercase tracking-wider">Mobile Engineering</span>
            </motion.div>

            {/* Main Heading */}
            <h1 className="text-5xl md:text-7xl font-black text-white mb-8 leading-tight">
              NATIVE MOBILE
              <br />
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-rose-400 bg-clip-text text-transparent">
                APPLICATIONS
              </span>
            </h1>

            {/* Subheading */}
            <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
              Next-generation mobile applications with AI integration, 5G optimization, and cross-platform excellence.
              Built for the mobile-first world with cutting-edge frameworks and native performance.
            </p>

            {/* CTA Button */}
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-12 py-5 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-300 flex items-center space-x-3 mx-auto relative overflow-hidden group"
            >
              <span className="relative z-10">Schedule Consultation</span>
              <ArrowRight className="relative z-10" size={20} />
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
            </motion.button>

            {/* Key Features */}
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-4xl mx-auto mt-16"
            >
              {technologies.map((tech, index) => (
                <div key={index} className="flex flex-col items-center space-y-3 text-gray-300">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-2xl flex items-center justify-center">
                    <tech.icon className="text-purple-400" size={24} />
                  </div>
                  <span className="font-medium text-center">{tech.name}</span>
                  <span className="text-sm text-gray-400 text-center">{tech.description}</span>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section ref={statsRef} className="py-20 bg-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-4xl md:text-5xl font-black text-white mb-2">
                  <span className="stat-counter" data-value={stat.number}>0</span>
                  <span className="text-purple-400">{stat.suffix}</span>
                </div>
                <div className="text-gray-400 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Rocket className="text-purple-400 mr-3" size={16} />
              <span className="text-purple-400 text-sm font-semibold uppercase tracking-wider">App Features</span>
            </motion.div>
            <h2 className="text-5xl md:text-6xl font-black text-white mb-8 leading-tight">
              EXCEPTIONAL
              <br />
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-rose-400 bg-clip-text text-transparent">
                USER EXPERIENCES
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
              Mobile applications built with cutting-edge technologies for maximum performance and engagement
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="h-full bg-gradient-to-br from-black/60 to-black/30 backdrop-blur-xl border border-white/20 rounded-3xl p-8 group-hover:border-white/30 transition-all duration-500 hover:transform hover:scale-[1.02] hover:-translate-y-3 relative">
                  {/* Icon */}
                  <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="text-white" size={28} />
                  </div>

                  {/* Content */}
                  <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-purple-400 transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-400 leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Hover Glow */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl`}></div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Smartphone className="text-purple-400 mr-3" size={16} />
              <span className="text-purple-400 text-sm font-semibold uppercase tracking-wider">Ready to Launch?</span>
            </motion.div>

            <h2 className="text-5xl md:text-6xl font-black text-white mb-8 leading-tight">
              BUILD YOUR
              <br />
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-rose-400 bg-clip-text text-transparent">
                MOBILE APP
              </span>
            </h2>

            <p className="text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed">
              Transform your idea into a powerful mobile application that users love
            </p>

            <div className="flex justify-center">
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-12 py-5 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-300 flex items-center space-x-3 relative overflow-hidden group"
              >
                <span className="relative z-10">Schedule Consultation</span>
                <ArrowRight className="relative z-10" size={20} />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
              </motion.button>
            </div>

            {/* Trust Indicators */}
            <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
              {[
                { icon: Star, text: 'Top Rated Apps', color: 'text-yellow-400' },
                { icon: Download, text: 'Store Approved', color: 'text-green-400' },
                { icon: TrendingUp, text: 'High Engagement', color: 'text-purple-400' },
                { icon: CheckCircle, text: 'Quality Assured', color: 'text-emerald-400' }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex flex-col items-center space-y-3"
                >
                  <div className="w-12 h-12 bg-white/10 rounded-2xl flex items-center justify-center">
                    <item.icon className={item.color} size={24} />
                  </div>
                  <span className="text-gray-300 text-sm font-medium">{item.text}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    </motion.div>
  )
}

export default MobileEngineering
