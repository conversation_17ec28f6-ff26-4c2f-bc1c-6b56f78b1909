import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface Feature {
  icon: React.ComponentType<any>
  title: string
  description: string
  color: string
}

interface FeaturesBentoGridProps {
  features: Feature[]
  className?: string
}

const FeaturesBentoGrid: React.FC<FeaturesBentoGridProps> = ({ features, className }) => {
  // Define different layouts based on number of features
  const getGridLayout = (count: number) => {
    if (count === 4) {
      return [
        { size: 'large', position: 'col-span-1 md:col-span-2 row-span-2' },
        { size: 'medium', position: 'col-span-1 row-span-1' },
        { size: 'medium', position: 'col-span-1 row-span-1' },
        { size: 'wide', position: 'col-span-1 md:col-span-2 row-span-1' }
      ]
    } else if (count === 6) {
      return [
        { size: 'large', position: 'col-span-1 md:col-span-2 row-span-2' },
        { size: 'medium', position: 'col-span-1 row-span-1' },
        { size: 'medium', position: 'col-span-1 row-span-1' },
        { size: 'wide', position: 'col-span-1 md:col-span-2 row-span-1' },
        { size: 'medium', position: 'col-span-1 row-span-1' },
        { size: 'medium', position: 'col-span-1 row-span-1' }
      ]
    } else {
      // Default layout for other counts
      return features.map((_, index) => ({
        size: index === 0 ? 'large' : 'medium',
        position: index === 0 ? 'col-span-1 md:col-span-2 row-span-2' : 'col-span-1 row-span-1'
      }))
    }
  }

  const layout = getGridLayout(features.length)

  return (
    <div className={cn(
      "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 auto-rows-[200px] max-w-7xl mx-auto",
      className
    )}>
      {features.map((feature, index) => {
        const Icon = feature.icon
        const layoutConfig = layout[index] || { size: 'medium', position: 'col-span-1 row-span-1' }
        
        return (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 30, scale: 0.9 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ 
              duration: 0.6, 
              delay: index * 0.1,
              type: "spring",
              stiffness: 100
            }}
            viewport={{ once: true }}
            className={cn(
              layoutConfig.position,
              "group relative overflow-hidden rounded-3xl border border-white/10 bg-gradient-to-br from-dark-800/50 to-dark-900/50 backdrop-blur-xl hover:border-white/20 transition-all duration-500 cursor-pointer"
            )}
          >
            {/* Background Gradient */}
            <div className={cn(
              "absolute inset-0 bg-gradient-to-br opacity-5 group-hover:opacity-15 transition-opacity duration-500",
              feature.color
            )} />
            
            {/* Animated Particles */}
            <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className={cn(
                    "absolute w-1 h-1 rounded-full bg-gradient-to-r",
                    feature.color
                  )}
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                  }}
                  animate={{
                    y: [0, -20, 0],
                    opacity: [0, 1, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: i * 0.3,
                  }}
                />
              ))}
            </div>

            {/* Content */}
            <div className="relative z-10 h-full flex flex-col p-6 md:p-8">
              {/* Icon */}
              <div className={cn(
                "w-14 h-14 rounded-2xl bg-gradient-to-br flex items-center justify-center mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg",
                feature.color
              )}>
                <div className='py-1'>
                <Icon className="w-7 h-7 text-white" />

                </div>
              </div>
              
              {/* Title */}
              <h3 className={cn(
                "font-bold mb-3 group-hover:text-white transition-colors duration-300",
                layoutConfig.size === 'large' ? 'text-2xl md:text-3xl text-white' : 'text-md md:text-lg text-white'
              )}>
                {feature.title}
              </h3>
              
              {/* Description */}
              <p className={cn(
                "text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-300 flex-1",
                layoutConfig.size === 'large' ? 'text-base md:text-lg' : 'text-sm md:text-sm'
              )}>
                {feature.description}
              </p>

              {/* Additional content for large cards */}
              {layoutConfig.size === 'large' && (
                <div className="mt-6">
                  <div className={cn(
                    "inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r text-white text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300",
                    feature.color
                  )}>
                    Learn More
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              )}
            </div>

            {/* Hover Effect Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            
            {/* Border Glow Effect */}
            <div className={cn(
              "absolute inset-0 rounded-3xl bg-gradient-to-br opacity-0 group-hover:opacity-30 transition-opacity duration-500 blur-xl -z-10",
              feature.color
            )} />

            {/* Corner Accent */}
            <div className={cn(
              "absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl opacity-10 group-hover:opacity-20 transition-opacity duration-500",
              feature.color
            )} style={{ clipPath: 'polygon(100% 0, 0 0, 100% 100%)' }} />
          </motion.div>
        )
      })}
    </div>
  )
}

export default FeaturesBentoGrid
