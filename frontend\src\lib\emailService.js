import emailjs from '@emailjs/browser'

// EmailJS configuration
const EMAILJS_CONFIG = {
  serviceId: 'service_15pixg2', // Your EmailJS service ID
  templateIds: {
    applicationConfirmation: 'template_deltaxero_app_confirm',
    newUserCredentials: 'template_deltaxero_new_user',
    adminNotification: 'template_deltaxero_admin_notify'
  },
  publicKey: 'iOQKBBBfqhJh_HNHJ' // Default public key - you may need to update this with yours
}

// Initialize EmailJS
emailjs.init(EMAILJS_CONFIG.publicKey)

// Email templates
const EMAIL_TEMPLATES = {
  jobApplicationConfirmation: (applicantData) => ({
    to_email: applicantData.email,
    to_name: `${applicantData.firstName} ${applicantData.lastName}`,
    subject: `Application Received - ${applicantData.position}`,
    message: `
Dear ${applicantData.firstName} ${applicantData.lastName},

Thank you for your interest in the ${applicantData.position} position at Delta Xero Creations.

We have successfully received your application and our team will review it carefully. Here are the details we received:

• Position: ${applicantData.position}
• Department: ${applicantData.department || 'Not specified'}
• Application ID: ${applicantData.applicationId}
• Submitted: ${new Date().toLocaleDateString()}

What happens next:
1. Our HR team will review your application within 3-5 business days
2. If your profile matches our requirements, we'll contact you for the next steps
3. You can expect to hear from us within 1-2 weeks

If you have any questions, please don't hesitate to contact <NAME_EMAIL>.

Best regards,
Delta Xero Creations HR Team
    `.trim()
  }),

  internshipApplicationConfirmation: (applicantData) => ({
    to_email: applicantData.email,
    to_name: `${applicantData.firstName} ${applicantData.lastName}`,
    subject: `Internship Application Received - ${applicantData.position}`,
    message: `
Dear ${applicantData.firstName} ${applicantData.lastName},

Thank you for applying for the ${applicantData.position} internship at Delta Xero Creations.

We have successfully received your internship application. Here are the details:

• Position: ${applicantData.position}
• University: ${applicantData.university}
• Major: ${applicantData.major}
• Application ID: ${applicantData.applicationId}
• Submitted: ${new Date().toLocaleDateString()}

Next Steps:
1. Our team will review your application and academic background
2. We'll assess your skills and project portfolio
3. Qualified candidates will be contacted for an interview
4. You can expect to hear from us within 1-2 weeks

We appreciate your interest in joining our team as an intern!

Best regards,
Delta Xero Creations Internship Team
    `.trim()
  }),

  newUserCredentials: (userData, temporaryPassword) => ({
    to_email: userData.email,
    to_name: userData.username,
    subject: 'Your Delta Xero Creations Admin Account',
    message: `
Dear ${userData.username},

Welcome to Delta Xero Creations! An admin account has been created for you.

Your login credentials:
• Username: ${userData.username}
• Email: ${userData.email}
• Temporary Password: ${temporaryPassword}
• Role: ${userData.role}

IMPORTANT SECURITY NOTICE:
- This is a temporary password that must be changed on your first login
- Please log in at: ${window.location.origin}/admin/login
- You will be prompted to set a new secure password
- Do not share these credentials with anyone

Login Instructions:
1. Visit the admin login page
2. Enter your username/email and temporary password
3. You'll be redirected to set a new password
4. Choose a strong password with at least 8 characters

If you have any questions or need assistance, please contact the system administrator.

Best regards,
Delta Xero Creations IT Team
    `.trim()
  }),

  adminNotificationJobApplication: (applicantData) => ({
    to_email: '<EMAIL>', // Admin email
    to_name: 'HR Team',
    subject: `New Job Application - ${applicantData.position}`,
    message: `
New job application received!

Applicant Details:
• Name: ${applicantData.firstName} ${applicantData.lastName}
• Email: ${applicantData.email}
• Phone: ${applicantData.phone || 'Not provided'}
• Position: ${applicantData.position}
• Department: ${applicantData.department || 'Not specified'}
• Experience: ${applicantData.experience || 'Not specified'}
• Application ID: ${applicantData.applicationId}

Application Summary:
• Submitted: ${new Date().toLocaleString()}
• Resume: ${applicantData.resumeUrl ? 'Uploaded' : 'Not provided'}
• Cover Letter: ${applicantData.coverLetter ? 'Provided' : 'Not provided'}
• Portfolio: ${applicantData.portfolioUrl ? 'Provided' : 'Not provided'}

Please review the application in the admin dashboard.

Admin Dashboard: ${window.location.origin}/admin/applications
    `.trim()
  }),

  adminNotificationInternshipApplication: (applicantData) => ({
    to_email: '<EMAIL>', // Admin email
    to_name: 'HR Team',
    subject: `New Internship Application - ${applicantData.position}`,
    message: `
New internship application received!

Applicant Details:
• Name: ${applicantData.firstName} ${applicantData.lastName}
• Email: ${applicantData.email}
• Phone: ${applicantData.phone || 'Not provided'}
• Position: ${applicantData.position}
• University: ${applicantData.university}
• Major: ${applicantData.major}
• Year: ${applicantData.yearLevel || 'Not specified'}
• GPA: ${applicantData.gpa || 'Not provided'}
• Application ID: ${applicantData.applicationId}

Application Summary:
• Submitted: ${new Date().toLocaleString()}
• Resume: ${applicantData.resumeUrl ? 'Uploaded' : 'Not provided'}
• Transcript: ${applicantData.transcriptUrl ? 'Uploaded' : 'Not provided'}
• Portfolio: ${applicantData.portfolioUrl ? 'Provided' : 'Not provided'}
• Skills: ${applicantData.skills ? applicantData.skills.join(', ') : 'Not specified'}

Please review the application in the admin dashboard.

Admin Dashboard: ${window.location.origin}/admin/applications
    `.trim()
  })
}

// Email service functions
export const emailService = {
  // Send job application confirmation to applicant
  sendJobApplicationConfirmation: async (applicantData) => {
    try {
      const template = EMAIL_TEMPLATES.jobApplicationConfirmation(applicantData)
      
      const result = await emailjs.send(
        EMAILJS_CONFIG.serviceId,
        EMAILJS_CONFIG.templateIds.applicationConfirmation,
        {
          to_email: template.to_email,
          to_name: template.to_name,
          subject: template.subject,
          message: template.message,
          from_name: 'Delta Xero Creations',
          reply_to: '<EMAIL>'
        }
      )

      console.log('Job application confirmation sent:', result)
      return { success: true, result }
    } catch (error) {
      console.error('Error sending job application confirmation:', error)
      return { success: false, error: error.message }
    }
  },

  // Send internship application confirmation to applicant
  sendInternshipApplicationConfirmation: async (applicantData) => {
    try {
      const template = EMAIL_TEMPLATES.internshipApplicationConfirmation(applicantData)
      
      const result = await emailjs.send(
        EMAILJS_CONFIG.serviceId,
        EMAILJS_CONFIG.templateIds.applicationConfirmation,
        {
          to_email: template.to_email,
          to_name: template.to_name,
          subject: template.subject,
          message: template.message,
          from_name: 'Delta Xero Creations',
          reply_to: '<EMAIL>'
        }
      )

      console.log('Internship application confirmation sent:', result)
      return { success: true, result }
    } catch (error) {
      console.error('Error sending internship application confirmation:', error)
      return { success: false, error: error.message }
    }
  },

  // Send new user credentials email
  sendNewUserCredentials: async (userData, temporaryPassword) => {
    try {
      const template = EMAIL_TEMPLATES.newUserCredentials(userData, temporaryPassword)
      
      const result = await emailjs.send(
        EMAILJS_CONFIG.serviceId,
        EMAILJS_CONFIG.templateIds.newUserCredentials,
        {
          to_email: template.to_email,
          to_name: template.to_name,
          subject: template.subject,
          message: template.message,
          from_name: 'Delta Xero Creations IT',
          reply_to: '<EMAIL>'
        }
      )

      console.log('New user credentials sent:', result)
      return { success: true, result }
    } catch (error) {
      console.error('Error sending new user credentials:', error)
      return { success: false, error: error.message }
    }
  },

  // Send admin notification for new job application
  sendAdminJobApplicationNotification: async (applicantData) => {
    try {
      const template = EMAIL_TEMPLATES.adminNotificationJobApplication(applicantData)
      
      const result = await emailjs.send(
        EMAILJS_CONFIG.serviceId,
        EMAILJS_CONFIG.templateIds.adminNotification,
        {
          to_email: template.to_email,
          to_name: template.to_name,
          subject: template.subject,
          message: template.message,
          from_name: 'Delta Xero System',
          reply_to: '<EMAIL>'
        }
      )

      console.log('Admin job application notification sent:', result)
      return { success: true, result }
    } catch (error) {
      console.error('Error sending admin job application notification:', error)
      return { success: false, error: error.message }
    }
  },

  // Send admin notification for new internship application
  sendAdminInternshipApplicationNotification: async (applicantData) => {
    try {
      const template = EMAIL_TEMPLATES.adminNotificationInternshipApplication(applicantData)
      
      const result = await emailjs.send(
        EMAILJS_CONFIG.serviceId,
        EMAILJS_CONFIG.templateIds.adminNotification,
        {
          to_email: template.to_email,
          to_name: template.to_name,
          subject: template.subject,
          message: template.message,
          from_name: 'Delta Xero System',
          reply_to: '<EMAIL>'
        }
      )

      console.log('Admin internship application notification sent:', result)
      return { success: true, result }
    } catch (error) {
      console.error('Error sending admin internship application notification:', error)
      return { success: false, error: error.message }
    }
  },

  // Send multiple emails (batch)
  sendBatchEmails: async (emailFunctions) => {
    const results = []
    
    for (const emailFunction of emailFunctions) {
      try {
        const result = await emailFunction()
        results.push(result)
      } catch (error) {
        results.push({ success: false, error: error.message })
      }
    }
    
    return results
  }
}

export default emailService
