/* Performance optimizations for smooth animations and interactions */

/* GPU acceleration for animated elements */
.hero-section,
.spline-container,
.service-card,
.stat-number,
.client-logo {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize Spline container */
.spline-container {
  contain: layout style paint;
  will-change: transform;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Optimize text rendering */
.hero-title,
.hero-subtitle {
  text-rendering: optimizeSpeed;
  font-display: swap;
}

/* Optimize gradients */
.gradient-text {
  background-attachment: fixed;
  -webkit-background-clip: text;
  background-clip: text;
}

/* Optimize button interactions */
.cta-button {
  contain: layout style;
  will-change: transform;
}

/* Optimize navbar */
.navbar {
  contain: layout style;
  will-change: transform, background-color;
}

/* Optimize scroll triggers */
.scroll-trigger-element {
  contain: layout style paint;
}
