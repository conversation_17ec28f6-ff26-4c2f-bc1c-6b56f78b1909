import React from 'react'
import { motion } from 'framer-motion'
import { Shield, Lock, Eye, Database, AlertCircle, CheckCircle } from 'lucide-react'

const LegalPrivacy = () => {
  const sections = [
    {
      title: "1. Information We Collect",
      icon: Database,
      content: "We collect information you provide directly to us, such as when you create an account, request a quote, or contact us. This may include your name, email address, phone number, company information, and project details."
    },
    {
      title: "2. How We Use Your Information",
      icon: Eye,
      content: "We use the information we collect to provide, maintain, and improve our services, communicate with you, process transactions, and send you technical notices and support messages."
    },
    {
      title: "3. Information Sharing",
      icon: Shield,
      content: "We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy. We may share information with trusted service providers who assist us in operating our website and conducting our business."
    },
    {
      title: "4. Data Security",
      icon: Lock,
      content: "We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure."
    },
    {
      title: "5. Cookies and Tracking",
      content: "We use cookies and similar tracking technologies to enhance your experience on our website. You can control cookie settings through your browser preferences."
    },
    {
      title: "6. Third-Party Services",
      content: "Our website may contain links to third-party websites or services. We are not responsible for the privacy practices of these external sites and encourage you to review their privacy policies."
    },
    {
      title: "7. Data Retention",
      content: "We retain your personal information for as long as necessary to provide our services and fulfill the purposes outlined in this policy, unless a longer retention period is required by law."
    },
    {
      title: "8. Your Rights",
      content: "You have the right to access, update, or delete your personal information. You may also opt out of certain communications from us. Contact us to exercise these rights."
    },
    {
      title: "9. Children's Privacy",
      content: "Our services are not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13."
    },
    {
      title: "10. Changes to This Policy",
      content: "We may update this privacy policy from time to time. We will notify you of any changes by posting the new policy on this page and updating the 'Last Updated' date."
    }
  ]

  return (
    <div className="min-h-screen bg-dark-950 text-white">
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        <div className="container mx-auto relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center bg-blue-500/10 border border-blue-500/20 rounded-full px-6 py-3 mb-8">
              <Shield className="text-blue-400 mr-3" size={20} />
              <span className="text-blue-400 text-sm font-semibold uppercase tracking-wider">Privacy & Security</span>
            </div>
            
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
              Privacy Policy
            </h1>
            
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Your privacy is important to us. This policy explains how we collect, use, and protect your information.
            </p>
            
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-400">
              <div className="flex items-center space-x-2">
                <CheckCircle className="text-green-400" size={16} />
                <span>Last Updated: January 2025</span>
              </div>
              <div className="flex items-center space-x-2">
                <Lock className="text-blue-400" size={16} />
                <span>GDPR Compliant</span>
              </div>
            </div>
          </motion.div>
        </div>
        
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary-500/10 rounded-full blur-3xl"></div>
        </div>
      </section>

      {/* Privacy Content */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto max-w-4xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="bg-dark-900/50 backdrop-blur-sm border border-gray-800 rounded-3xl p-8 md:p-12"
          >
            <div className="space-y-12">
              {sections.map((section, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 * index }}
                  className="border-b border-gray-800 pb-8 last:border-b-0 last:pb-0"
                >
                  <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
                    {section.icon && (
                      <div className="w-10 h-10 bg-blue-500/10 rounded-xl flex items-center justify-center mr-4">
                        <section.icon className="text-blue-400" size={20} />
                      </div>
                    )}
                    {!section.icon && <div className="w-2 h-2 bg-blue-500 rounded-full mr-4"></div>}
                    {section.title}
                  </h2>
                  <p className="text-gray-300 leading-relaxed text-lg">
                    {section.content}
                  </p>
                </motion.div>
              ))}
            </div>
            
            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="mt-16 p-8 bg-blue-500/5 border border-blue-500/20 rounded-2xl"
            >
              <div className="flex items-start space-x-4">
                <AlertCircle className="text-blue-400 mt-1" size={24} />
                <div>
                  <h3 className="text-xl font-bold text-white mb-2">Privacy Questions or Concerns?</h3>
                  <p className="text-gray-300 mb-4">
                    If you have any questions about this Privacy Policy or our data practices, please contact us:
                  </p>
                  <div className="space-y-2 text-gray-400">
                    <p>Email: <EMAIL></p>
                    <p>Phone: +****************</p>
                    <p>Address: 123 Innovation Drive, Tech City, TC 12345</p>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default LegalPrivacy
