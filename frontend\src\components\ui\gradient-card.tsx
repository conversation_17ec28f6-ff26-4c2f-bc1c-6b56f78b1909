'use client'
import React, { useRef, useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";

interface GradientCardProps {
  icon: React.ComponentType<{ className?: string; size?: number }>;
  title: string;
  description: string;
  features: string[];
  color: string;
  index: number;
  link?: string;
}

export const GradientCard: React.FC<GradientCardProps> = ({
  icon: Icon,
  title,
  description,
  features,
  color,
  index,
  link
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [rotation, setRotation] = useState({ x: 0, y: 0 });
  const [isThrottled, setIsThrottled] = useState(false);

  // Handle mouse movement for 3D effect - Throttled for performance
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardRef.current && isHovered && !isThrottled) {
      setIsThrottled(true);

      const rect = cardRef.current.getBoundingClientRect();

      // Calculate mouse position relative to card center
      const x = e.clientX - rect.left - rect.width / 2;
      const y = e.clientY - rect.top - rect.height / 2;

      setMousePosition({ x, y });

      // Calculate rotation (reduced range for better performance)
      const rotateX = -(y / rect.height) * 2; // Reduced from 5 to 2 degrees
      const rotateY = (x / rect.width) * 2; // Reduced from 5 to 2 degrees

      setRotation({ x: rotateX, y: rotateY });

      // Throttle updates to 60fps
      setTimeout(() => setIsThrottled(false), 16);
    }
  };

  // Reset rotation when not hovering
  const handleMouseLeave = () => {
    setIsHovered(false);
    setRotation({ x: 0, y: 0 });
  };

  const CardWrapper = link ? Link : 'div';
  const cardProps = link ? { to: link } : {};

  return (
    <CardWrapper {...cardProps} className={link ? "block" : ""}>
      <motion.div
        ref={cardRef}
        className="relative rounded-[32px] overflow-hidden w-full h-[450px] cursor-pointer gpu-accelerated gradient-card"
      style={{
        transformStyle: "preserve-3d",
        backgroundColor: "#0e131f",
        boxShadow: "0 -10px 100px 10px rgba(78, 99, 255, 0.25), 0 0 10px 0 rgba(0, 0, 0, 0.5)",
      }}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.08, ease: "easeOut" }}
      viewport={{ once: true, margin: "-50px" }}
      animate={{
        y: isHovered ? -3 : 0,
        // Reduced 3D rotations to minimize jitter during scroll
        rotateX: isHovered ? rotation.x * 0.5 : 0,
        rotateY: isHovered ? rotation.y * 0.5 : 0,
        perspective: 1000,
      }}
      whileHover={{
        transition: {
          type: "spring",
          stiffness: 200,
          damping: 25,
          mass: 0.8
        }
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      onMouseMove={handleMouseMove}
    >
      {/* Subtle glass reflection overlay - Simplified for performance */}
      <motion.div
        className="absolute inset-0 z-35 pointer-events-none"
        style={{
          background: "linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0) 40%, rgba(255,255,255,0) 80%, rgba(255,255,255,0.03) 100%)",
          // Removed backdropFilter to improve performance
        }}
        animate={{
          opacity: isHovered ? 0.6 : 0.4,
          // Removed rotation animations to reduce jitter
          z: 1,
        }}
        transition={{
          duration: 0.2,
          ease: "easeOut"
        }}
      />

      {/* Dark background with black gradient */}
      <motion.div
        className="absolute inset-0 z-0"
        style={{
          background: "linear-gradient(180deg, #000000 0%, #000000 70%)",
        }}
        animate={{
          z: -1
        }}
      />

      {/* Noise texture overlay */}
      <motion.div
        className="absolute inset-0 opacity-30 mix-blend-overlay z-10"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='5' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noise)'/%3E%3C/svg%3E")`,
        }}
        animate={{
          z: -0.5
        }}
      />

      {/* Purple/blue glow effect - Simplified for performance */}
      <motion.div
        className="absolute bottom-0 left-0 right-0 h-2/3 z-20"
        style={{
          background: `
            radial-gradient(ellipse at bottom right, rgba(172, 92, 255, 0.5) -10%, rgba(79, 70, 229, 0) 70%),
            radial-gradient(ellipse at bottom left, rgba(56, 189, 248, 0.5) -10%, rgba(79, 70, 229, 0) 70%)
          `,
          filter: "blur(20px)", // Reduced blur for better performance
        }}
        animate={{
          opacity: isHovered ? 0.7 : 0.6,
          // Removed y animation to reduce jitter during scroll
          z: 0
        }}
        transition={{
          duration: 0.2,
          ease: "easeOut"
        }}
      />

      {/* Central purple glow - Simplified for performance */}
      <motion.div
        className="absolute bottom-0 left-0 right-0 h-2/3 z-21"
        style={{
          background: `
            radial-gradient(circle at bottom center, rgba(161, 58, 229, 0.5) -20%, rgba(79, 70, 229, 0) 60%)
          `,
          filter: "blur(25px)", // Reduced blur
        }}
        animate={{
          opacity: isHovered ? 0.7 : 0.6,
          // Removed complex y animation to reduce jitter
          z: 0
        }}
        transition={{
          duration: 0.2,
          ease: "easeOut"
        }}
      />

      {/* Enhanced bottom border glow */}
      <motion.div
        className="absolute bottom-0 left-0 right-0 h-[2px] z-25"
        style={{
          background: "linear-gradient(90deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.7) 50%, rgba(255, 255, 255, 0.05) 100%)",
        }}
        animate={{
          boxShadow: isHovered
            ? "0 0 20px 4px rgba(172, 92, 255, 0.9), 0 0 30px 6px rgba(138, 58, 185, 0.7), 0 0 40px 8px rgba(56, 189, 248, 0.5)"
            : "0 0 15px 3px rgba(172, 92, 255, 0.8), 0 0 25px 5px rgba(138, 58, 185, 0.6), 0 0 35px 7px rgba(56, 189, 248, 0.4)",
          opacity: isHovered ? 1 : 0.9,
          z: 0.5
        }}
        transition={{
          duration: 0.4,
          ease: "easeOut"
        }}
      />

      {/* Card content */}
      <motion.div
        className="relative flex flex-col h-full p-8 z-40"
        animate={{
          z: 2
        }}
      >
        {/* Icon circle with shadow */}
        <motion.div
          className="w-12 h-12 rounded-full flex items-center justify-center mb-6"
          style={{
            background: "linear-gradient(225deg, #171c2c 0%, #121624 100%)",
            position: "relative",
            overflow: "hidden"
          }}
          initial={{ opacity: 0.8 }}
          animate={{
            opacity: 1,
            boxShadow: isHovered
              ? "0 6px 12px -2px rgba(0, 0, 0, 0.25), 0 3px 6px -1px rgba(0, 0, 0, 0.15), inset 1px 1px 3px rgba(255, 255, 255, 0.12), inset -2px -2px 4px rgba(0, 0, 0, 0.5)"
              : "0 4px 8px -2px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1), inset 1px 1px 2px rgba(255, 255, 255, 0.08), inset -1px -1px 3px rgba(0, 0, 0, 0.4)",
            z: isHovered ? 8 : 4,
            y: isHovered ? -1 : 0,
            rotateX: isHovered ? -rotation.x * 0.3 : 0,
            rotateY: isHovered ? -rotation.y * 0.3 : 0
          }}
          transition={{
            duration: 0.3,
            ease: "easeOut"
          }}
        >
          {/* Top-left highlight for realistic lighting */}
          <div
            className="absolute top-0 left-0 w-2/3 h-2/3 opacity-40"
            style={{
              background: "radial-gradient(circle at top left, rgba(255, 255, 255, 0.5), transparent 80%)",
              pointerEvents: "none",
              filter: "blur(10px)"
            }}
          />

          {/* Bottom shadow for depth */}
          <div
            className="absolute bottom-0 left-0 w-full h-1/2 opacity-50"
            style={{
              background: "linear-gradient(to top, rgba(0, 0, 0, 0.4), transparent)",
              pointerEvents: "none",
              backdropFilter: "blur(3px)"
            }}
          />

          {/* Service icon */}
          <div className="flex items-center justify-center w-full h-full relative z-10">
            <Icon className="text-white" size={20} />
          </div>
        </motion.div>

        {/* Content positioning */}
        <motion.div
          className="mb-auto"
          animate={{
            z: isHovered ? 5 : 2,
            rotateX: isHovered ? -rotation.x * 0.3 : 0,
            rotateY: isHovered ? -rotation.y * 0.3 : 0
          }}
          transition={{
            duration: 0.4,
            ease: "easeOut"
          }}
        >
          <motion.h3
            className="text-2xl font-medium text-white mb-3"
            style={{
              letterSpacing: "-0.01em",
              lineHeight: 1.2,
            }}
            initial={{ opacity: 0.8 }}
            animate={{
              textShadow: isHovered ? "0 1px 2px rgba(0,0,0,0.1)" : "none",
              opacity: 1,
              transition: { duration: 0.8, delay: 0.1 }
            }}
          >
            {title}
          </motion.h3>

          <motion.p
            className="text-sm mb-6 text-gray-300"
            style={{
              lineHeight: 1.5,
              fontWeight: 350,
            }}
            initial={{ opacity: 0.7 }}
            animate={{
              textShadow: isHovered ? "0 1px 2px rgba(0,0,0,0.05)" : "none",
              opacity: 0.85,
              transition: { duration: 0.8, delay: 0.2 }
            }}
          >
            {description}
          </motion.p>

          {/* Features list */}
          <div className="space-y-2 mb-6">
            {features.map((feature, featureIndex) => (
              <motion.div 
                key={featureIndex} 
                className="flex items-center space-x-2"
                initial={{ opacity: 0, x: -5 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.3 + featureIndex * 0.05 }}
              >
                <div className="w-1 h-1 bg-primary-400 rounded-full"></div>
                <span className="text-gray-300 text-xs">{feature}</span>
              </motion.div>
            ))}
          </div>

          {/* Learn More with arrow */}
          <motion.div
            className="inline-flex items-center text-white text-sm font-medium group"
            initial={{ opacity: 0.8 }}
            animate={{
              opacity: 0.9,
              transition: { duration: 0.8, delay: 0.3 }
            }}
            whileHover={{
              filter: "drop-shadow(0 0 5px rgba(255, 255, 255, 0.5))"
            }}
          >
            Learn More
            <motion.svg
              className="ml-1 w-4 h-4"
              width="8"
              height="8"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              animate={{
                x: isHovered ? 4 : 0
              }}
              transition={{
                duration: 0.6,
                ease: "easeOut"
              }}
            >
              <path
                d="M1 8H15M15 8L8 1M15 8L8 15"
                stroke="white"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </motion.svg>
          </motion.div>
        </motion.div>
      </motion.div>
    </motion.div>
    </CardWrapper>
  );
};
