import React, { useState } from "react";
import { HoveredLink, Menu, MenuItem, ProductItem } from "./navbar-menu";
import { cn } from "@/lib/utils";

export function NavbarDemo() {
  return (
    <div className="relative w-full flex items-center justify-center">
      <Navbar className="top-2" />
    </div>
  );
}

function Navbar({ className }: { className?: string }) {
  const [active, setActive] = useState<string | null>(null);
  return (
    <div
      className={cn("fixed top-10 inset-x-0 max-w-2xl mx-auto z-50", className)}
    >
      <Menu setActive={setActive}>
        <MenuItem setActive={setActive} active={active} item="Services">
          <div className="flex flex-col space-y-4 text-sm">
            <HoveredLink to="/services">Web Development</HoveredLink>
            <HoveredLink to="/services">Mobile Development</HoveredLink>
            <HoveredLink to="/services">UI/UX Design</HoveredLink>
            <HoveredLink to="/services">Digital Marketing</HoveredLink>
          </div>
        </MenuItem>
        <MenuItem setActive={setActive} active={active} item="Portfolio">
          <div className="text-sm grid grid-cols-2 gap-10 p-4">
            <ProductItem
              title="E-commerce Platform"
              href="/portfolio"
              src="https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=400&h=200&fit=crop"
              description="Modern e-commerce solution with advanced features"
            />
            <ProductItem
              title="Mobile Banking App"
              href="/portfolio"
              src="https://images.unsplash.com/photo-*************-90a1b58e7e9c?w=400&h=200&fit=crop"
              description="Secure and user-friendly mobile banking application"
            />
            <ProductItem
              title="SaaS Dashboard"
              href="/portfolio"
              src="https://images.unsplash.com/photo-**********-bebda4e38f71?w=400&h=200&fit=crop"
              description="Analytics dashboard for business intelligence"
            />
            <ProductItem
              title="Healthcare Portal"
              href="/portfolio"
              src="https://images.unsplash.com/photo-*************-112ba8d25d1f?w=400&h=200&fit=crop"
              description="Patient management and telemedicine platform"
            />
          </div>
        </MenuItem>
        <MenuItem setActive={setActive} active={active} item="Company">
          <div className="flex flex-col space-y-4 text-sm">
            <HoveredLink to="/about">About Us</HoveredLink>
            <HoveredLink to="/jobs">Careers</HoveredLink>
            <HoveredLink to="/blog">Blog</HoveredLink>
            <HoveredLink to="/contact">Get Quote</HoveredLink>
          </div>
        </MenuItem>
      </Menu>
    </div>
  );
}

export default Navbar;
