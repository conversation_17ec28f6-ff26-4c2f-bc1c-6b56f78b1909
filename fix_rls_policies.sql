-- Fix RLS Policies for Job and Internship Applications
-- Run this in your Supabase SQL Editor

-- First, drop existing policies that might be causing conflicts
DROP POLICY IF EXISTS "Anyone can insert job applications" ON job_applications;
DROP POLICY IF EXISTS "Users can view own job applications" ON job_applications;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all job applications" ON job_applications;
DROP POLICY IF EXISTS "Ad<PERSON> can update job applications" ON job_applications;

DROP POLICY IF EXISTS "Anyone can insert internship applications" ON internship_applications;
DROP POLICY IF EXISTS "Users can view own internship applications" ON internship_applications;
DROP POLICY IF EXISTS "Ad<PERSON> can view all internship applications" ON internship_applications;
DROP POLICY IF EXISTS "Ad<PERSON> can update internship applications" ON internship_applications;

DROP POLICY IF EXISTS "Ad<PERSON> can manage references" ON application_references;

-- Create new policies that allow anonymous submissions

-- Job Applications Policies
CREATE POLICY "Enable insert for anonymous users" ON job_applications
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable read for application owners" ON job_applications
    FOR SELECT USING (
        email = auth.email() OR 
        auth.uid() IS NULL OR
        EXISTS (SELECT 1 FROM auth.users WHERE auth.users.id = auth.uid() AND auth.users.email = job_applications.email)
    );

CREATE POLICY "Enable read for admins" ON job_applications
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'hr')
        )
    );

CREATE POLICY "Enable update for admins" ON job_applications
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'hr')
        )
    );

-- Internship Applications Policies
CREATE POLICY "Enable insert for anonymous users" ON internship_applications
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable read for application owners" ON internship_applications
    FOR SELECT USING (
        email = auth.email() OR 
        auth.uid() IS NULL OR
        EXISTS (SELECT 1 FROM auth.users WHERE auth.users.id = auth.uid() AND auth.users.email = internship_applications.email)
    );

CREATE POLICY "Enable read for admins" ON internship_applications
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'hr')
        )
    );

CREATE POLICY "Enable update for admins" ON internship_applications
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'hr')
        )
    );

-- Application References Policies
CREATE POLICY "Enable insert for anonymous users" ON application_references
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable read for admins" ON application_references
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'hr')
        )
    );

-- Ensure RLS is enabled on all tables
ALTER TABLE job_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE internship_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE application_references ENABLE ROW LEVEL SECURITY;

-- Optional: Disable RLS temporarily for testing (NOT recommended for production)
-- ALTER TABLE job_applications DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE internship_applications DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE application_references DISABLE ROW LEVEL SECURITY;

-- Check current policies (for verification)
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename IN ('job_applications', 'internship_applications', 'application_references')
ORDER BY tablename, policyname;
