"use client";

import DisplayCards from "@/components/ui/display-cards";
import { Code, Smartphone, Palette } from "lucide-react";

const deltaXeroCards = [
  {
    icon: <Code className="size-4 text-primary-300" />,
    title: "Web Development",
    description: "Modern, scalable applications",
    date: "Our Specialty",
    iconClassName: "text-primary-500",
    titleClassName: "text-primary-500",
    className:
      "[grid-area:stack] hover:-translate-y-10 before:absolute before:w-[100%] before:outline-1 before:rounded-xl before:outline-gray-700 before:h-[100%] before:content-[''] before:bg-blend-overlay before:bg-dark-900/50 grayscale-[100%] hover:before:opacity-0 before:transition-opacity before:duration-700 hover:grayscale-0 before:left-0 before:top-0",
  },
  {
    icon: <Smartphone className="size-4 text-primary-300" />,
    title: "Mobile Apps",
    description: "Cross-platform solutions",
    date: "iOS & Android",
    iconClassName: "text-primary-500",
    titleClassName: "text-primary-500",
    className:
      "[grid-area:stack] translate-x-12 translate-y-10 hover:-translate-y-1 before:absolute before:w-[100%] before:outline-1 before:rounded-xl before:outline-gray-700 before:h-[100%] before:content-[''] before:bg-blend-overlay before:bg-dark-900/50 grayscale-[100%] hover:before:opacity-0 before:transition-opacity before:duration-700 hover:grayscale-0 before:left-0 before:top-0",
  },
  {
    icon: <Palette className="size-4 text-primary-300" />,
    title: "UI/UX Design",
    description: "Beautiful user experiences",
    date: "Creative Excellence",
    iconClassName: "text-primary-500",
    titleClassName: "text-primary-500",
    className:
      "[grid-area:stack] translate-x-24 translate-y-20 hover:translate-y-10",
  },
];

function DisplayCardsDemo() {
  return (
    <div className="flex min-h-[400px] w-full items-center justify-center py-20">
      <div className="w-full max-w-3xl">
        <DisplayCards cards={deltaXeroCards} />
      </div>
    </div>
  );
}

export { DisplayCardsDemo };
