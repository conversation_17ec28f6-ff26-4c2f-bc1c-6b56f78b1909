import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import FeaturesBentoGrid from '../components/ui/features-bento-grid'
import { 
  Building2, 
  Shield, 
  Users, 
  Globe, 
  TrendingUp, 
  Zap, 
  ArrowRight, 
  CheckCircle, 
  Star,
  CreditCard,
  Smartphone,
  BarChart3,
  Lock,
  Banknote,
  Wallet,
  PieChart,
  Database
} from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const BankingPage = () => {
  const statsRef = useRef(null)
  const heroRef = useRef(null)

  useEffect(() => {
    // Hero animation
    gsap.fromTo(heroRef.current?.children,
      { y: 100, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1.2,
        stagger: 0.2,
        ease: 'power3.out'
      }
    )

    // Stats counter animation
    gsap.fromTo('.stat-counter',
      { textContent: 0 },
      {
        textContent: (i, target) => target.getAttribute('data-value'),
        duration: 2,
        ease: 'power2.out',
        snap: { textContent: 1 },
        scrollTrigger: {
          trigger: statsRef.current,
          start: 'top 80%',
        }
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const features = [
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Multi-layer security with advanced fraud detection and regulatory compliance',
      color: 'from-indigo-500 to-blue-500'
    },
    {
      icon: Database,
      title: 'Core Banking System',
      description: 'Comprehensive core banking platform with real-time transaction processing',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Smartphone,
      title: 'Digital Banking',
      description: 'Modern mobile and web banking with intuitive user experience',
      color: 'from-emerald-500 to-teal-500'
    },
    {
      icon: BarChart3,
      title: 'Risk Management',
      description: 'Advanced risk assessment and compliance monitoring systems',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: CreditCard,
      title: 'Payment Solutions',
      description: 'Integrated payment processing with multiple gateway support',
      color: 'from-orange-500 to-red-500'
    },
    {
      icon: TrendingUp,
      title: 'Analytics Platform',
      description: 'Business intelligence and predictive analytics for banking operations',
      color: 'from-yellow-500 to-orange-500'
    }
  ]

  const solutions = [
    {
      title: 'Digital Banking Platforms',
      description: 'Complete digital transformation solutions for traditional and neo banks',
      features: ['Account management', 'Transaction processing', 'Customer onboarding', 'Regulatory reporting'],
      image: 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=600&h=400&fit=crop',
      color: 'from-indigo-500 to-blue-500'
    },
    {
      title: 'Core Banking Systems',
      description: 'Modern core banking infrastructure with real-time processing capabilities',
      features: ['Real-time processing', 'Multi-currency support', 'API-first architecture', 'Scalable infrastructure'],
      image: 'https://images.unsplash.com/photo-**********-824ae1b704d3?w=600&h=400&fit=crop',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      title: 'Wealth Management',
      description: 'Sophisticated wealth management platforms for high-net-worth clients',
      features: ['Portfolio management', 'Investment advisory', 'Risk assessment', 'Client reporting'],
      image: 'https://images.unsplash.com/photo-*************-9c2a0a7236a3?w=600&h=400&fit=crop',
      color: 'from-emerald-500 to-teal-500'
    }
  ]

  const caseStudies = [
    {
      title: 'Regional Bank',
      subtitle: '$10B+ Assets',
      description: 'Complete digital transformation for a regional bank with $10B+ in assets',
      metrics: { assets: '$10B+', customers: '1M+', efficiency: '50%' },
      image: 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=400&h=300&fit=crop',
      color: 'from-indigo-500 to-blue-500'
    },
    {
      title: 'Neo Bank',
      subtitle: '2M+ Customers',
      description: 'Built a complete digital-only bank from the ground up',
      metrics: { customers: '2M+', transactions: '100M+', satisfaction: '98%' },
      image: 'https://images.unsplash.com/photo-**********-824ae1b704d3?w=400&h=300&fit=crop',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      title: 'Investment Platform',
      subtitle: '$5B+ Managed',
      description: 'Wealth management platform managing billions in client assets',
      metrics: { managed: '$5B+', clients: '50K+', performance: '12%' },
      image: 'https://images.unsplash.com/photo-*************-9c2a0a7236a3?w=400&h=300&fit=crop',
      color: 'from-emerald-500 to-green-500'
    }
  ]

  const stats = [
    { number: 15, label: 'Banking Projects', suffix: '+' },
    { number: 99, label: 'Security Compliance', suffix: '.99%' },
    { number: 10, label: 'Billion Processed', suffix: 'B+' },
    { number: 24, label: 'Support Available', suffix: '/7' }
  ]

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen -mt-16 lg:-mt-20"
    >
      {/* Hero Section */}
      <section className="relative pt-24 pb-20 min-h-screen flex items-center justify-center bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-indigo-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-primary-500/5 to-indigo-500/5 rounded-full blur-3xl"></div>
        </div>

        {/* Animated Grid Background */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.3) 1px, transparent 0)`,
            backgroundSize: '50px 50px'
          }}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div ref={heroRef}>
            {/* Badge */}
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-indigo-500/10 to-blue-500/10 border border-indigo-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Building2 className="text-indigo-400 mr-3" size={20} />
              <span className="text-indigo-400 text-sm font-semibold uppercase tracking-wider">Banking Excellence</span>
            </motion.div>

            {/* Main Heading */}
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-black text-white mb-6 leading-tight">
              DIGITAL
              <br />
              <span className="bg-gradient-to-r from-indigo-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent">
                BANKING
              </span>
              <br />
              SOLUTIONS
            </h1>

            {/* Subtitle */}
            <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto mb-10 leading-relaxed">
              Transform traditional banking with secure, scalable digital solutions that deliver
              exceptional customer experiences and operational efficiency
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Link to="/get-quote">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gradient-to-r from-indigo-500 to-blue-500 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-2xl transition-all duration-300 flex items-center space-x-3 relative overflow-hidden group"
                >
                  <span className="relative z-10">Start Your Project</span>
                  <ArrowRight className="relative z-10" size={20} />
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                </motion.button>
              </Link>

              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white/10 backdrop-blur-md border border-white/20 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-white/20 transition-all duration-300 flex items-center space-x-3"
              >
                <span>View Case Studies</span>
                <Star size={20} />
              </motion.button>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/60 rounded-full mt-2"></div>
          </div>
        </motion.div>
      </section>

      {/* Stats Section */}
      <section ref={statsRef} className="py-20 bg-gradient-to-b from-dark-950 to-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-xl border border-white/10 p-8 rounded-3xl hover:border-white/20 transition-all duration-500 group">
                  <div className="text-4xl md:text-5xl font-black text-white mb-2">
                    <span className="stat-counter" data-value={stat.number}>0</span>
                    <span className="text-indigo-400">{stat.suffix}</span>
                  </div>
                  <div className="text-gray-400 font-medium">{stat.label}</div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-dark-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-indigo-500/10 to-blue-500/10 border border-indigo-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Lock className="text-indigo-400 mr-3" size={16} />
              <span className="text-indigo-400 text-sm font-semibold uppercase tracking-wider">Banking Features</span>
            </motion.div>
            <h2 className="text-5xl md:text-6xl font-black text-white mb-8 leading-tight">
              ENTERPRISE-GRADE
              <br />
              <span className="bg-gradient-to-r from-indigo-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent">
                BANKING TECH
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
              Built for security, compliance, and scale with modern banking technology standards
            </p>
          </motion.div>

          <FeaturesBentoGrid features={features} />
        </div>
      </section>

      {/* Solutions Section */}
      <section className="py-20 bg-gradient-to-b from-dark-950 to-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-blue-500/10 to-emerald-500/10 border border-blue-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Banknote className="text-blue-400 mr-3" size={16} />
              <span className="text-blue-400 text-sm font-semibold uppercase tracking-wider">Banking Solutions</span>
            </motion.div>
            <h2 className="text-5xl md:text-6xl font-black text-white mb-8 leading-tight">
              COMPREHENSIVE
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-emerald-400 to-teal-400 bg-clip-text text-transparent">
                BANKING PLATFORMS
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
              From digital banking to wealth management, we build the future of financial services
            </p>
          </motion.div>

          <div className="space-y-24">
            {solutions.map((solution, index) => {
              const isEven = index % 2 === 0
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className={`grid grid-cols-1 lg:grid-cols-2 gap-16 items-center ${!isEven ? 'lg:grid-flow-col-dense' : ''}`}
                >
                  {/* Content */}
                  <div className={isEven ? 'lg:pr-8' : 'lg:pl-8 lg:col-start-2'}>
                    <h3 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
                      {solution.title}
                    </h3>
                    <p className="text-gray-300 text-xl mb-8 leading-relaxed">
                      {solution.description}
                    </p>

                    {/* Features */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                      {solution.features.map((feature, featureIndex) => (
                        <motion.div
                          key={featureIndex}
                          className="flex items-center space-x-3 group"
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: featureIndex * 0.1 }}
                          viewport={{ once: true }}
                        >
                          <div className={`w-2 h-2 bg-gradient-to-r ${solution.color} rounded-full group-hover:scale-125 transition-transform duration-300`}></div>
                          <span className="text-gray-300 group-hover:text-white transition-colors duration-300">{feature}</span>
                        </motion.div>
                      ))}
                    </div>


                  </div>

                  {/* Image */}
                  <div className={isEven ? 'lg:pl-8' : 'lg:pr-8 lg:col-start-1 lg:row-start-1'}>
                    <div className="relative group">
                      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-black/60 to-black/30 backdrop-blur-xl border border-white/20 group-hover:border-white/30 transition-all duration-500">
                        <img
                          src={solution.image}
                          alt={solution.title}
                          className="w-full h-96 object-cover group-hover:scale-110 transition-transform duration-700"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/30 to-transparent"></div>

                        {/* Overlay Content */}
                        <div className="absolute bottom-6 left-6 right-6">
                          <h4 className="text-white font-bold text-xl mb-2">{solution.title}</h4>
                          <p className="text-gray-300 text-sm">{solution.description}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>



      {/* CTA Section */}
      <section className="py-32 bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-indigo-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-flex items-center bg-gradient-to-r from-indigo-500/10 to-blue-500/10 border border-indigo-500/20 rounded-full px-6 py-3 mb-8"
              whileHover={{ scale: 1.05 }}
            >
              <Building2 className="text-indigo-400 mr-3" size={16} />
              <span className="text-indigo-400 text-sm font-semibold uppercase tracking-wider">Transform Banking</span>
            </motion.div>

            <h2 className="text-5xl md:text-6xl font-black text-white mb-8 leading-tight">
              FUTURE OF
              <br />
              <span className="bg-gradient-to-r from-indigo-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent">
                BANKING
              </span>
            </h2>

            <p className="text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed">
              Join leading financial institutions in delivering next-generation banking experiences
            </p>

            <div className="flex justify-center">
              <Link to="/get-quote">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gradient-to-r from-indigo-500 to-blue-500 text-white px-12 py-5 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-300 flex items-center space-x-3 relative overflow-hidden group"
                >
                  <span className="relative z-10">Schedule Consultation</span>
                  <ArrowRight className="relative z-10" size={20} />
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                </motion.button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </motion.div>
  )
}

export default BankingPage
