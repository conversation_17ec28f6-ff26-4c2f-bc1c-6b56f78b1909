import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface BentoGridProps {
  children: React.ReactNode
  className?: string
}

interface BentoCardProps {
  icon?: React.ReactNode
  title: string
  description: string
  color?: string
  className?: string
  size?: 'small' | 'medium' | 'large' | 'wide' | 'tall'
  children?: React.ReactNode
  index?: number
}

const BentoGrid: React.FC<BentoGridProps> = ({ children, className }) => {
  return (
    <div className={cn(
      "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 auto-rows-[200px] max-w-7xl mx-auto",
      className
    )}>
      {children}
    </div>
  )
}

const BentoCard: React.FC<BentoCardProps> = ({
  icon,
  title,
  description,
  color = 'from-primary-500 to-primary-600',
  className,
  size = 'medium',
  children,
  index = 0
}) => {
  const sizeClasses = {
    small: 'col-span-1 row-span-1',
    medium: 'col-span-1 md:col-span-1 row-span-1',
    large: 'col-span-1 md:col-span-2 row-span-2',
    wide: 'col-span-1 md:col-span-2 row-span-1',
    tall: 'col-span-1 row-span-2'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      className={cn(
        sizeClasses[size],
        "group relative overflow-hidden rounded-3xl border border-white/10 bg-gradient-to-br from-dark-800/50 to-dark-900/50 backdrop-blur-xl p-6 hover:border-white/20 transition-all duration-500",
        className
      )}
    >
      {/* Background Gradient */}
      <div className={cn(
        "absolute inset-0 bg-gradient-to-br opacity-5 group-hover:opacity-10 transition-opacity duration-500",
        color
      )} />
      
      {/* Animated Background Pattern */}
      <div className="absolute inset-0 opacity-[0.02] group-hover:opacity-[0.05] transition-opacity duration-500">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]" />
      </div>

      {/* Content */}
      <div className="relative z-10 h-full flex flex-col">
        {icon && (
          <div className={cn(
            "w-12 h-12 rounded-2xl bg-gradient-to-br flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",
            color
          )}>
            <div className="text-white">
              {icon}
            </div>
          </div>
        )}
        
        <h3 className="text-white font-bold text-lg mb-2 group-hover:text-white/90 transition-colors duration-300">
          {title}
        </h3>
        
        <p className="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors duration-300 flex-1">
          {description}
        </p>

        {children && (
          <div className="mt-4">
            {children}
          </div>
        )}
      </div>

      {/* Hover Effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      
      {/* Border Glow */}
      <div className={cn(
        "absolute inset-0 rounded-3xl bg-gradient-to-br opacity-0 group-hover:opacity-20 transition-opacity duration-500 blur-xl",
        color
      )} />
    </motion.div>
  )
}

// Specialized Bento Cards for different content types
const BentoFeatureCard: React.FC<BentoCardProps & { features?: string[] }> = ({
  features,
  ...props
}) => {
  return (
    <BentoCard {...props}>
      {features && (
        <div className="space-y-2">
          {features.slice(0, 3).map((feature, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div className={cn(
                "w-1.5 h-1.5 rounded-full bg-gradient-to-r",
                props.color || 'from-primary-500 to-primary-600'
              )} />
              <span className="text-xs text-gray-400">{feature}</span>
            </div>
          ))}
        </div>
      )}
    </BentoCard>
  )
}

const BentoMetricCard: React.FC<BentoCardProps & { metric?: string; label?: string }> = ({
  metric,
  label,
  ...props
}) => {
  return (
    <BentoCard {...props} size="small">
      <div className="text-center">
        {metric && (
          <div className={cn(
            "text-2xl font-black bg-gradient-to-r bg-clip-text text-transparent mb-1",
            props.color || 'from-primary-400 to-primary-600'
          )}>
            {metric}
          </div>
        )}
        {label && (
          <div className="text-xs text-gray-400 uppercase tracking-wider">
            {label}
          </div>
        )}
      </div>
    </BentoCard>
  )
}

export { BentoGrid, BentoCard, BentoFeatureCard, BentoMetricCard }
