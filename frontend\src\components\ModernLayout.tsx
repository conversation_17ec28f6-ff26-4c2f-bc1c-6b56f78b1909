import React from 'react';
import ModernNavbar from './ModernNavbar';
import { DeltaXeroFooter } from './DeltaXeroFooter';
import ScrollToTop from './ScrollToTop';
import { useSmoothScroll } from '../hooks/useSmoothScroll';

interface ModernLayoutProps {
  children: React.ReactNode;
}

const ModernLayout: React.FC<ModernLayoutProps> = ({ children }) => {
  // Initialize Lenis smooth scroll
  const { isReady } = useSmoothScroll();

  return (
    <div className="min-h-screen bg-dark-950">
      {/* Modern Navigation */}
      <ModernNavbar />

      {/* Main Content */}
      <main className={`pt-16 md:pt-20 transition-opacity duration-300 ${isReady ? 'opacity-100' : 'opacity-95'}`}>
        {children}
      </main>

      {/* Footer */}
      <DeltaXeroFooter />

      {/* Scroll to Top Component */}
      <ScrollToTop />
    </div>
  );
};

export default ModernLayout;
